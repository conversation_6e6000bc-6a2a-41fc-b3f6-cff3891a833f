package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.model.User;
import com.mycompany.quanlydoituongdacbiet.service.UserService;
import com.mycompany.quanlydoituongdacbiet.view.LoginManagementView;
import com.mycompany.quanlydoituongdacbiet.view.RegisterView;
import com.mycompany.quanlydoituongdacbiet.controller.RegisterController;
import com.mycompany.quanlydoituongdacbiet.view.ThiSinhManagementView;
import com.mycompany.quanlydoituongdacbiet.view.MonThiManagementView;
import com.mycompany.quanlydoituongdacbiet.view.KhoiThiManagementView;
import com.mycompany.quanlydoituongdacbiet.view.DiemThiManagementView;
import com.mycompany.quanlydoituongdacbiet.view.TraCuuDiemView;
import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Controller cho Login Management
 */
public class LoginController {
    
    private LoginManagementView view;
    private UserService userService;
    private User currentUser;
    
    public LoginController(LoginManagementView view) {
        this.view = view;
        this.userService = UserService.getInstance();
        
        initEventListeners();
    }
    
    /**
     * Khởi tạo event listeners
     */
    private void initEventListeners() {
        view.addLoginListener(new LoginListener());
        view.addRegisterListener(new RegisterListener());
        view.addAboutListener(new AboutListener());
        view.addExitListener(new ExitListener());
    }
    
    /**
     * Listener cho nút Login
     */
    private class LoginListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (!view.validateInput()) {
                return;
            }
            
            String username = view.getUsername();
            String password = view.getPassword();
            
            try {
                User user = userService.login(username, password);
                
                if (user != null) {
                    currentUser = user;
                    view.showSuccess("Đăng nhập thành công! Chào mừng " + user.getFullName());
                    
                    // Hiển thị main menu
                    showMainMenu();
                    
                } else {
                    view.showError("Tên đăng nhập hoặc mật khẩu không đúng!");
                    view.getTxtPassword().setText("");
                    view.getTxtPassword().requestFocus();
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi đăng nhập: " + ex.getMessage());
            }
        }
    }

    /**
     * Listener cho nút Register
     */
    private class RegisterListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                // Ẩn login view
                view.setVisible(false);

                // Tạo và hiển thị register view
                RegisterView registerView = new RegisterView();
                RegisterController registerController = new RegisterController(registerView, view);
                registerView.setVisible(true);

                view.updateStatus("Đã mở form đăng ký");

            } catch (Exception ex) {
                view.showError("Lỗi khi mở form đăng ký: " + ex.getMessage());
                view.setVisible(true); // Hiển thị lại login view nếu có lỗi
            }
        }
    }

    /**
     * Listener cho nút About
     */
    private class AboutListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String aboutMessage = 
                "HỆ THỐNG QUẢN LÝ ĐIỂM THI ĐẠI HỌC\n\n" +
                "Phiên bản: 2.0\n" +
                "Thiết kế: Modern UI với màu sắc #0071F0\n" +
                "Kiến trúc: MVC Pattern\n" +
                "Lưu trữ: File-based với UTF-8 encoding\n\n" +
                "Tính năng:\n" +
                "• Đăng ký và đăng nhập tài khoản\n" +
                "• Quản lý thí sinh và điểm thi\n" +
                "• Giao diện đẹp, thân thiện\n" +
                "• Bảo mật và ổn định\n\n" +
                "Tài khoản mặc định:\n" +
                "Username: admin\n" +
                "Password: admin";
                
            JOptionPane.showMessageDialog(view, 
                aboutMessage, 
                "Thông tin hệ thống", 
                JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    /**
     * Listener cho nút Exit
     */
    private class ExitListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int option = JOptionPane.showConfirmDialog(view,
                "Bạn có chắc chắn muốn thoát khỏi hệ thống?",
                "Xác nhận thoát",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE);
                
            if (option == JOptionPane.YES_OPTION) {
                System.exit(0);
            }
        }
    }
    
    /**
     * Hiển thị main menu sau khi đăng nhập thành công
     */
    private void showMainMenu() {
        view.showMainMenu();
        
        // Thêm event listeners cho main menu
        addMainMenuListeners();
    }
    
    /**
     * Thêm event listeners cho main menu
     */
    private void addMainMenuListeners() {
        // Tìm các button trong main menu và thêm listeners
        SwingUtilities.invokeLater(() -> {
            addMenuButtonListeners();
        });
    }
    
    /**
     * Thêm listeners cho menu buttons
     */
    private void addMenuButtonListeners() {
        // Thêm listeners cho các management buttons
        view.addThiSinhManagementListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openThiSinhManagement();
            }
        });

        view.addMonThiManagementListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openMonThiManagement();
            }
        });

        view.addKhoiThiManagementListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openKhoiThiManagement();
            }
        });

        view.addDiemThiManagementListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openDiemThiManagement();
            }
        });

        view.addTraCuuDiemListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openTraCuuDiem();
            }
        });

        view.addLogoutListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                logout();
            }
        });
    }
    
    /**
     * Tìm button theo text và thêm listener
     */
    private void findAndAddListener(String buttonText, ActionListener listener) {
        findButton(view.getContentPane(), buttonText, listener);
    }
    
    /**
     * Recursive method để tìm button
     */
    private void findButton(java.awt.Container container, String buttonText, ActionListener listener) {
        for (java.awt.Component component : container.getComponents()) {
            if (component instanceof JButton) {
                JButton button = (JButton) component;
                if (buttonText.equals(button.getText())) {
                    button.addActionListener(listener);
                    return;
                }
            } else if (component instanceof java.awt.Container) {
                findButton((java.awt.Container) component, buttonText, listener);
            }
        }
    }
    

    /**
     * Mở ThiSinh Management
     */
    private void openThiSinhManagement() {
        try {
            ThiSinhManagementView thiSinhView = new ThiSinhManagementView();
            ThiSinhController thiSinhController = new ThiSinhController(thiSinhView);
            thiSinhView.setVisible(true);

            view.updateStatus("Đã mở quản lý thí sinh");

        } catch (Exception ex) {
            view.showError("Lỗi khi mở quản lý thí sinh: " + ex.getMessage());
        }
    }

    /**
     * Mở quản lý môn thi
     */
    private void openMonThiManagement() {
        try {
            MonThiManagementView monThiView = new MonThiManagementView();
            MonThiController monThiController = new MonThiController(monThiView);
            monThiController.showView();

            view.updateStatus("Đã mở quản lý môn thi");

        } catch (Exception ex) {
            view.showError("Lỗi khi mở quản lý môn thi: " + ex.getMessage());
        }
    }

    /**
     * Mở quản lý khối thi
     */
    private void openKhoiThiManagement() {
        try {
            KhoiThiManagementView khoiThiView = new KhoiThiManagementView();
            KhoiThiController khoiThiController = new KhoiThiController(khoiThiView);
            khoiThiController.showView();

            view.updateStatus("Đã mở quản lý khối thi");

        } catch (Exception ex) {
            view.showError("Lỗi khi mở quản lý khối thi: " + ex.getMessage());
        }
    }

    /**
     * Mở quản lý điểm thi
     */
    private void openDiemThiManagement() {
        try {
            DiemThiManagementView diemThiView = new DiemThiManagementView();
            DiemThiController diemThiController = new DiemThiController(diemThiView);
            diemThiController.showView();

            view.updateStatus("Đã mở quản lý điểm thi");

        } catch (Exception ex) {
            view.showError("Lỗi khi mở quản lý điểm thi: " + ex.getMessage());
        }
    }

    /**
     * Mở tra cứu điểm thi
     */
    private void openTraCuuDiem() {
        try {
            TraCuuDiemView traCuuView = new TraCuuDiemView();
            TraCuuDiemController traCuuController = new TraCuuDiemController(traCuuView, true);
            traCuuController.showView();

            view.updateStatus("Đã mở tra cứu điểm thi");

        } catch (Exception ex) {
            view.showError("Lỗi khi mở tra cứu điểm thi: " + ex.getMessage());
        }
    }
    
    /**
     * Đăng xuất
     */
    private void logout() {
        int option = JOptionPane.showConfirmDialog(view,
            "Bạn có chắc chắn muốn đăng xuất?",
            "Xác nhận đăng xuất",
            JOptionPane.YES_NO_OPTION);
            
        if (option == JOptionPane.YES_OPTION) {
            currentUser = null;
            view.showLoginForm();
            view.clearForm();
            view.updateStatus("Đã đăng xuất thành công");
        }
    }
    
    /**
     * Lấy current user
     */
    public User getCurrentUser() {
        return currentUser;
    }
    
    /**
     * Kiểm tra user có phải admin không
     */
    public boolean isCurrentUserAdmin() {
        return currentUser != null && currentUser.isAdmin();
    }
}
