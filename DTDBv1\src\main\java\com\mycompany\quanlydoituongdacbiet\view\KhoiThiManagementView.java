package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.KhoiThi;
import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.List;

/**
 * Màn hình quản lý khối thi
 */
public class KhoiThiManagementView extends JFrame {
    
    // Components cho form nhập liệu
    private JTextField txtMaKhoi, txtTenKhoi, txtMoTa;
    private JList<String> listAvailableMon, listSelectedMon;
    private DefaultListModel<String> availableMonModel, selectedMonModel;
    private JButton btnAddMon, btnRemoveMon;
    
    // Components cho bảng dữ liệu
    private JTable tableKhoiThi;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    
    // Components cho tìm kiếm
    private JTextField txtTimKiem;
    private JButton btnTimKiem, btnLamMoi;
    
    // Components cho thao tác
    private JButton btnThem, btnSua, btnXoa, btnLuu, btnHuy;
    
    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingMaKhoi = null;
    
    // Colors
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240); // #0071F0
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69);
    private static final Color DANGER_COLOR = new Color(220, 53, 69);
    private static final Color WARNING_COLOR = new Color(255, 193, 7);
    
    public KhoiThiManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupEvents();
        setupWindow();
        resetForm();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components
        txtMaKhoi = new JTextField(15);
        txtTenKhoi = new JTextField(25);
        txtMoTa = new JTextField(30);
        
        // List models và lists cho môn thi
        availableMonModel = new DefaultListModel<>();
        selectedMonModel = new DefaultListModel<>();
        listAvailableMon = new JList<>(availableMonModel);
        listSelectedMon = new JList<>(selectedMonModel);
        
        listAvailableMon.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        listSelectedMon.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        
        // Buttons cho quản lý môn thi
        btnAddMon = new JButton("→");
        btnRemoveMon = new JButton("←");
        
        // Search components
        txtTimKiem = new JTextField(20);
        btnTimKiem = new JButton("Tìm kiếm");
        btnLamMoi = new JButton("Làm mới");
        
        // Action buttons
        btnThem = new JButton("Thêm khối thi");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        btnLuu = new JButton("Lưu");
        btnHuy = new JButton("Hủy");
        
        // Status
        lblStatus = new JLabel("Sẵn sàng");
        
        // Table
        String[] columnNames = {"Mã khối", "Tên khối", "Số môn thi", "Mô tả"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho edit trực tiếp trên table
            }
        };
        tableKhoiThi = new JTable(tableModel);
        scrollPane = new JScrollPane(tableKhoiThi);
        
        // Style components
        styleComponents();
    }
    
    /**
     * Style các components
     */
    private void styleComponents() {
        // Style buttons
        btnThem.setBackground(SUCCESS_COLOR);
        btnThem.setForeground(Color.WHITE);
        btnThem.setFont(new Font("Arial", Font.BOLD, 14));
        btnThem.setPreferredSize(new Dimension(150, 40));
        
        btnSua.setBackground(WARNING_COLOR);
        btnSua.setForeground(Color.BLACK);
        btnSua.setFont(new Font("Arial", Font.BOLD, 14));
        btnSua.setPreferredSize(new Dimension(100, 40));
        
        btnXoa.setBackground(DANGER_COLOR);
        btnXoa.setForeground(Color.WHITE);
        btnXoa.setFont(new Font("Arial", Font.BOLD, 14));
        btnXoa.setPreferredSize(new Dimension(100, 40));
        
        btnLuu.setBackground(PRIMARY_COLOR);
        btnLuu.setForeground(Color.WHITE);
        btnLuu.setFont(new Font("Arial", Font.BOLD, 14));
        btnLuu.setPreferredSize(new Dimension(100, 40));
        
        btnHuy.setBackground(Color.GRAY);
        btnHuy.setForeground(Color.WHITE);
        btnHuy.setFont(new Font("Arial", Font.BOLD, 14));
        btnHuy.setPreferredSize(new Dimension(100, 40));
        
        btnTimKiem.setBackground(PRIMARY_COLOR);
        btnTimKiem.setForeground(Color.WHITE);
        btnTimKiem.setFont(new Font("Arial", Font.BOLD, 12));
        btnTimKiem.setPreferredSize(new Dimension(100, 35));
        
        btnLamMoi.setBackground(Color.GRAY);
        btnLamMoi.setForeground(Color.WHITE);
        btnLamMoi.setFont(new Font("Arial", Font.BOLD, 12));
        btnLamMoi.setPreferredSize(new Dimension(100, 35));
        
        // Style môn thi buttons
        btnAddMon.setBackground(PRIMARY_COLOR);
        btnAddMon.setForeground(Color.WHITE);
        btnAddMon.setFont(new Font("Arial", Font.BOLD, 16));
        btnAddMon.setPreferredSize(new Dimension(50, 30));
        
        btnRemoveMon.setBackground(DANGER_COLOR);
        btnRemoveMon.setForeground(Color.WHITE);
        btnRemoveMon.setFont(new Font("Arial", Font.BOLD, 16));
        btnRemoveMon.setPreferredSize(new Dimension(50, 30));
        
        // Style text fields
        Font textFieldFont = new Font("Arial", Font.PLAIN, 14);
        txtMaKhoi.setFont(textFieldFont);
        txtTenKhoi.setFont(textFieldFont);
        txtMoTa.setFont(textFieldFont);
        txtTimKiem.setFont(textFieldFont);
        
        // Style lists
        listAvailableMon.setFont(new Font("Arial", Font.PLAIN, 12));
        listSelectedMon.setFont(new Font("Arial", Font.PLAIN, 12));
        
        // Style table
        tableKhoiThi.setFont(new Font("Arial", Font.PLAIN, 12));
        tableKhoiThi.getTableHeader().setFont(new Font("Arial", Font.BOLD, 12));
        tableKhoiThi.getTableHeader().setBackground(PRIMARY_COLOR);
        tableKhoiThi.getTableHeader().setForeground(Color.WHITE);
        tableKhoiThi.setRowHeight(25);
        tableKhoiThi.setSelectionBackground(new Color(230, 240, 255));
        
        // Style status
        lblStatus.setFont(new Font("Arial", Font.ITALIC, 12));
        lblStatus.setForeground(PRIMARY_COLOR);
    }
    
    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));
        
        // Header panel
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);
        
        // Main content panel
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        
        // Form panel (left)
        JPanel formPanel = createFormPanel();
        
        // Table panel (right)
        JPanel tablePanel = createTablePanel();
        
        // Split pane
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, formPanel, tablePanel);
        splitPane.setDividerLocation(500);
        splitPane.setResizeWeight(0.5);
        
        mainPanel.add(splitPane, BorderLayout.CENTER);
        add(mainPanel, BorderLayout.CENTER);
        
        // Status panel
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.add(lblStatus);
        statusPanel.setBorder(BorderFactory.createEtchedBorder());
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Tạo header panel
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JLabel titleLabel = new JLabel("QUẢN LÝ KHỐI THI", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(PRIMARY_COLOR);
        
        panel.add(titleLabel, BorderLayout.CENTER);
        return panel;
    }
    
    /**
     * Tạo form panel
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Thông tin khối thi"));
        
        // Basic info panel
        JPanel basicInfoPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Row 0: Mã khối
        gbc.gridx = 0; gbc.gridy = 0;
        basicInfoPanel.add(new JLabel("Mã khối:"), gbc);
        gbc.gridx = 1;
        basicInfoPanel.add(txtMaKhoi, gbc);
        
        // Row 1: Tên khối
        gbc.gridx = 0; gbc.gridy = 1;
        basicInfoPanel.add(new JLabel("Tên khối:"), gbc);
        gbc.gridx = 1;
        basicInfoPanel.add(txtTenKhoi, gbc);
        
        // Row 2: Mô tả
        gbc.gridx = 0; gbc.gridy = 2;
        basicInfoPanel.add(new JLabel("Mô tả:"), gbc);
        gbc.gridx = 1;
        basicInfoPanel.add(txtMoTa, gbc);
        
        panel.add(basicInfoPanel, BorderLayout.NORTH);
        
        // Môn thi selection panel
        JPanel monThiPanel = createMonThiSelectionPanel();
        panel.add(monThiPanel, BorderLayout.CENTER);
        
        // Buttons panel
        JPanel buttonsPanel = new JPanel(new FlowLayout());
        buttonsPanel.add(btnThem);
        buttonsPanel.add(btnSua);
        buttonsPanel.add(btnXoa);
        buttonsPanel.add(btnLuu);
        buttonsPanel.add(btnHuy);
        
        panel.add(buttonsPanel, BorderLayout.SOUTH);
        
        return panel;
    }

    /**
     * Tạo panel chọn môn thi
     */
    private JPanel createMonThiSelectionPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Chọn môn thi"));

        // Available subjects panel
        JPanel availablePanel = new JPanel(new BorderLayout());
        availablePanel.add(new JLabel("Môn thi có sẵn:"), BorderLayout.NORTH);
        availablePanel.add(new JScrollPane(listAvailableMon), BorderLayout.CENTER);

        // Selected subjects panel
        JPanel selectedPanel = new JPanel(new BorderLayout());
        selectedPanel.add(new JLabel("Môn thi đã chọn:"), BorderLayout.NORTH);
        selectedPanel.add(new JScrollPane(listSelectedMon), BorderLayout.CENTER);

        // Control buttons panel
        JPanel controlPanel = new JPanel(new GridLayout(2, 1, 5, 5));
        controlPanel.add(btnAddMon);
        controlPanel.add(btnRemoveMon);

        // Main selection panel
        JPanel selectionPanel = new JPanel(new BorderLayout(5, 5));
        selectionPanel.add(availablePanel, BorderLayout.WEST);
        selectionPanel.add(controlPanel, BorderLayout.CENTER);
        selectionPanel.add(selectedPanel, BorderLayout.EAST);

        panel.add(selectionPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Tạo table panel
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Danh sách khối thi"));

        // Search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.add(new JLabel("Tìm kiếm:"));
        searchPanel.add(txtTimKiem);
        searchPanel.add(btnTimKiem);
        searchPanel.add(btnLamMoi);

        panel.add(searchPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableKhoiThi.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Set column widths
        tableKhoiThi.getColumnModel().getColumn(0).setPreferredWidth(100); // Mã khối
        tableKhoiThi.getColumnModel().getColumn(1).setPreferredWidth(200); // Tên khối
        tableKhoiThi.getColumnModel().getColumn(2).setPreferredWidth(100); // Số môn
        tableKhoiThi.getColumnModel().getColumn(3).setPreferredWidth(250); // Mô tả
    }

    /**
     * Thiết lập events
     */
    private void setupEvents() {
        // Table selection event
        tableKhoiThi.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedKhoiThi();
            }
        });

        // Môn thi selection events
        btnAddMon.addActionListener(e -> addSelectedMon());
        btnRemoveMon.addActionListener(e -> removeSelectedMon());
    }

    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản lý Khối thi");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);

        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception ex) {
            // Use default look and feel
        }
    }

    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        txtMaKhoi.setText("");
        txtTenKhoi.setText("");
        txtMoTa.setText("");

        selectedMonModel.clear();

        txtMaKhoi.setEditable(true);
        isEditing = false;
        editingMaKhoi = null;

        btnThem.setEnabled(true);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(false);
        btnHuy.setEnabled(false);

        tableKhoiThi.clearSelection();
        updateStatus("Sẵn sàng");
    }

    /**
     * Load dữ liệu khối thi được chọn vào form
     */
    public void loadSelectedKhoiThi() {
        int selectedRow = tableKhoiThi.getSelectedRow();
        if (selectedRow >= 0) {
            String maKhoi = (String) tableModel.getValueAt(selectedRow, 0);
            String tenKhoi = (String) tableModel.getValueAt(selectedRow, 1);
            String moTa = (String) tableModel.getValueAt(selectedRow, 3);

            txtMaKhoi.setText(maKhoi);
            txtTenKhoi.setText(tenKhoi);
            txtMoTa.setText(moTa);

            btnSua.setEnabled(true);
            btnXoa.setEnabled(true);
            btnThem.setEnabled(false);
        } else {
            btnSua.setEnabled(false);
            btnXoa.setEnabled(false);
            btnThem.setEnabled(true);
        }
    }

    /**
     * Thêm môn thi được chọn
     */
    private void addSelectedMon() {
        List<String> selectedValues = listAvailableMon.getSelectedValuesList();
        for (String mon : selectedValues) {
            if (!selectedMonModel.contains(mon)) {
                selectedMonModel.addElement(mon);
            }
        }
    }

    /**
     * Xóa môn thi được chọn
     */
    private void removeSelectedMon() {
        List<String> selectedValues = listSelectedMon.getSelectedValuesList();
        for (String mon : selectedValues) {
            selectedMonModel.removeElement(mon);
        }
    }

    /**
     * Cập nhật bảng dữ liệu
     */
    public void updateTable(List<KhoiThi> khoiThis) {
        tableModel.setRowCount(0);
        for (KhoiThi khoiThi : khoiThis) {
            Object[] row = {
                khoiThi.getMaKhoi(),
                khoiThi.getTenKhoi(),
                khoiThi.getDanhSachMon().size(),
                khoiThi.getMoTa()
            };
            tableModel.addRow(row);
        }
    }

    /**
     * Cập nhật danh sách môn thi có sẵn
     */
    public void updateAvailableMonThi(List<MonThi> monThis) {
        availableMonModel.clear();
        for (MonThi monThi : monThis) {
            availableMonModel.addElement(monThi.getMaMon() + " - " + monThi.getTenMon());
        }
    }

    /**
     * Load danh sách môn thi đã chọn cho khối thi
     */
    public void loadSelectedMonThi(List<String> selectedMons) {
        selectedMonModel.clear();
        for (String maMon : selectedMons) {
            // Tìm tên môn từ available list
            for (int i = 0; i < availableMonModel.size(); i++) {
                String item = availableMonModel.getElementAt(i);
                if (item.startsWith(maMon + " - ")) {
                    selectedMonModel.addElement(item);
                    break;
                }
            }
        }
    }

    /**
     * Lấy dữ liệu từ form
     */
    public KhoiThi getFormData() {
        String maKhoi = txtMaKhoi.getText().trim();
        String tenKhoi = txtTenKhoi.getText().trim();
        String moTa = txtMoTa.getText().trim();

        // Lấy danh sách mã môn từ selected list
        List<String> danhSachMon = new ArrayList<>();
        for (int i = 0; i < selectedMonModel.size(); i++) {
            String item = selectedMonModel.getElementAt(i);
            String maMon = item.split(" - ")[0]; // Lấy mã môn từ "MA001 - Toán học"
            danhSachMon.add(maMon);
        }

        return new KhoiThi(maKhoi, tenKhoi, danhSachMon, moTa);
    }

    /**
     * Cập nhật trạng thái
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }

    /**
     * Hiển thị thông báo lỗi
     */
    public void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    /**
     * Hiển thị thông báo thành công
     */
    public void showSuccess(String message) {
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * Xác nhận xóa
     */
    public boolean confirmDelete(String maKhoi) {
        return JOptionPane.showConfirmDialog(this,
            "Bạn có chắc chắn muốn xóa khối thi: " + maKhoi + "?",
            "Xác nhận xóa",
            JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION;
    }

    // Getter methods cho controller
    public JTextField getTxtTimKiem() { return txtTimKiem; }
    public boolean isEditing() { return isEditing; }
    public String getEditingMaKhoi() { return editingMaKhoi; }
    public DefaultTableModel getTableModel() { return tableModel; }
    public JTable getTableKhoiThi() { return tableKhoiThi; }

    // Event listener methods
    public void addThemListener(ActionListener listener) { btnThem.addActionListener(listener); }
    public void addSuaListener(ActionListener listener) { btnSua.addActionListener(listener); }
    public void addXoaListener(ActionListener listener) { btnXoa.addActionListener(listener); }
    public void addLuuListener(ActionListener listener) { btnLuu.addActionListener(listener); }
    public void addHuyListener(ActionListener listener) { btnHuy.addActionListener(listener); }
    public void addTimKiemListener(ActionListener listener) { btnTimKiem.addActionListener(listener); }
    public void addLamMoiListener(ActionListener listener) { btnLamMoi.addActionListener(listener); }

    /**
     * Chuyển sang chế độ thêm mới
     */
    public void setAddMode() {
        resetForm();
        txtMaKhoi.setEditable(true);
        btnLuu.setEnabled(true);
        btnHuy.setEnabled(true);
        btnThem.setEnabled(false);
        updateStatus("Chế độ thêm mới");
    }

    /**
     * Chuyển sang chế độ sửa
     */
    public void setEditMode() {
        if (tableKhoiThi.getSelectedRow() >= 0) {
            isEditing = true;
            editingMaKhoi = txtMaKhoi.getText();
            txtMaKhoi.setEditable(false);
            btnLuu.setEnabled(true);
            btnHuy.setEnabled(true);
            btnSua.setEnabled(false);
            btnXoa.setEnabled(false);
            btnThem.setEnabled(false);
            updateStatus("Chế độ chỉnh sửa");
        }
    }
}
