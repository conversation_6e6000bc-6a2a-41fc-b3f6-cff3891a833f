package com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong;

import com.mycompany.quanlydoituongdacbiet.controller.LoginController;
import com.mycompany.quanlydoituongdacbiet.view.LoginManagementView;
import javax.swing.*;

/**
 * Main Application - Entry point của hệ thống
 */
public class MainApplication {
    
    public static void main(String[] args) {
        // Set system properties for better UI
        System.setProperty("awt.useSystemAAFontSettings", "on");
        System.setProperty("swing.aatext", "true");
        
        // Set Look and Feel
        setLookAndFeel();
        
        // Start application
        SwingUtilities.invokeLater(() -> {
            try {
                startApplication();
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, 
                    "Lỗi khởi động ứng dụng: " + e.getMessage(), 
                    "Lỗi", 
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
    
    /**
     * Thi<PERSON>t lập Look and Feel
     */
    private static void setLookAndFeel() {
        try {
            // Thử sử dụng Nimbus Look and Feel
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    return;
                }
            }
            
            // Nếu không có Nimbus, sử dụng System Look and Feel
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            
        } catch (Exception e) {
            // Nếu có lỗi, sử dụng default Look and Feel
            System.err.println("Không thể thiết lập Look and Feel: " + e.getMessage());
        }
    }
    
    /**
     * Khởi động ứng dụng
     */
    private static void startApplication() {
        // Tạo và hiển thị Login View
        LoginManagementView loginView = new LoginManagementView();
        LoginController loginController = new LoginController(loginView);
        
        // Hiển thị login view
        loginView.setVisible(true);
        
        // Focus vào username field
        SwingUtilities.invokeLater(() -> {
            loginView.getTxtUsername().requestFocus();
        });
        
        System.out.println("=== HỆ THỐNG QUẢN LÝ ĐIỂM THI ĐẠI HỌC ===");
        System.out.println("Ứng dụng đã khởi động thành công!");
        System.out.println("Tài khoản mặc định: admin/admin");
        System.out.println("========================================");
    }
}
