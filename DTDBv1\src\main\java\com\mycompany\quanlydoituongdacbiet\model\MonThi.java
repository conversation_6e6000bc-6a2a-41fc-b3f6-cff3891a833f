package com.mycompany.quanlydoituongdacbiet.model;

/**
 * Class đại diện cho môn thi
 */
public class MonThi {
    private String maMon;
    private String tenMon;
    private int thoiGianThi; // phút
    private String moTa;
    
    // Constructor mặc định
    public MonThi() {}
    
    // Constructor đầy đủ
    public MonThi(String maMon, String tenMon, int thoiGianThi, String moTa) {
        this.maMon = maMon;
        this.tenMon = tenMon;
        this.thoiGianThi = thoiGianThi;
        this.moTa = moTa;
    }
    
    /**
     * Chuyển đổi từ string trong file thành object
     */
    public static MonThi fromString(String line) {
        try {
            String[] parts = line.split("\\|");
            if (parts.length >= 4) {
                MonThi monThi = new MonThi();
                monThi.maMon = parts[0];
                monThi.tenMon = parts[1];
                monThi.thoiGianThi = Integer.parseInt(parts[2]);
                monThi.moTa = parts[3];
                return monThi;
            }
        } catch (Exception e) {
            System.err.println("Lỗi parse MonThi: " + line);
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Chuyển đổi object thành string để lưu vào file
     */
    @Override
    public String toString() {
        return maMon + "|" + tenMon + "|" + thoiGianThi + "|" + moTa;
    }
    
    // Getters and Setters
    public String getMaMon() { return maMon; }
    public void setMaMon(String maMon) { this.maMon = maMon; }
    
    public String getTenMon() { return tenMon; }
    public void setTenMon(String tenMon) { this.tenMon = tenMon; }
    
    public int getThoiGianThi() { return thoiGianThi; }
    public void setThoiGianThi(int thoiGianThi) { this.thoiGianThi = thoiGianThi; }
    
    public String getMoTa() { return moTa; }
    public void setMoTa(String moTa) { this.moTa = moTa; }
}