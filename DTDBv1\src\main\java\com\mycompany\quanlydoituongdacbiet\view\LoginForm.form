<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="title" type="java.lang.String" value="<PERSON>ệ thống <PERSON>uản lý <PERSON> tượng Đặc biệt"/>
    <Property name="resizable" type="boolean" value="false"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="true"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="headerPanel" alignment="0" max="32767" attributes="0"/>
          <Component id="mainPanel" alignment="0" max="32767" attributes="0"/>
          <Component id="statusPanel" alignment="0" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="headerPanel" min="-2" pref="100" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="mainPanel" max="32767" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="statusPanel" min="-2" pref="40" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="headerPanel">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="7b" red="0" type="rgb"/>
        </Property>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[0, 100]"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Component id="titleLabel" alignment="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Component id="titleLabel" alignment="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="titleLabel">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Arial" size="24" style="1"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="horizontalAlignment" type="int" value="0"/>
            <Property name="text" type="java.lang.String" value="HỆ THỐNG QUẢN LÝ ĐIỂM THI ĐẠI HỌC"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="mainPanel">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="fa" green="f9" red="f8" type="rgb"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="150" max="-2" attributes="0"/>
                  <Component id="formPanel" min="-2" pref="600" max="-2" attributes="0"/>
                  <EmptySpace min="150" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="50" max="-2" attributes="0"/>
                  <Component id="formPanel" min="-2" pref="380" max="-2" attributes="0"/>
                  <EmptySpace min="50" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Container class="javax.swing.JPanel" name="formPanel">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
            <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
              <Border info="org.netbeans.modules.form.compat2.border.CompoundBorderInfo">
                <CompoundBorder>
                  <Border PropertyName="outside" info="org.netbeans.modules.form.compat2.border.BevelBorderInfo">
                    <BevelBorder/>
                  </Border>
                  <Border PropertyName="inside" info="org.netbeans.modules.form.compat2.border.EmptyBorderInfo">
                    <EmptyBorder bottom="50" left="50" right="50" top="50"/>
                  </Border>
                </CompoundBorder>
              </Border>
            </Property>
            <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
              <Dimension value="[600, 380]"/>
            </Property>
          </Properties>

          <Layout>
            <DimensionLayout dim="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="loginTitleLabel" alignment="0" max="32767" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="50" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="1" attributes="0">
                          <Component id="lblPassword" min="-2" pref="140" max="-2" attributes="0"/>
                          <Component id="lblUsername" min="-2" pref="140" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" max="-2" attributes="0">
                          <Component id="txtUsername" max="32767" attributes="0"/>
                          <Component id="txtPassword" min="-2" pref="300" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace min="50" max="32767" attributes="0"/>
                  </Group>
                  <Component id="buttonPanel" alignment="0" max="32767" attributes="0"/>
              </Group>
            </DimensionLayout>
            <DimensionLayout dim="1">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                      <Component id="loginTitleLabel" min="-2" pref="40" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lblUsername" alignment="3" min="-2" pref="45" max="-2" attributes="0"/>
                          <Component id="txtUsername" alignment="3" min="-2" pref="45" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lblPassword" alignment="3" min="-2" pref="45" max="-2" attributes="0"/>
                          <Component id="txtPassword" alignment="3" min="-2" pref="45" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
                      <Component id="buttonPanel" min="-2" pref="60" max="-2" attributes="0"/>
                      <EmptySpace min="20" max="32767" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
          </Layout>
          <SubComponents>
            <Component class="javax.swing.JLabel" name="loginTitleLabel">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="20" style="1"/>
                </Property>
                <Property name="horizontalAlignment" type="int" value="0"/>
                <Property name="text" type="java.lang.String" value="ĐĂNG NHẬP HỆ THỐNG"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="lblUsername">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="1"/>
                </Property>
                <Property name="horizontalAlignment" type="int" value="4"/>
                <Property name="text" type="java.lang.String" value="Tên đăng nhập:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JTextField" name="txtUsername">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="0"/>
                </Property>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[300, 45]"/>
                </Property>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="lblPassword">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="1"/>
                </Property>
                <Property name="horizontalAlignment" type="int" value="4"/>
                <Property name="text" type="java.lang.String" value="Mật khẩu:"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JPasswordField" name="txtPassword">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="0"/>
                </Property>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[300, 45]"/>
                </Property>
              </Properties>
            </Component>
            <Container class="javax.swing.JPanel" name="buttonPanel">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
              </Properties>

              <Layout>
                <DimensionLayout dim="0">
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" alignment="0" attributes="0">
                          <EmptySpace min="-2" pref="50" max="-2" attributes="0"/>
                          <Component id="btnLogin" min="-2" pref="130" max="-2" attributes="0"/>
                          <EmptySpace type="separate" max="-2" attributes="0"/>
                          <Component id="btnAbout" min="-2" pref="130" max="-2" attributes="0"/>
                          <EmptySpace type="separate" max="-2" attributes="0"/>
                          <Component id="btnExit" min="-2" pref="130" max="-2" attributes="0"/>
                          <EmptySpace min="50" max="32767" attributes="0"/>
                      </Group>
                  </Group>
                </DimensionLayout>
                <DimensionLayout dim="1">
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" alignment="1" attributes="0">
                          <EmptySpace max="32767" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="btnLogin" alignment="3" min="-2" pref="45" max="-2" attributes="0"/>
                              <Component id="btnAbout" alignment="3" min="-2" pref="45" max="-2" attributes="0"/>
                              <Component id="btnExit" alignment="3" min="-2" pref="45" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                      </Group>
                  </Group>
                </DimensionLayout>
              </Layout>
              <SubComponents>
                <Component class="javax.swing.JButton" name="btnLogin">
                  <Properties>
                    <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="ff" green="7b" red="0" type="rgb"/>
                    </Property>
                    <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                      <Font name="Arial" size="16" style="1"/>
                    </Property>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="ff" green="ff" red="ff" type="rgb"/>
                    </Property>
                    <Property name="text" type="java.lang.String" value="Đăng nhập"/>
                    <Property name="focusPainted" type="boolean" value="false"/>
                    <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                      <Dimension value="[130, 45]"/>
                    </Property>
                  </Properties>
                </Component>
                <Component class="javax.swing.JButton" name="btnAbout">
                  <Properties>
                    <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="7d" green="75" red="6c" type="rgb"/>
                    </Property>
                    <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                      <Font name="Arial" size="16" style="1"/>
                    </Property>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="ff" green="ff" red="ff" type="rgb"/>
                    </Property>
                    <Property name="text" type="java.lang.String" value="Thông tin"/>
                    <Property name="focusPainted" type="boolean" value="false"/>
                    <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                      <Dimension value="[130, 45]"/>
                    </Property>
                  </Properties>
                </Component>
                <Component class="javax.swing.JButton" name="btnExit">
                  <Properties>
                    <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="45" green="35" red="dc" type="rgb"/>
                    </Property>
                    <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                      <Font name="Arial" size="16" style="1"/>
                    </Property>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="ff" green="ff" red="ff" type="rgb"/>
                    </Property>
                    <Property name="text" type="java.lang.String" value="Thoát"/>
                    <Property name="focusPainted" type="boolean" value="false"/>
                    <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                      <Dimension value="[130, 45]"/>
                    </Property>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="statusPanel">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="fa" green="f9" red="f8" type="rgb"/>
        </Property>
        <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
          <Border info="org.netbeans.modules.form.compat2.border.BevelBorderInfo">
            <BevelBorder bevelType="1"/>
          </Border>
        </Property>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[0, 40]"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="lblStatus" min="-2" max="-2" attributes="0"/>
                  <EmptySpace max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Component id="lblStatus" alignment="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="lblStatus">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Arial" size="16" style="0"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="0" green="0" red="ff" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value=" "/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
