package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.dao.UserDAO;
import com.mycompany.quanlydoituongdacbiet.model.User;

/**
 * Controller xử lý authentication và quản lý session
 */
public class AuthenticationController {
    private UserDAO userDAO;
    private User currentUser; // User hiện tại đã đăng nhập
    
    public AuthenticationController() {
        this.userDAO = new UserDAO();
        this.currentUser = null;
        
        // Tạo admin mặc định nếu chưa có user nào
        userDAO.createDefaultAdmin();
    }
    
    /**
     * Đăng nhập
     */
    public boolean login(String username, String password) {
        if (username == null || username.trim().isEmpty() || 
            password == null || password.trim().isEmpty()) {
            return false;
        }
        
        User user = userDAO.authenticate(username.trim(), password);
        if (user != null) {
            this.currentUser = user;
            return true;
        }
        
        return false;
    }
    
    /**
     * Đăng xuất
     */
    public void logout() {
        this.currentUser = null;
    }
    
    /**
     * <PERSON>ểm tra xem user đã đăng nhập chưa
     */
    public boolean isLoggedIn() {
        return currentUser != null;
    }
    
    /**
     * Lấy user hiện tại
     */
    public User getCurrentUser() {
        return currentUser;
    }
    
    /**
     * Kiểm tra xem user hiện tại có phải admin không
     */
    public boolean isCurrentUserAdmin() {
        return currentUser != null && currentUser.isAdmin();
    }
    
    /**
     * Đổi mật khẩu cho user hiện tại
     */
    public boolean changePassword(String oldPassword, String newPassword) {
        if (currentUser == null) {
            return false;
        }
        
        if (newPassword == null || newPassword.trim().length() < 3) {
            return false; // Mật khẩu quá ngắn
        }
        
        boolean success = userDAO.changePassword(currentUser.getUsername(), oldPassword, newPassword.trim());
        if (success) {
            // Cập nhật mật khẩu trong session hiện tại
            currentUser.setPassword(newPassword.trim());
        }
        
        return success;
    }
    
    /**
     * Đăng ký user mới (chỉ admin mới được phép)
     */
    public boolean registerUser(String username, String password, String fullName, String role) {
        if (!isCurrentUserAdmin()) {
            return false; // Chỉ admin mới được tạo user
        }
        
        if (username == null || username.trim().isEmpty() ||
            password == null || password.trim().isEmpty() ||
            fullName == null || fullName.trim().isEmpty()) {
            return false;
        }
        
        if (password.trim().length() < 3) {
            return false; // Mật khẩu quá ngắn
        }
        
        // Validate role
        if (!"ADMIN".equals(role) && !"USER".equals(role)) {
            role = "USER"; // Mặc định là USER
        }
        
        User newUser = new User(username.trim(), password.trim(), fullName.trim(), role);
        return userDAO.addUser(newUser);
    }
    
    /**
     * Xóa user (chỉ admin mới được phép và không thể xóa chính mình)
     */
    public boolean deleteUser(String username) {
        if (!isCurrentUserAdmin()) {
            return false; // Chỉ admin mới được xóa user
        }
        
        if (currentUser.getUsername().equals(username)) {
            return false; // Không thể xóa chính mình
        }
        
        return userDAO.deleteUser(username);
    }
    
    /**
     * Lấy tất cả users (chỉ admin mới được phép)
     */
    public java.util.List<User> getAllUsers() {
        if (!isCurrentUserAdmin()) {
            return new java.util.ArrayList<>(); // Trả về list rỗng nếu không phải admin
        }
        
        return userDAO.getAllUsers();
    }
    
    /**
     * Kiểm tra tính hợp lệ của thông tin đăng nhập
     */
    public String validateLoginInput(String username, String password) {
        if (username == null || username.trim().isEmpty()) {
            return "Tên đăng nhập không được để trống";
        }
        
        if (password == null || password.trim().isEmpty()) {
            return "Mật khẩu không được để trống";
        }
        
        return null; // Hợp lệ
    }
    
    /**
     * Kiểm tra tính hợp lệ của thông tin đăng ký
     */
    public String validateRegistrationInput(String username, String password, String fullName) {
        if (username == null || username.trim().isEmpty()) {
            return "Tên đăng nhập không được để trống";
        }
        
        if (username.trim().length() < 3) {
            return "Tên đăng nhập phải có ít nhất 3 ký tự";
        }
        
        if (password == null || password.trim().isEmpty()) {
            return "Mật khẩu không được để trống";
        }
        
        if (password.trim().length() < 3) {
            return "Mật khẩu phải có ít nhất 3 ký tự";
        }
        
        if (fullName == null || fullName.trim().isEmpty()) {
            return "Họ tên không được để trống";
        }
        
        // Kiểm tra username đã tồn tại chưa
        if (userDAO.findByUsername(username.trim()) != null) {
            return "Tên đăng nhập đã tồn tại";
        }
        
        return null; // Hợp lệ
    }
}
