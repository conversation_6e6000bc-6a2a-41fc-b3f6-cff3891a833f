package com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong;

import com.mycompany.quanlydoituongdacbiet.view.LoginManagementViewNew;
import com.mycompany.quanlydoituongdacbiet.view.UserManagementViewNew;
import com.mycompany.quanlydoituongdacbiet.model.User;
import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.List;

/**
 * Test class cho các giao diện mới với thiết kế theo chuẩn ThiSinhManagementView
 */
public class TestNewViews {
    
    public static void main(String[] args) {
        // Set Look and Feel
        try {
            UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");
        } catch (Exception e) {
            // Use default look and feel
            e.printStackTrace();
        }
        
        SwingUtilities.invokeLater(() -> {
            // Test Login View
            testLoginView();
        });
    }
    
    /**
     * Test LoginManagementViewNew
     */
    private static void testLoginView() {
        LoginManagementViewNew loginView = new LoginManagementViewNew();
        
        // Add event listeners
        loginView.addLoginListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (loginView.validateInput()) {
                    String username = loginView.getUsername();
                    String password = loginView.getPassword();
                    
                    // Simple validation
                    if ("admin".equals(username) && "admin".equals(password)) {
                        loginView.showSuccess("Đăng nhập thành công!");
                        loginView.showMainMenu();
                        
                        // Test User Management View
                        SwingUtilities.invokeLater(() -> {
                            testUserManagementView();
                        });
                    } else {
                        loginView.showError("Tên đăng nhập hoặc mật khẩu không đúng!");
                    }
                }
            }
        });
        
        loginView.addAboutListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                JOptionPane.showMessageDialog(loginView, 
                    "Hệ thống Quản lý Điểm thi Đại học\n" +
                    "Phiên bản: 1.0\n" +
                    "Thiết kế theo chuẩn ThiSinhManagementView\n" +
                    "Màu sắc chủ đạo: #0071F0", 
                    "Thông tin hệ thống", 
                    JOptionPane.INFORMATION_MESSAGE);
            }
        });
        
        loginView.addExitListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int option = JOptionPane.showConfirmDialog(loginView, 
                    "Bạn có chắc chắn muốn thoát?", 
                    "Xác nhận thoát", 
                    JOptionPane.YES_NO_OPTION);
                if (option == JOptionPane.YES_OPTION) {
                    System.exit(0);
                }
            }
        });
        
        loginView.setVisible(true);
    }
    
    /**
     * Test UserManagementViewNew
     */
    private static void testUserManagementView() {
        UserManagementViewNew userView = new UserManagementViewNew();
        
        // Sample data
        List<User> sampleUsers = createSampleUsers();
        userView.loadUsers(sampleUsers);
        
        // Add event listeners
        userView.addAddListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                userView.updateStatus("Chế độ thêm người dùng mới");
                userView.setEditing(false);
                userView.resetForm();
            }
        });
        
        userView.addEditListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                userView.updateStatus("Chế độ chỉnh sửa người dùng");
                userView.setEditing(true);
            }
        });
        
        userView.addDeleteListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int option = JOptionPane.showConfirmDialog(userView, 
                    "Bạn có chắc chắn muốn xóa người dùng này?", 
                    "Xác nhận xóa", 
                    JOptionPane.YES_NO_OPTION);
                if (option == JOptionPane.YES_OPTION) {
                    userView.showSuccess("Đã xóa người dùng thành công!");
                    userView.resetForm();
                }
            }
        });
        
        userView.addSaveListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // Validate form
                if (userView.getTxtUsername().getText().trim().isEmpty()) {
                    userView.showError("Vui lòng nhập tên đăng nhập!");
                    return;
                }
                
                if (userView.getTxtFullName().getText().trim().isEmpty()) {
                    userView.showError("Vui lòng nhập họ tên!");
                    return;
                }
                
                if (userView.getTxtPassword().getPassword().length == 0) {
                    userView.showError("Vui lòng nhập mật khẩu!");
                    return;
                }
                
                // Simulate save
                if (userView.isEditing()) {
                    userView.showSuccess("Đã cập nhật người dùng thành công!");
                } else {
                    userView.showSuccess("Đã thêm người dùng mới thành công!");
                }
                userView.resetForm();
            }
        });
        
        userView.addCancelListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                userView.resetForm();
                userView.updateStatus("Đã hủy thao tác");
            }
        });
        
        userView.addSearchListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String searchText = userView.getTxtSearch().getText().trim();
                if (searchText.isEmpty()) {
                    userView.showError("Vui lòng nhập từ khóa tìm kiếm!");
                    return;
                }
                userView.updateStatus("Đang tìm kiếm: " + searchText);
                // Simulate search
                List<User> filteredUsers = sampleUsers.stream()
                    .filter(user -> user.getUsername().toLowerCase().contains(searchText.toLowerCase()) ||
                                   user.getFullName().toLowerCase().contains(searchText.toLowerCase()))
                    .collect(java.util.stream.Collectors.toList());
                userView.loadUsers(filteredUsers);
            }
        });
        
        userView.addRefreshListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                userView.loadUsers(sampleUsers);
                userView.getTxtSearch().setText("");
                userView.updateStatus("Đã làm mới danh sách");
            }
        });
        
        userView.addLogoutListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int option = JOptionPane.showConfirmDialog(userView, 
                    "Bạn có chắc chắn muốn đăng xuất?", 
                    "Xác nhận đăng xuất", 
                    JOptionPane.YES_NO_OPTION);
                if (option == JOptionPane.YES_OPTION) {
                    userView.dispose();
                    // Có thể quay lại login view ở đây
                }
            }
        });
        
        userView.setVisible(true);
    }
    
    /**
     * Tạo dữ liệu mẫu
     */
    private static List<User> createSampleUsers() {
        List<User> users = new ArrayList<>();
        
        User admin = new User("admin", "Administrator", "ADMIN");
        admin.setEmail("<EMAIL>");
        users.add(admin);
        
        User user1 = new User("user1", "Nguyễn Văn A", "USER");
        user1.setEmail("<EMAIL>");
        users.add(user1);
        
        User user2 = new User("user2", "Trần Thị B", "USER");
        user2.setEmail("<EMAIL>");
        users.add(user2);
        
        User user3 = new User("manager", "Lê Văn C", "ADMIN");
        user3.setEmail("<EMAIL>");
        users.add(user3);
        
        return users;
    }
}
