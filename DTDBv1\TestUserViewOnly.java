import com.mycompany.quanlydoituongdacbiet.view.UserManagementView;

/**
 * Test chỉ UserManagementView
 */
public class TestUserViewOnly {
    
    public static void main(String[] args) {
        System.out.println("=== TEST USER VIEW ONLY ===");
        
        try {
            System.out.println("1. Tạo UserManagementView...");
            UserManagementView userView = new UserManagementView();
            System.out.println("✅ UserManagementView tạo thành công!");
            
            System.out.println("2. Hiển thị view...");
            userView.setVisible(true);
            System.out.println("✅ UserManagementView hiển thị thành công!");
            
            System.out.println("\n=== KẾT QUẢ ===");
            System.out.println("UserManagementView hoạt động tốt!");
            
        } catch (Exception ex) {
            System.out.println("❌ LỖI: " + ex.getMessage());
            ex.printStackTrace();
        }
    }
}
