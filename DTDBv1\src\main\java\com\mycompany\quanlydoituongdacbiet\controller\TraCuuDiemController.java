package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.view.TraCuuDiemView;
import com.mycompany.quanlydoituongdacbiet.service.TraCuuDiemService;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Controller cho tra cứu điểm thi
 */
public class TraCuuDiemController {
    private TraCuuDiemView view;
    private TraCuuDiemService service;
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
    
    public TraCuuDiemController(TraCuuDiemView view) {
        this.view = view;
        this.service = TraCuuDiemService.getInstance();
        initEventListeners();
        loadKhoiThiData();
        view.updateStatus("Sẵn sàng tra cứu điểm thi");
    }
    
    /**
     * Khởi tạo event listeners
     */
    private void initEventListeners() {
        view.addTraCuuListener(new TraCuuActionListener());
        view.addLamMoiListener(new LamMoiActionListener());
        view.addKhoiThiSelectionListener(new KhoiThiSelectionListener());
    }
    
    /**
     * Load dữ liệu khối thi vào ComboBox
     */
    private void loadKhoiThiData() {
        String[] khoiThis = service.getKhoiThiDisplayList();
        view.loadKhoiThiData(khoiThis);
    }
    
    /**
     * Hiển thị view
     */
    public void showView() {
        view.setVisible(true);
    }
    
    /**
     * Action listener cho nút Tra cứu
     */
    private class TraCuuActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String soBaoDanh = view.getSoBaoDanh();
            
            if (soBaoDanh.isEmpty()) {
                view.showError("Vui lòng nhập số báo danh!");
                return;
            }
            
            try {
                // Kiểm tra thí sinh có tồn tại không
                if (!service.isThiSinhExists(soBaoDanh)) {
                    view.showError("Không tìm thấy thí sinh với số báo danh: " + soBaoDanh);
                    view.clearData();
                    view.updateStatus("Không tìm thấy thí sinh");
                    return;
                }
                
                // Lấy thông tin thí sinh
                ThiSinh thiSinh = service.getThiSinhInfo(soBaoDanh);
                view.setThiSinhInfo(thiSinh);
                
                // Lấy và hiển thị điểm thi
                loadDiemThiData(soBaoDanh);
                
                // Cập nhật thống kê
                updateThongKe(soBaoDanh);
                
                view.updateStatus("Đã tải thông tin điểm thi của thí sinh " + soBaoDanh);
                
            } catch (Exception ex) {
                view.showError("Lỗi khi tra cứu: " + ex.getMessage());
                ex.printStackTrace();
            }
        }
    }
    
    /**
     * Load dữ liệu điểm thi vào bảng
     */
    private void loadDiemThiData(String soBaoDanh) {
        List<TraCuuDiemService.DiemThiDetail> details = service.getDiemThiDetails(soBaoDanh);
        
        // Clear bảng
        view.getTableModel().setRowCount(0);
        
        // Thêm dữ liệu vào bảng
        for (TraCuuDiemService.DiemThiDetail detail : details) {
            Object[] row = {
                detail.getMaMon(),
                detail.getTenMon(),
                String.format("%.2f", detail.getDiem()),
                dateFormat.format(detail.getNgayThi()),
                detail.getXepLoai()
            };
            view.getTableModel().addRow(row);
        }
        
        if (details.isEmpty()) {
            view.updateStatus("Thí sinh chưa có điểm thi nào");
        }
    }
    
    /**
     * Cập nhật thống kê điểm
     */
    private void updateThongKe(String soBaoDanh) {
        TraCuuDiemService.DiemThongKe thongKe = service.tinhThongKeDiem(soBaoDanh);
        view.updateStats(
            thongKe.getTongSoMon(),
            thongKe.getDiemTrungBinh(),
            thongKe.getDiemCao(),
            thongKe.getDiemThap()
        );
    }
    
    /**
     * Action listener cho nút Làm mới
     */
    private class LamMoiActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.clearData();
            view.setSoBaoDanh("");
            view.updateStatus("Đã làm mới dữ liệu");
        }
    }
    
    /**
     * Action listener cho ComboBox khối thi
     */
    private class KhoiThiSelectionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String soBaoDanh = view.getSoBaoDanh();
            String selectedKhoi = view.getSelectedKhoiThi();
            
            if (soBaoDanh.isEmpty() || selectedKhoi == null) {
                view.setTongDiemKhoi(null, 0.0);
                return;
            }
            
            try {
                // Kiểm tra thí sinh có tồn tại không
                if (!service.isThiSinhExists(soBaoDanh)) {
                    view.setTongDiemKhoi(null, 0.0);
                    return;
                }
                
                // Tính tổng điểm khối thi
                double tongDiem = service.tinhTongDiemKhoiThi(soBaoDanh, selectedKhoi);
                
                if (tongDiem >= 0) {
                    view.setTongDiemKhoi(selectedKhoi, tongDiem);
                    view.updateStatus("Đã tính tổng điểm khối thi " + selectedKhoi + ": " + String.format("%.2f", tongDiem));
                } else {
                    view.setTongDiemKhoi(selectedKhoi, 0.0);
                    view.updateStatus("Thí sinh chưa có đủ điểm tất cả môn trong khối " + selectedKhoi);
                    view.showInfo("Thí sinh chưa có đủ điểm tất cả môn trong khối thi " + selectedKhoi + 
                                 "\nChỉ tính tổng điểm khi có đủ điểm tất cả môn trong khối.");
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi khi tính điểm khối thi: " + ex.getMessage());
                ex.printStackTrace();
            }
        }
    }
    
    /**
     * Refresh dữ liệu khối thi (gọi khi có thay đổi từ KhoiThiController)
     */
    public void refreshKhoiThiData() {
        loadKhoiThiData();
        
        // Nếu đang có thí sinh được tra cứu, cập nhật lại tổng điểm khối
        String soBaoDanh = view.getSoBaoDanh();
        String selectedKhoi = view.getSelectedKhoiThi();
        
        if (!soBaoDanh.isEmpty() && selectedKhoi != null && service.isThiSinhExists(soBaoDanh)) {
            double tongDiem = service.tinhTongDiemKhoiThi(soBaoDanh, selectedKhoi);
            if (tongDiem >= 0) {
                view.setTongDiemKhoi(selectedKhoi, tongDiem);
            } else {
                view.setTongDiemKhoi(selectedKhoi, 0.0);
            }
        }
    }
    
    /**
     * Static reference để các controller khác có thể gọi refresh
     */
    private static TraCuuDiemController instance;
    
    public TraCuuDiemController(TraCuuDiemView view, boolean setAsInstance) {
        this(view);
        if (setAsInstance) {
            TraCuuDiemController.instance = this;
        }
    }
    
    /**
     * Static method để refresh từ bất kỳ đâu
     */
    public static void refreshKhoiThiDataStatic() {
        if (instance != null) {
            instance.refreshKhoiThiData();
        }
    }
}
