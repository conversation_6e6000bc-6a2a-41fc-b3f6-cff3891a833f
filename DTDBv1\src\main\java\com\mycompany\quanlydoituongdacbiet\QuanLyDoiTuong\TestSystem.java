package com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong;

import com.mycompany.quanlydoituongdacbiet.dao.*;
import com.mycompany.quanlydoituongdacbiet.model.*;
import com.mycompany.quanlydoituongdacbiet.utils.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Class test hệ thống
 */
public class TestSystem {
    
    public static void main(String[] args) {
        System.out.println("=== BẮT ĐẦU TEST HỆ THỐNG QUẢN LÝ ĐIỂM THI ===\n");
        
        // Test các DAO
        testThiSinhDAO();
        testMonThiDAO();
        testKhoiThiDAO();
        testDiemThiDAO();
        testUtils();
        
        System.out.println("\n=== KẾT THÚC TEST ===");
    }
    
    /**
     * Test ThiSinhDAO
     */
    public static void testThiSinhDAO() {
        System.out.println("--- TEST THÍ SINH DAO ---");
        ThiSinhDAO dao = new ThiSinhDAO();
        
        // Lấy tất cả thí sinh
        List<ThiSinh> allThiSinh = dao.getAllThiSinh();
        System.out.println("Tổng số thí sinh: " + allThiSinh.size());
        
        // Hiển thị thông tin thí sinh
        for (ThiSinh ts : allThiSinh) {
            System.out.println("- " + ts.getSoBaoDanh() + ": " + ts.getHoTen() + 
                             " (Khối: " + ts.getMaKhoi() + ")");
        }
        
        // Test tìm thí sinh theo số báo danh
        ThiSinh ts = dao.getThiSinhBySoBaoDanh("TS001");
        if (ts != null) {
            System.out.println("Tìm thấy thí sinh TS001: " + ts.getHoTen());
        }
        
        // Test tìm theo tên
        List<ThiSinh> searchResult = dao.searchThiSinhByName("Nguyễn");
        System.out.println("Tìm thấy " + searchResult.size() + " thí sinh có tên chứa 'Nguyễn'");
        
        System.out.println();
    }
    
    /**
     * Test MonThiDAO
     */
    public static void testMonThiDAO() {
        System.out.println("--- TEST MÔN THI DAO ---");
        MonThiDAO dao = new MonThiDAO();
        
        // Lấy tất cả môn thi
        List<MonThi> allMonThi = dao.getAllMonThi();
        System.out.println("Tổng số môn thi: " + allMonThi.size());
        
        // Hiển thị thông tin môn thi
        for (MonThi mt : allMonThi) {
            System.out.println("- " + mt.getMaMon() + ": " + mt.getTenMon() + 
                             " (" + mt.getThoiGianThi() + " phút)");
        }
        
        // Test tìm môn theo mã
        MonThi mt = dao.getMonThiByMaMon("TOAN");
        if (mt != null) {
            System.out.println("Tìm thấy môn TOAN: " + mt.getTenMon());
        }
        
        System.out.println();
    }
    
    /**
     * Test KhoiThiDAO
     */
    public static void testKhoiThiDAO() {
        System.out.println("--- TEST KHỐI THI DAO ---");
        KhoiThiDAO dao = new KhoiThiDAO();
        
        // Lấy tất cả khối thi
        List<KhoiThi> allKhoiThi = dao.getAllKhoiThi();
        System.out.println("Tổng số khối thi: " + allKhoiThi.size());
        
        // Hiển thị thông tin khối thi
        for (KhoiThi kt : allKhoiThi) {
            System.out.println("- " + kt.getMaKhoi() + ": " + kt.getTenKhoi());
            System.out.println("  Môn thi: " + String.join(", ", kt.getDanhSachMon()));
        }
        
        // Test tìm khối có môn TOAN
        List<KhoiThi> khoiCoToan = dao.getKhoiThiByMon("TOAN");
        System.out.println("Có " + khoiCoToan.size() + " khối thi có môn Toán");
        
        System.out.println();
    }
    
    /**
     * Test DiemThiDAO
     */
    public static void testDiemThiDAO() {
        System.out.println("--- TEST ĐIỂM THI DAO ---");
        DiemThiDAO dao = new DiemThiDAO();
        
        // Lấy tất cả điểm thi
        List<DiemThi> allDiemThi = dao.getAllDiemThi();
        System.out.println("Tổng số bản ghi điểm: " + allDiemThi.size());
        
        // Test lấy điểm của thí sinh TS001
        List<DiemThi> diemTS001 = dao.getDiemThiByThiSinh("TS001");
        System.out.println("\nĐiểm thi của TS001:");
        for (DiemThi dt : diemTS001) {
            System.out.println("- " + dt.getMaMon() + ": " + dt.getDiem() + 
                             " (ngày thi: " + DateUtils.dateToString(dt.getNgayThi()) + ")");
        }
        
        // Test tính điểm trung bình
        double dtb = dao.getDiemTrungBinh("TS001");
        System.out.println("Điểm trung bình TS001: " + String.format("%.2f", dtb));
        
        // Test thống kê điểm theo môn
        Map<String, Double> thongKe = dao.getThongKeDiemTheoMon();
        System.out.println("\nThống kê điểm trung bình theo môn:");
        for (Map.Entry<String, Double> entry : thongKe.entrySet()) {
            System.out.println("- " + entry.getKey() + ": " + 
                             String.format("%.2f", entry.getValue()));
        }
        
        System.out.println();
    }
    
    /**
     * Test Utils
     */
    public static void testUtils() {
        System.out.println("--- TEST UTILS ---");
        
        // Test ValidationUtils
        System.out.println("Test Validation:");
        System.out.println("- Email hợp lệ: " + ValidationUtils.isValidEmail("<EMAIL>"));
        System.out.println("- Phone hợp lệ: " + ValidationUtils.isValidPhone("0123456789"));
        System.out.println("- Điểm hợp lệ: " + ValidationUtils.isValidDiem(8.5));
        System.out.println("- Tên hợp lệ: " + ValidationUtils.isValidName("Nguyễn Văn An"));
        
        // Test DateUtils
        System.out.println("\nTest DateUtils:");
        Date now = DateUtils.getCurrentDate();
        System.out.println("- Ngày hiện tại: " + DateUtils.dateToString(now));
        
        Date testDate = DateUtils.stringToDate("15/03/2006");
        System.out.println("- Parse date: " + DateUtils.dateToString(testDate));
        
        System.out.println("- Date hợp lệ: " + DateUtils.isValidDateString("31/12/2023"));
        System.out.println("- Date không hợp lệ: " + DateUtils.isValidDateString("32/13/2023"));
        
        System.out.println();
    }
    
    /**
     * Test thêm dữ liệu mới
     */
    public static void testAddData() {
        System.out.println("--- TEST THÊM DỮ LIỆU ---");
        
        // Test thêm thí sinh mới
        ThiSinhDAO thiSinhDAO = new ThiSinhDAO();
        ThiSinh newThiSinh = new ThiSinh();
        newThiSinh.setSoBaoDanh("TS006");
        newThiSinh.setHoTen("Nguyễn Thị Hoa");
        newThiSinh.setGioiTinh("Nữ");
        newThiSinh.setNgaySinh(DateUtils.stringToDate("01/01/2006"));
        newThiSinh.setDiaChi("Hà Nội");
        newThiSinh.setSoDienThoai("0123456788");
        newThiSinh.setEmail("<EMAIL>");
        newThiSinh.setMaKhoi("A00");
        
        boolean addResult = thiSinhDAO.addThiSinh(newThiSinh);
        System.out.println("Thêm thí sinh mới: " + (addResult ? "Thành công" : "Thất bại"));
        
        // Test thêm điểm thi
        DiemThiDAO diemDAO = new DiemThiDAO();
        DiemThi newDiem = new DiemThi();
        newDiem.setSoBaoDanh("TS006");
        newDiem.setMaMon("TOAN");
        newDiem.setDiem(9.5);
        newDiem.setNgayThi(DateUtils.getCurrentDate());
        
        boolean addDiemResult = diemDAO.addDiemThi(newDiem);
        System.out.println("Thêm điểm thi: " + (addDiemResult ? "Thành công" : "Thất bại"));
        
        System.out.println();
    }
}