import com.mycompany.quanlydoituongdacbiet.view.UserManagementView;
import com.mycompany.quanlydoituongdacbiet.controller.UserController;
import com.mycompany.quanlydoituongdacbiet.model.User;

/**
 * Test UserController
 */
public class TestUserController {
    
    public static void main(String[] args) {
        System.out.println("=== TEST USER CONTROLLER ===");
        
        try {
            // Tạo user admin để test
            User adminUser = new User();
            adminUser.setUsername("admin");
            adminUser.setFullName("Administrator");
            adminUser.setRole("ADMIN");
            
            System.out.println("1. Tạo UserManagementView...");
            UserManagementView userView = new UserManagementView();
            System.out.println("✅ UserManagementView tạo thành công!");
            
            System.out.println("2. Tạo UserController...");
            UserController userController = new UserController(userView, adminUser);
            System.out.println("✅ UserController tạo thành công!");
            
            System.out.println("3. Hiển thị view...");
            userView.setVisible(true);
            System.out.println("✅ UserManagementView hiển thị thành công!");
            
            System.out.println("\n=== KẾT QUẢ ===");
            System.out.println("UserController hoạt động tốt!");
            System.out.println("Có thể test các nút trong GUI.");
            
        } catch (Exception ex) {
            System.out.println("❌ LỖI: " + ex.getMessage());
            ex.printStackTrace();
        }
    }
}
