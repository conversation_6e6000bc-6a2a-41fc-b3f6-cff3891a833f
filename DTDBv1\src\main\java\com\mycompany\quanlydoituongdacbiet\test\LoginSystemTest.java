package com.mycompany.quanlydoituongdacbiet.test;

import com.mycompany.quanlydoituongdacbiet.controller.AuthenticationController;
import com.mycompany.quanlydoituongdacbiet.dao.UserDAO;
import com.mycompany.quanlydoituongdacbiet.model.User;

/**
 * Test class để kiểm tra hệ thống login
 */
public class LoginSystemTest {
    
    public static void main(String[] args) {
        System.out.println("=== KIỂM TRA HỆ THỐNG LOGIN ===\n");
        
        // Test 1: Kiểm tra UserDAO
        testUserDAO();
        
        // Test 2: <PERSON><PERSON><PERSON> tra AuthenticationController
        testAuthenticationController();
        
        // Test 3: Kiểm tra file users.txt
        testUsersFile();
        
        System.out.println("\n=== KẾT THÚC KIỂM TRA ===");
    }
    
    private static void testUserDAO() {
        System.out.println("1. KIỂM TRA UserDAO:");
        
        UserDAO userDAO = new UserDAO();
        
        // Test lấy tất cả users
        System.out.println("   - L<PERSON>y danh sách users:");
        var users = userDAO.getAllUsers();
        for (User user : users) {
            System.out.println("     + " + user.getUsername() + " - " + user.getFullName() + " (" + user.getRole() + ")");
        }
        
        // Test tìm user theo username
        System.out.println("   - Tìm user 'admin':");
        User admin = userDAO.findByUsername("admin");
        if (admin != null) {
            System.out.println("     ✅ Tìm thấy: " + admin.getFullName());
        } else {
            System.out.println("     ❌ Không tìm thấy");
        }
        
        // Test authentication
        System.out.println("   - Kiểm tra đăng nhập admin/admin123:");
        User authUser = userDAO.authenticate("admin", "admin123");
        if (authUser != null) {
            System.out.println("     ✅ Đăng nhập thành công");
        } else {
            System.out.println("     ❌ Đăng nhập thất bại");
        }
        
        // Test authentication với sai password
        System.out.println("   - Kiểm tra đăng nhập admin/wrongpass:");
        User wrongAuth = userDAO.authenticate("admin", "wrongpass");
        if (wrongAuth == null) {
            System.out.println("     ✅ Từ chối đăng nhập đúng cách");
        } else {
            System.out.println("     ❌ Lỗi: Cho phép đăng nhập với sai password");
        }
        
        System.out.println();
    }
    
    private static void testAuthenticationController() {
        System.out.println("2. KIỂM TRA AuthenticationController:");
        
        AuthenticationController authController = new AuthenticationController();
        
        // Test login
        System.out.println("   - Kiểm tra login admin/admin123:");
        boolean loginResult = authController.login("admin", "admin123");
        if (loginResult) {
            System.out.println("     ✅ Login thành công");
            System.out.println("     - User hiện tại: " + authController.getCurrentUser().getFullName());
            System.out.println("     - Là admin: " + authController.isCurrentUserAdmin());
        } else {
            System.out.println("     ❌ Login thất bại");
        }
        
        // Test validation
        System.out.println("   - Kiểm tra validation input:");
        String validationError = authController.validateLoginInput("", "");
        if (validationError != null) {
            System.out.println("     ✅ Validation hoạt động: " + validationError);
        } else {
            System.out.println("     ❌ Validation không hoạt động");
        }
        
        // Test đăng ký user mới (nếu đã login là admin)
        if (authController.isLoggedIn() && authController.isCurrentUserAdmin()) {
            System.out.println("   - Kiểm tra đăng ký user mới:");
            boolean registerResult = authController.registerUser("testuser", "testpass", "Test User", "USER");
            if (registerResult) {
                System.out.println("     ✅ Đăng ký user mới thành công");
                
                // Test login với user mới
                authController.logout();
                boolean newUserLogin = authController.login("testuser", "testpass");
                if (newUserLogin) {
                    System.out.println("     ✅ Login với user mới thành công");
                    System.out.println("     - User: " + authController.getCurrentUser().getFullName());
                    System.out.println("     - Là admin: " + authController.isCurrentUserAdmin());
                } else {
                    System.out.println("     ❌ Login với user mới thất bại");
                }
            } else {
                System.out.println("     ❌ Đăng ký user mới thất bại");
            }
        }
        
        // Test logout
        System.out.println("   - Kiểm tra logout:");
        authController.logout();
        if (!authController.isLoggedIn()) {
            System.out.println("     ✅ Logout thành công");
        } else {
            System.out.println("     ❌ Logout thất bại");
        }
        
        System.out.println();
    }
    
    private static void testUsersFile() {
        System.out.println("3. KIỂM TRA FILE users.txt:");
        
        try {
            java.io.File usersFile = new java.io.File("data/users.txt");
            if (usersFile.exists()) {
                System.out.println("   ✅ File users.txt tồn tại");
                System.out.println("   - Đường dẫn: " + usersFile.getAbsolutePath());
                System.out.println("   - Kích thước: " + usersFile.length() + " bytes");
                
                // Đọc và hiển thị nội dung
                System.out.println("   - Nội dung file:");
                java.util.Scanner scanner = new java.util.Scanner(usersFile, "UTF-8");
                int lineNumber = 1;
                while (scanner.hasNextLine()) {
                    String line = scanner.nextLine();
                    if (!line.trim().isEmpty()) {
                        System.out.println("     " + lineNumber + ". " + line);
                        lineNumber++;
                    }
                }
                scanner.close();
            } else {
                System.out.println("   ❌ File users.txt không tồn tại");
            }
        } catch (Exception e) {
            System.out.println("   ❌ Lỗi khi kiểm tra file: " + e.getMessage());
        }
        
        System.out.println();
    }
}
