package com.mycompany.quanlydoituongdacbiet.model;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Class đại diện cho điểm thi
 */
public class DiemThi {
    private String soBaoDanh;
    private String maMon;
    private double diem;
    private Date ngayThi;
    
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
    
    // Constructor mặc định
    public DiemThi() {}
    
    // Constructor đầy đủ
    public DiemThi(String soBaoDanh, String maMon, double diem, Date ngayThi) {
        this.soBaoDanh = soBaoDanh;
        this.maMon = maMon;
        this.diem = diem;
        this.ngayThi = ngayThi;
    }
    
    /**
     * Chuyển đổi từ string trong file thành object
     */
    public static DiemThi fromString(String line) {
        try {
            String[] parts = line.split("\\|");
            if (parts.length >= 4) {
                DiemThi diemThi = new DiemThi();
                diemThi.soBaoDanh = parts[0];
                diemThi.maMon = parts[1];
                diemThi.diem = Double.parseDouble(parts[2]);
                diemThi.ngayThi = dateFormat.parse(parts[3]);
                return diemThi;
            }
        } catch (Exception e) {
            System.err.println("Lỗi parse DiemThi: " + line);
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Chuyển đổi object thành string để lưu vào file
     */
    @Override
    public String toString() {
        return soBaoDanh + "|" + maMon + "|" + diem + "|" + 
               dateFormat.format(ngayThi);
    }
    
    // Getters and Setters
    public String getSoBaoDanh() { return soBaoDanh; }
    public void setSoBaoDanh(String soBaoDanh) { this.soBaoDanh = soBaoDanh; }
    
    public String getMaMon() { return maMon; }
    public void setMaMon(String maMon) { this.maMon = maMon; }
    
    public double getDiem() { return diem; }
    public void setDiem(double diem) { this.diem = diem; }
    
    public Date getNgayThi() { return ngayThi; }
    public void setNgayThi(Date ngayThi) { this.ngayThi = ngayThi; }
    
    /**
     * Kiểm tra điểm có hợp lệ không (0-10)
     */
    public boolean isValidDiem() {
        return diem >= 0 && diem <= 10;
    }
    
    /**
     * Lấy xếp loại theo điểm
     */
    public String getXepLoai() {
        if (diem >= 8.5) return "Giỏi";
        if (diem >= 7.0) return "Khá";
        if (diem >= 5.0) return "Trung bình";
        if (diem >= 3.5) return "Yếu";
        return "Kém";
    }
}