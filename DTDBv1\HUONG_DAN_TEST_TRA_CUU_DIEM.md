# HƯỚNG DẪN TEST MODULE TRA CỨU ĐIỂM THI

## Tổng quan
<PERSON> "Tra cứu điểm thi" cho phép tra cứu toàn bộ điểm thi của thí sinh theo số báo danh và tính tổng điểm theo khối thi đã chọn.

## Cách khởi động ứng dụng
```bash
cd DTDBv1/src/main/java
java com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong.MainApplication
```

## Tài khoản đăng nhập mặc định
- **Username:** admin
- **Password:** admin

## Các bước test chức năng

### 1. Đ<PERSON>ng nhập và truy cập module
1. Khởi động ứng dụng
2. Đăng nhập với tài khoản admin/admin
3. Trong menu chính, click nút **"Tra cứu Điểm thi"** (m<PERSON><PERSON> tí<PERSON>)
4. <PERSON><PERSON><PERSON> sổ tra cứu điểm thi sẽ mở ra

### 2. Test giao diện
- **Kiểm tra layout:** Giao diện có 4 phần chính:
  - Panel tìm kiếm (trên cùng)
  - Thông tin thí sinh (bên trái)
  - Bảng điểm chi tiết (giữa)
  - Panel khối thi và thống kê (bên phải)

- **Kiểm tra màu sắc:** Giao diện sử dụng màu chủ đạo #0071F0 (xanh dương)

### 3. Test chức năng tìm kiếm thí sinh

#### Test case 1: Tìm kiếm thành công
1. Nhập số báo danh hợp lệ (ví dụ: "SBD001")
2. Click nút "Tra cứu"
3. **Kết quả mong đợi:**
   - Thông tin thí sinh hiển thị đầy đủ
   - Bảng điểm hiển thị tất cả môn thi của thí sinh
   - Thống kê cập nhật (tổng môn, điểm TB, cao nhất, thấp nhất)

#### Test case 2: Tìm kiếm không tìm thấy
1. Nhập số báo danh không tồn tại (ví dụ: "INVALID")
2. Click nút "Tra cứu"
3. **Kết quả mong đợi:**
   - Hiển thị thông báo "Không tìm thấy thí sinh"
   - Các panel thông tin được xóa sạch

#### Test case 3: Tìm kiếm với dữ liệu rỗng
1. Để trống ô số báo danh
2. Click nút "Tra cứu"
3. **Kết quả mong đợi:**
   - Hiển thị thông báo lỗi "Vui lòng nhập số báo danh"

### 4. Test chức năng tính tổng điểm khối thi

#### Test case 1: Chọn khối thi có đủ điểm
1. Tìm kiếm một thí sinh có điểm đầy đủ
2. Chọn một khối thi từ ComboBox
3. **Kết quả mong đợi:**
   - Hiển thị "Tổng điểm khối thi: [số điểm]"
   - Số điểm là tổng của 3 môn trong khối thi

#### Test case 2: Chọn khối thi thiếu điểm
1. Tìm kiếm thí sinh có điểm không đầy đủ cho khối thi
2. Chọn khối thi đó
3. **Kết quả mong đợi:**
   - Hiển thị "Tổng điểm khối thi: Chưa đủ điểm"

### 5. Test chức năng làm mới
1. Thực hiện tìm kiếm
2. Click nút "Làm mới"
3. **Kết quả mong đợi:**
   - Tất cả dữ liệu được xóa sạch
   - Form trở về trạng thái ban đầu

### 6. Test dữ liệu mẫu

#### Dữ liệu thí sinh mẫu (thisinhs.txt):
```
SBD001|Nguyễn Văn A|01/01/2005|Nam|Hà Nội|A01
SBD002|Trần Thị B|02/02/2005|Nữ|TP.HCM|A02
```

#### Dữ liệu môn thi mẫu (monthis.txt):
```
TOAN|Toán học|180
LY|Vật lý|90
HOA|Hóa học|90
SINH|Sinh học|90
VAN|Ngữ văn|180
SU|Lịch sử|90
DIA|Địa lý|90
GDCD|GDCD|90
NGOAINGU|Ngoại ngữ|90
```

#### Dữ liệu khối thi mẫu (khoithis.txt):
```
A00|Khối A00|TOAN,LY,HOA
A01|Khối A01|TOAN,LY,NGOAINGU
B00|Khối B00|TOAN,HOA,SINH
C00|Khối C00|VAN,SU,DIA
D01|Khối D01|TOAN,VAN,NGOAINGU
```

#### Dữ liệu điểm thi mẫu (diemthis.txt):
```
SBD001|TOAN|8.5|2024-06-28
SBD001|LY|7.0|2024-06-29
SBD001|HOA|8.0|2024-06-30
SBD001|NGOAINGU|9.0|2024-07-01
SBD002|TOAN|9.0|2024-06-28
SBD002|VAN|8.5|2024-06-29
```

### 7. Test scenarios cụ thể

#### Scenario 1: Tra cứu SBD001 - Khối A01
1. Tìm kiếm "SBD001"
2. Chọn khối "A01" (Toán, Lý, Ngoại ngữ)
3. **Kết quả mong đợi:** Tổng điểm = 8.5 + 7.0 + 9.0 = 24.5

#### Scenario 2: Tra cứu SBD002 - Khối A01
1. Tìm kiếm "SBD002"
2. Chọn khối "A01"
3. **Kết quả mong đợi:** "Chưa đủ điểm" (thiếu điểm Lý)

### 8. Test tích hợp với các module khác

#### Test 1: Thêm điểm mới từ module Quản lý điểm thi
1. Mở module "Quản lý điểm thi"
2. Thêm điểm mới cho thí sinh
3. Quay lại module "Tra cứu điểm thi"
4. Tìm kiếm thí sinh đó
5. **Kết quả mong đợi:** Điểm mới được hiển thị

#### Test 2: Thêm khối thi mới từ module Quản lý khối thi
1. Mở module "Quản lý khối thi"
2. Thêm khối thi mới
3. Quay lại module "Tra cứu điểm thi"
4. **Kết quả mong đợi:** Khối thi mới xuất hiện trong ComboBox

### 9. Test hiệu năng và ổn định
- Test với nhiều thí sinh (>100 records)
- Test tìm kiếm liên tục nhiều lần
- Test chuyển đổi khối thi nhanh chóng
- Test đóng/mở cửa sổ nhiều lần

### 10. Lỗi thường gặp và cách khắc phục

#### Lỗi 1: Không hiển thị dữ liệu
- **Nguyên nhân:** File dữ liệu không tồn tại hoặc bị lỗi format
- **Khắc phục:** Kiểm tra file trong thư mục data/

#### Lỗi 2: Tổng điểm không chính xác
- **Nguyên nhân:** Dữ liệu khối thi hoặc điểm thi bị sai
- **Khắc phục:** Kiểm tra dữ liệu trong khoithis.txt và diemthis.txt

#### Lỗi 3: Giao diện bị lỗi hiển thị
- **Nguyên nhân:** Look and Feel không tương thích
- **Khắc phục:** Khởi động lại ứng dụng

## Kết luận
Module "Tra cứu điểm thi" đã được tích hợp hoàn chỉnh với các tính năng:
- ✅ Tìm kiếm thí sinh theo số báo danh
- ✅ Hiển thị toàn bộ điểm thi của thí sinh
- ✅ Tính tổng điểm theo khối thi đã chọn
- ✅ Hiển thị thống kê điểm số
- ✅ Giao diện đẹp, thân thiện
- ✅ Tích hợp với các module khác

Tất cả chức năng đã hoạt động ổn định và đáp ứng yêu cầu của người dùng.
