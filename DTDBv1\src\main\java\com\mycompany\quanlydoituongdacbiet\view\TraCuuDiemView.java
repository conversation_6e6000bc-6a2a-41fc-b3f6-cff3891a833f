package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.DiemThi;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * View cho tra cứu điểm thi
 */
public class TraCuuDiemView extends JFrame {
    
    // Components cho tìm kiếm
    private JTextField txtSoBaoDanh;
    private JButton btnTraCuu, btnLamMoi;
    
    // Components cho thông tin thí sinh
    private JLabel lblHoTen, lblGioiTinh, lblNgaySinh, lblDiaChi, lblKhoiThi;
    
    // Components cho khối thi
    private JComboBox<String> cmbKhoiThi;
    private JLabel lblTongDiemKhoi;
    
    // Components cho bảng điểm
    private JTable tableDiem;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    
    // Components cho thống kê
    private JLabel lblTongSoMon, lblDiemTrungBinh, lblDiemCao, lblDiemThap;
    
    // Status
    private JLabel lblStatus;
    
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
    
    public TraCuuDiemView() {
        initComponents();
        setupLayout();
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setTitle("Tra cứu điểm thi");
        setSize(1000, 700);
        setLocationRelativeTo(null);
        

    }
    
    private void initComponents() {
        // Tìm kiếm
        txtSoBaoDanh = new JTextField(15);
        btnTraCuu = new JButton("Tra cứu");
        btnLamMoi = new JButton("Làm mới");
        
        // Thông tin thí sinh
        lblHoTen = new JLabel("Chưa có thông tin");
        lblGioiTinh = new JLabel("Chưa có thông tin");
        lblNgaySinh = new JLabel("Chưa có thông tin");
        lblDiaChi = new JLabel("Chưa có thông tin");
        lblKhoiThi = new JLabel("Chưa có thông tin");
        
        // Khối thi và tổng điểm
        cmbKhoiThi = new JComboBox<>();
        lblTongDiemKhoi = new JLabel("Tổng điểm khối thi: Chưa chọn khối");
        
        // Bảng điểm
        String[] columnNames = {"Mã môn", "Tên môn", "Điểm", "Ngày thi", "Xếp loại"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        tableDiem = new JTable(tableModel);
        scrollPane = new JScrollPane(tableDiem);
        
        // Thống kê
        lblTongSoMon = new JLabel("Tổng số môn: 0");
        lblDiemTrungBinh = new JLabel("Điểm trung bình: 0.0");
        lblDiemCao = new JLabel("Điểm cao nhất: 0.0");
        lblDiemThap = new JLabel("Điểm thấp nhất: 0.0");
        
        // Status
        lblStatus = new JLabel("Nhập số báo danh và bấm Tra cứu");
        
        // Styling
        styleComponents();
    }
    
    private void styleComponents() {
        Color primaryColor = new Color(0, 113, 240); // #0071F0
        Color backgroundColor = Color.WHITE;
        Color textColor = Color.BLACK;
        Font labelFont = new Font("Arial", Font.BOLD, 14);
        Font dataFont = new Font("Arial", Font.PLAIN, 13);
        
        // Style buttons
        btnTraCuu.setBackground(primaryColor);
        btnTraCuu.setForeground(Color.WHITE);
        btnTraCuu.setFont(labelFont);
        btnTraCuu.setPreferredSize(new Dimension(120, 35));
        
        btnLamMoi.setBackground(Color.GRAY);
        btnLamMoi.setForeground(Color.WHITE);
        btnLamMoi.setFont(labelFont);
        btnLamMoi.setPreferredSize(new Dimension(120, 35));
        
        // Style text field
        txtSoBaoDanh.setFont(dataFont);
        txtSoBaoDanh.setPreferredSize(new Dimension(200, 30));
        
        // Style labels
        lblHoTen.setFont(dataFont);
        lblGioiTinh.setFont(dataFont);
        lblNgaySinh.setFont(dataFont);
        lblDiaChi.setFont(dataFont);
        lblKhoiThi.setFont(dataFont);
        lblTongDiemKhoi.setFont(new Font("Arial", Font.BOLD, 16));
        lblTongDiemKhoi.setForeground(primaryColor);
        
        // Style ComboBox
        cmbKhoiThi.setFont(dataFont);
        cmbKhoiThi.setPreferredSize(new Dimension(250, 30));
        
        // Style table
        tableDiem.setFont(dataFont);
        tableDiem.getTableHeader().setFont(labelFont);
        tableDiem.getTableHeader().setBackground(primaryColor);
        tableDiem.getTableHeader().setForeground(Color.WHITE);
        tableDiem.setRowHeight(25);
        tableDiem.setSelectionBackground(new Color(230, 240, 255));
        
        // Style thống kê labels
        lblTongSoMon.setFont(dataFont);
        lblDiemTrungBinh.setFont(dataFont);
        lblDiemCao.setFont(dataFont);
        lblDiemThap.setFont(dataFont);
        
        // Style status
        lblStatus.setFont(dataFont);
        lblStatus.setForeground(Color.BLUE);
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));
        
        // Top panel - Tìm kiếm
        JPanel topPanel = createSearchPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // Center panel - Thông tin và bảng điểm
        JPanel centerPanel = createCenterPanel();
        add(centerPanel, BorderLayout.CENTER);
        
        // Bottom panel - Status
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        bottomPanel.setBorder(BorderFactory.createTitledBorder("Trạng thái"));
        bottomPanel.add(lblStatus);
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private JPanel createSearchPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBorder(BorderFactory.createTitledBorder("Tìm kiếm thí sinh"));
        panel.setBackground(Color.WHITE);
        
        JPanel inputPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 10));
        inputPanel.setBackground(Color.WHITE);
        
        inputPanel.add(new JLabel("Số báo danh:"));
        inputPanel.add(txtSoBaoDanh);
        inputPanel.add(btnTraCuu);
        inputPanel.add(btnLamMoi);
        
        panel.add(inputPanel, BorderLayout.CENTER);
        return panel;
    }
    
    private JPanel createCenterPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        
        // Left panel - Thông tin thí sinh
        JPanel leftPanel = createThiSinhInfoPanel();
        
        // Right panel - Bảng điểm và thống kê
        JPanel rightPanel = createDiemPanel();
        
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, leftPanel, rightPanel);
        splitPane.setDividerLocation(350);
        splitPane.setResizeWeight(0.35);
        
        panel.add(splitPane, BorderLayout.CENTER);
        return panel;
    }
    
    private JPanel createThiSinhInfoPanel() {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createTitledBorder("Thông tin thí sinh"));
        panel.setBackground(Color.WHITE);
        
        // Thông tin cơ bản
        JPanel infoPanel = new JPanel(new GridBagLayout());
        infoPanel.setBackground(Color.WHITE);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Row 0
        gbc.gridx = 0; gbc.gridy = 0;
        infoPanel.add(new JLabel("Họ tên:"), gbc);
        gbc.gridx = 1;
        infoPanel.add(lblHoTen, gbc);
        
        // Row 1
        gbc.gridx = 0; gbc.gridy = 1;
        infoPanel.add(new JLabel("Giới tính:"), gbc);
        gbc.gridx = 1;
        infoPanel.add(lblGioiTinh, gbc);
        
        // Row 2
        gbc.gridx = 0; gbc.gridy = 2;
        infoPanel.add(new JLabel("Ngày sinh:"), gbc);
        gbc.gridx = 1;
        infoPanel.add(lblNgaySinh, gbc);
        
        // Row 3
        gbc.gridx = 0; gbc.gridy = 3;
        infoPanel.add(new JLabel("Địa chỉ:"), gbc);
        gbc.gridx = 1;
        infoPanel.add(lblDiaChi, gbc);
        
        // Row 4
        gbc.gridx = 0; gbc.gridy = 4;
        infoPanel.add(new JLabel("Khối thi:"), gbc);
        gbc.gridx = 1;
        infoPanel.add(lblKhoiThi, gbc);
        
        panel.add(infoPanel, BorderLayout.NORTH);
        
        // Khối thi selection và tổng điểm
        JPanel khoiPanel = createKhoiThiPanel();
        panel.add(khoiPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createKhoiThiPanel() {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createTitledBorder("Tính điểm khối thi"));
        panel.setBackground(Color.WHITE);
        
        JPanel selectPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        selectPanel.setBackground(Color.WHITE);
        selectPanel.add(new JLabel("Chọn khối thi:"));
        selectPanel.add(cmbKhoiThi);
        
        JPanel resultPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        resultPanel.setBackground(Color.WHITE);
        resultPanel.add(lblTongDiemKhoi);
        
        panel.add(selectPanel, BorderLayout.NORTH);
        panel.add(resultPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createDiemPanel() {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        
        // Bảng điểm
        JPanel tablePanel = new JPanel(new BorderLayout());
        tablePanel.setBorder(BorderFactory.createTitledBorder("Bảng điểm chi tiết"));
        tablePanel.add(scrollPane, BorderLayout.CENTER);
        
        // Thống kê
        JPanel statsPanel = createStatsPanel();
        
        panel.add(tablePanel, BorderLayout.CENTER);
        panel.add(statsPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createStatsPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 2, 10, 5));
        panel.setBorder(BorderFactory.createTitledBorder("Thống kê"));
        panel.setBackground(Color.WHITE);
        
        panel.add(lblTongSoMon);
        panel.add(lblDiemTrungBinh);
        panel.add(lblDiemCao);
        panel.add(lblDiemThap);
        
        return panel;
    }
    
    // Event listeners
    public void addTraCuuListener(ActionListener listener) {
        btnTraCuu.addActionListener(listener);
    }
    
    public void addLamMoiListener(ActionListener listener) {
        btnLamMoi.addActionListener(listener);
    }
    
    public void addKhoiThiSelectionListener(ActionListener listener) {
        cmbKhoiThi.addActionListener(listener);
    }
    
    // Getters
    public String getSoBaoDanh() {
        return txtSoBaoDanh.getText().trim();
    }
    
    public String getSelectedKhoiThi() {
        String selected = (String) cmbKhoiThi.getSelectedItem();
        return selected != null ? selected.split(" - ")[0] : null;
    }
    
    public JTable getTableDiem() {
        return tableDiem;
    }
    
    public DefaultTableModel getTableModel() {
        return tableModel;
    }
    
    // Setters
    public void setSoBaoDanh(String soBaoDanh) {
        txtSoBaoDanh.setText(soBaoDanh);
    }
    
    public void setThiSinhInfo(ThiSinh thiSinh) {
        if (thiSinh != null) {
            lblHoTen.setText(thiSinh.getHoTen());
            lblGioiTinh.setText(thiSinh.getGioiTinh());
            lblNgaySinh.setText(dateFormat.format(thiSinh.getNgaySinh()));
            lblDiaChi.setText(thiSinh.getDiaChi());
            lblKhoiThi.setText(thiSinh.getMaKhoi());
        } else {
            clearThiSinhInfo();
        }
    }
    
    public void clearThiSinhInfo() {
        lblHoTen.setText("Chưa có thông tin");
        lblGioiTinh.setText("Chưa có thông tin");
        lblNgaySinh.setText("Chưa có thông tin");
        lblDiaChi.setText("Chưa có thông tin");
        lblKhoiThi.setText("Chưa có thông tin");
    }
    
    public void loadKhoiThiData(String[] khoiThis) {
        cmbKhoiThi.removeAllItems();
        cmbKhoiThi.addItem("-- Chọn khối thi --");
        for (String khoi : khoiThis) {
            cmbKhoiThi.addItem(khoi);
        }
    }
    
    public void setTongDiemKhoi(String khoiThi, double tongDiem) {
        if (khoiThi != null && !khoiThi.isEmpty()) {
            lblTongDiemKhoi.setText(String.format("Tổng điểm khối thi %s: %.2f", khoiThi, tongDiem));
        } else {
            lblTongDiemKhoi.setText("Tổng điểm khối thi: Chưa chọn khối");
        }
    }
    
    public void updateStats(int tongSoMon, double diemTrungBinh, double diemCao, double diemThap) {
        lblTongSoMon.setText("Tổng số môn: " + tongSoMon);
        lblDiemTrungBinh.setText(String.format("Điểm trung bình: %.2f", diemTrungBinh));
        lblDiemCao.setText(String.format("Điểm cao nhất: %.2f", diemCao));
        lblDiemThap.setText(String.format("Điểm thấp nhất: %.2f", diemThap));
    }
    
    public void clearData() {
        tableModel.setRowCount(0);
        clearThiSinhInfo();
        cmbKhoiThi.setSelectedIndex(0);
        lblTongDiemKhoi.setText("Tổng điểm khối thi: Chưa chọn khối");
        updateStats(0, 0.0, 0.0, 0.0);
    }
    
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }
    
    public void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }
    
    public void showSuccess(String message) {
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }
    
    public void showInfo(String message) {
        JOptionPane.showMessageDialog(this, message, "Thông báo", JOptionPane.INFORMATION_MESSAGE);
    }
}
