# HƯỚNG DẪN HỆ THỐNG PHÂN QUYỀN

## 🔐 Tổng quan hệ thống phân quyền

Hệ thống quản lý điểm thi đại học hiện đã được tích hợp phân quyền với 2 loại tài khoản:

### 👨‍💼 ADMIN (Quản trị viên)
- **Tài khoản mặc định:** `admin` / `admin`
- **Quyền hạn:** Toàn quyền quản lý hệ thống
- **Chức năng có thể truy cập:**
  - ✅ Quản lý Thí sinh
  - ✅ Quản lý Môn thi  
  - ✅ Quản lý Khối thi
  - ✅ Quản lý Điểm thi
  - ✅ Tra cứu Điểm thi
  - ✅ **Quản lý Người dùng** (Mới)
  - ✅ Đăng xuất

### 👤 USER (Người dùng)
- **Tạo tài khoản:** Thông qua nút "Đăng ký"
- **Quyề<PERSON> hạn:** Chỉ tra cứu điểm thi
- **Chứ<PERSON> năng có thể truy cập:**
  - ✅ Tra cứu Điểm thi
  - ✅ Đăng xuất

---

## 🚀 Cách sử dụng hệ thống

### 1. Khởi động ứng dụng
```bash
cd DTDBv1/src/main/java
java com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong.MainApplication
```

### 2. Đăng nhập với tài khoản ADMIN
- **Username:** `admin`
- **Password:** `admin`
- **Kết quả:** Hiển thị menu đầy đủ với 7 chức năng

### 3. Tạo tài khoản USER mới
1. Click nút **"Đăng ký"** trên màn hình đăng nhập
2. Nhập thông tin:
   - Tên đăng nhập (ít nhất 3 ký tự)
   - Mật khẩu (ít nhất 4 ký tự)
   - Họ tên đầy đủ
   - Email (tùy chọn)
3. Click **"Đăng ký"**
4. Hệ thống tự động tạo tài khoản với role **USER**

### 4. Đăng nhập với tài khoản USER
- Sử dụng thông tin vừa đăng ký
- **Kết quả:** Chỉ hiển thị 2 chức năng: "Tra cứu Điểm thi" và "Đăng xuất"

---

## 🛠️ Quản lý Người dùng (Chỉ dành cho ADMIN)

### Truy cập module
1. Đăng nhập với tài khoản admin
2. Click nút **"Quản lý Người dùng"** (màu đỏ)

### Chức năng có sẵn

#### ➕ Thêm người dùng mới
1. Click nút **"Thêm mới"**
2. Nhập thông tin:
   - **Tên đăng nhập:** Duy nhất, ít nhất 3 ký tự
   - **Mật khẩu:** Ít nhất 4 ký tự (hiển thị rõ)
   - **Họ tên:** Bắt buộc
   - **Email:** Tùy chọn
   - **Vai trò:** USER hoặc ADMIN
3. Click **"Lưu"**

#### ✏️ Sửa thông tin người dùng
1. Chọn người dùng trong bảng
2. Click nút **"Sửa"**
3. Chỉnh sửa thông tin (không thể sửa tên đăng nhập)
4. Click **"Lưu"**

#### 🗑️ Xóa người dùng
1. Chọn người dùng trong bảng
2. Click nút **"Xóa"**
3. Xác nhận xóa
4. **Lưu ý:** Không thể xóa admin cuối cùng

#### 🔍 Tìm kiếm người dùng
1. Nhập từ khóa vào ô "Tìm kiếm"
2. Click **"Tìm kiếm"**
3. Hệ thống tìm theo: tên đăng nhập, họ tên, email, vai trò

#### 🔄 Làm mới dữ liệu
- Click nút **"Làm mới"** để tải lại toàn bộ danh sách

### Đặc điểm của bảng quản lý
- **Hiển thị mật khẩu:** Theo yêu cầu, mật khẩu được hiển thị rõ trong bảng
- **Cột thông tin:** Tên đăng nhập | Mật khẩu | Họ tên | Email | Vai trò
- **Sắp xếp:** Có thể click vào header để sắp xếp
- **Chọn dòng:** Click vào dòng để chọn và kích hoạt nút Sửa/Xóa

---

## 🔒 Bảo mật và phân quyền

### Kiểm tra quyền truy cập
- Hệ thống tự động kiểm tra role của user khi đăng nhập
- Chỉ ADMIN mới thấy nút "Quản lý Người dùng"
- USER chỉ có quyền tra cứu điểm thi

### Bảo vệ chức năng quan trọng
- Không thể xóa admin cuối cùng trong hệ thống
- Kiểm tra quyền admin trước khi mở module quản lý người dùng
- Validation đầy đủ cho dữ liệu đầu vào

### Lưu trữ dữ liệu
- File lưu trữ: `data/users.txt`
- Format: `username|password|fullName|email|role`
- Encoding: UTF-8 (hỗ trợ tiếng Việt)

---

## 🧪 Test scenarios

### Test 1: Đăng nhập ADMIN
1. Khởi động ứng dụng
2. Đăng nhập: admin/admin
3. **Kết quả mong đợi:** Menu 7 nút (bao gồm "Quản lý Người dùng")

### Test 2: Tạo và đăng nhập USER
1. Click "Đăng ký"
2. Tạo tài khoản: user1/1234/Nguyen Van A
3. Đăng nhập với tài khoản vừa tạo
4. **Kết quả mong đợi:** Menu 2 nút (chỉ "Tra cứu Điểm thi" và "Đăng xuất")

### Test 3: Quản lý người dùng
1. Đăng nhập admin
2. Mở "Quản lý Người dùng"
3. Thêm/sửa/xóa người dùng
4. **Kết quả mong đợi:** Tất cả chức năng hoạt động bình thường

### Test 4: Kiểm tra bảo mật
1. Đăng nhập USER
2. Thử truy cập trực tiếp các chức năng quản lý
3. **Kết quả mong đợi:** Không thể truy cập, chỉ thấy menu hạn chế

---

## 🎯 Lợi ích của hệ thống phân quyền

### Cho tổ chức
- **Bảo mật:** Chỉ admin mới có quyền quản lý dữ liệu
- **Kiểm soát:** Phân quyền rõ ràng theo vai trò
- **Linh hoạt:** Dễ dàng tạo tài khoản cho nhiều người dùng

### Cho người dùng
- **USER:** Tra cứu điểm thi nhanh chóng, không cần quyền admin
- **ADMIN:** Toàn quyền quản lý hệ thống và người dùng

### Cho hệ thống
- **Mở rộng:** Dễ dàng thêm role mới trong tương lai
- **Bảo trì:** Quản lý tập trung tất cả tài khoản
- **Audit:** Theo dõi được ai đang sử dụng hệ thống

---

## 🔧 Troubleshooting

### Lỗi thường gặp

#### 1. Không thể đăng nhập admin
- **Nguyên nhân:** File users.txt bị lỗi hoặc không tồn tại
- **Khắc phục:** Xóa file `data/users.txt`, khởi động lại ứng dụng

#### 2. Không thấy nút "Quản lý Người dùng"
- **Nguyên nhân:** Đăng nhập với tài khoản USER
- **Khắc phục:** Đăng xuất và đăng nhập lại với admin/admin

#### 3. Lỗi khi tạo tài khoản mới
- **Nguyên nhân:** Tên đăng nhập đã tồn tại hoặc không đủ ký tự
- **Khắc phục:** Kiểm tra validation và thử tên khác

#### 4. Không thể xóa admin
- **Nguyên nhân:** Đây là admin cuối cùng trong hệ thống
- **Khắc phục:** Tạo admin khác trước khi xóa

---

## 📞 Hỗ trợ

Nếu gặp vấn đề khi sử dụng hệ thống phân quyền:

1. **Kiểm tra log:** Xem thông báo lỗi trên console
2. **Kiểm tra file:** Đảm bảo thư mục `data/` tồn tại
3. **Reset hệ thống:** Xóa file `data/users.txt` để tạo lại admin mặc định
4. **Backup dữ liệu:** Sao lưu file `data/users.txt` trước khi thực hiện thay đổi lớn

---

## ✅ Kết luận

Hệ thống phân quyền đã được tích hợp hoàn chỉnh với các tính năng:

- ✅ **Phân quyền rõ ràng:** ADMIN vs USER
- ✅ **Đăng ký tự động:** Tạo USER role mặc định
- ✅ **Quản lý người dùng:** CRUD đầy đủ với hiển thị mật khẩu
- ✅ **Bảo mật:** Kiểm tra quyền truy cập
- ✅ **Giao diện thân thiện:** Menu khác nhau theo role
- ✅ **Tích hợp hoàn chỉnh:** Hoạt động với tất cả module hiện có

Hệ thống đã sẵn sàng để triển khai và sử dụng trong môi trường thực tế!
