package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.DiemThi;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Màn hình quản lý điểm thi
 */
public class DiemThiManagementView extends JFrame {

    // Colors
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240);
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69);
    private static final Color WARNING_COLOR = new Color(255, 193, 7);
    private static final Color DANGER_COLOR = new Color(220, 53, 69);

    // Components cho form nhập liệu
    private JComboBox<String> cmbSoBaoDanh, cmbMaMon;
    private JSpinner spnDiem, spnNgayThi;

    // Components cho bảng dữ liệu
    private JTable tableDiemThi;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;

    // Components cho tìm kiếm
    private JTextField txtTimKiem;
    private JComboBox<String> cmbTimKiemTheo;
    private JButton btnTimKiem, btnLamMoi;

    // Components cho thao tác
    private JButton btnThem, btnSua, btnXoa, btnLuu, btnHuy;

    // Statistics labels
    private JLabel lblTongSoLuong, lblDiemTrungBinh, lblDiemCao, lblDiemThap;

    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingSoBaoDanh = null;
    private String editingMaMon = null;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

    public DiemThiManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupEvents();
        setupWindow();
        resetForm();
    }

    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components
        cmbSoBaoDanh = new JComboBox<>();
        cmbMaMon = new JComboBox<>();

        // Spinner cho điểm (0-10, step 0.25)
        spnDiem = new JSpinner(new SpinnerNumberModel(0.0, 0.0, 10.0, 0.25));

        // Spinner cho ngày thi
        spnNgayThi = new JSpinner(new SpinnerDateModel());
        JSpinner.DateEditor dateEditor = new JSpinner.DateEditor(spnNgayThi, "dd/MM/yyyy");
        spnNgayThi.setEditor(dateEditor);

        // Search components
        txtTimKiem = new JTextField(20);
        cmbTimKiemTheo = new JComboBox<>(new String[]{"Tất cả", "Số báo danh", "Mã môn", "Điểm"});
        btnTimKiem = new JButton("Tìm kiếm");
        btnLamMoi = new JButton("Làm mới");

        // Action buttons
        btnThem = new JButton("Thêm điểm thi");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        btnLuu = new JButton("Lưu");
        btnHuy = new JButton("Hủy");

        // Statistics labels
        lblTongSoLuong = new JLabel("Tổng số lượng: 0");
        lblDiemTrungBinh = new JLabel("Điểm trung bình: 0.0");
        lblDiemCao = new JLabel("Điểm cao nhất: 0.0");
        lblDiemThap = new JLabel("Điểm thấp nhất: 0.0");

        // Status
        lblStatus = new JLabel("Sẵn sàng");

        // Table
        String[] columnNames = {"Số báo danh", "Mã môn", "Tên môn", "Điểm", "Ngày thi", "Xếp loại"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho edit trực tiếp trên table
            }
        };
        tableDiemThi = new JTable(tableModel);
        scrollPane = new JScrollPane(tableDiemThi);

        // Style components
        styleComponents();
    }

    /**
     * Style các components
     */
    private void styleComponents() {
        // Set fonts
        Font titleFont = new Font("Arial", Font.BOLD, 24);
        Font labelFont = new Font("Arial", Font.PLAIN, 14);
        Font buttonFont = new Font("Arial", Font.BOLD, 14);

        // ComboBox styling
        cmbSoBaoDanh.setFont(labelFont);
        cmbSoBaoDanh.setPreferredSize(new Dimension(200, 35));
        cmbMaMon.setFont(labelFont);
        cmbMaMon.setPreferredSize(new Dimension(200, 35));
        cmbTimKiemTheo.setFont(labelFont);

        // Spinner styling
        spnDiem.setFont(labelFont);
        spnDiem.setPreferredSize(new Dimension(150, 35));
        spnNgayThi.setFont(labelFont);
        spnNgayThi.setPreferredSize(new Dimension(150, 35));

        // Text field styling
        txtTimKiem.setFont(labelFont);
        txtTimKiem.setPreferredSize(new Dimension(200, 35));

        // Button styling
        styleButton(btnThem, PRIMARY_COLOR);
        styleButton(btnSua, WARNING_COLOR);
        styleButton(btnXoa, DANGER_COLOR);
        styleButton(btnLuu, SUCCESS_COLOR);
        styleButton(btnHuy, Color.GRAY);
        styleButton(btnTimKiem, PRIMARY_COLOR);
        styleButton(btnLamMoi, Color.GRAY);

        // Statistics labels styling
        lblTongSoLuong.setFont(labelFont);
        lblDiemTrungBinh.setFont(labelFont);
        lblDiemCao.setFont(labelFont);
        lblDiemThap.setFont(labelFont);

        // Status label styling
        lblStatus.setFont(labelFont);
        lblStatus.setForeground(PRIMARY_COLOR);

        // Table styling
        tableDiemThi.setFont(labelFont);
        tableDiemThi.setRowHeight(25);
        tableDiemThi.getTableHeader().setFont(new Font("Arial", Font.BOLD, 14));
        tableDiemThi.getTableHeader().setBackground(PRIMARY_COLOR);
        tableDiemThi.getTableHeader().setForeground(Color.WHITE);
        tableDiemThi.setSelectionBackground(new Color(184, 207, 229));
    }

    /**
     * Style button với màu sắc
     */
    private void styleButton(JButton button, Color bgColor) {
        button.setFont(new Font("Arial", Font.BOLD, 14));
        button.setBackground(bgColor);
        button.setForeground(Color.WHITE);
        button.setPreferredSize(new Dimension(120, 35));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setOpaque(true);
    }

    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());

        // Title panel
        JPanel titlePanel = createTitlePanel();
        add(titlePanel, BorderLayout.NORTH);

        // Main content panel
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBackground(Color.WHITE);
        contentPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Form panel
        JPanel formPanel = createFormPanel();
        contentPanel.add(formPanel, BorderLayout.NORTH);

        // Table panel
        JPanel tablePanel = createTablePanel();
        contentPanel.add(tablePanel, BorderLayout.CENTER);

        // Button panel
        JPanel buttonPanel = createButtonPanel();
        contentPanel.add(buttonPanel, BorderLayout.SOUTH);

        add(contentPanel, BorderLayout.CENTER);

        // Status panel
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }

    /**
     * Tạo title panel
     */
    private JPanel createTitlePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(PRIMARY_COLOR);
        panel.setBorder(BorderFactory.createEmptyBorder(15, 20, 15, 20));

        JLabel titleLabel = new JLabel("QUẢN LÝ ĐIỂM THI", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);

        panel.add(titleLabel, BorderLayout.CENTER);
        return panel;
    }

    /**
     * Tạo form panel
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(PRIMARY_COLOR, 2),
            "Thông tin điểm thi",
            0, 0,
            new Font("Arial", Font.BOLD, 16),
            PRIMARY_COLOR
        ));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.WEST;

        // Row 1: Số báo danh và Mã môn
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("Số báo danh:"), gbc);
        gbc.gridx = 1;
        panel.add(cmbSoBaoDanh, gbc);

        gbc.gridx = 2;
        panel.add(new JLabel("Mã môn:"), gbc);
        gbc.gridx = 3;
        panel.add(cmbMaMon, gbc);

        // Row 2: Điểm và Ngày thi
        gbc.gridx = 0; gbc.gridy = 1;
        panel.add(new JLabel("Điểm:"), gbc);
        gbc.gridx = 1;
        panel.add(spnDiem, gbc);

        gbc.gridx = 2;
        panel.add(new JLabel("Ngày thi:"), gbc);
        gbc.gridx = 3;
        panel.add(spnNgayThi, gbc);

        return panel;
    }

    /**
     * Tạo table panel với search
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(PRIMARY_COLOR, 2),
            "Danh sách điểm thi",
            0, 0,
            new Font("Arial", Font.BOLD, 16),
            PRIMARY_COLOR
        ));

        // Search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setBackground(Color.WHITE);
        searchPanel.add(new JLabel("Tìm kiếm theo:"));
        searchPanel.add(cmbTimKiemTheo);
        searchPanel.add(new JLabel("Từ khóa:"));
        searchPanel.add(txtTimKiem);
        searchPanel.add(btnTimKiem);
        searchPanel.add(btnLamMoi);

        // Statistics panel
        JPanel statsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statsPanel.setBackground(Color.WHITE);
        statsPanel.add(lblTongSoLuong);
        statsPanel.add(Box.createHorizontalStrut(20));
        statsPanel.add(lblDiemTrungBinh);
        statsPanel.add(Box.createHorizontalStrut(20));
        statsPanel.add(lblDiemCao);
        statsPanel.add(Box.createHorizontalStrut(20));
        statsPanel.add(lblDiemThap);

        // Top panel combining search and stats
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBackground(Color.WHITE);
        topPanel.add(searchPanel, BorderLayout.NORTH);
        topPanel.add(statsPanel, BorderLayout.SOUTH);

        panel.add(topPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Tạo button panel
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 10));
        panel.setBackground(Color.WHITE);

        panel.add(btnThem);
        panel.add(btnSua);
        panel.add(btnXoa);
        panel.add(btnLuu);
        panel.add(btnHuy);

        return panel;
    }

    /**
     * Tạo status panel
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(Color.LIGHT_GRAY);
        panel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));

        panel.add(lblStatus, BorderLayout.WEST);

        return panel;
    }

    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableDiemThi.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        tableDiemThi.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = tableDiemThi.getSelectedRow();
                if (selectedRow >= 0) {
                    loadSelectedRowToForm(selectedRow);
                    btnSua.setEnabled(true);
                    btnXoa.setEnabled(true);
                } else {
                    btnSua.setEnabled(false);
                    btnXoa.setEnabled(false);
                }
            }
        });
    }

    /**
     * Thiết lập events
     */
    private void setupEvents() {
        // Các events sẽ được thiết lập bởi controller
    }

    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản Lý Điểm Thi");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setResizable(true);

        // Set Look and Feel
        try {
            UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            // Fallback to default look and feel
            e.printStackTrace();
        }
    }

    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        cmbSoBaoDanh.setSelectedIndex(-1);
        cmbMaMon.setSelectedIndex(-1);
        spnDiem.setValue(0.0);
        spnNgayThi.setValue(new Date());

        isEditing = false;
        editingSoBaoDanh = null;
        editingMaMon = null;

        btnThem.setEnabled(true);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(false);
        btnHuy.setEnabled(false);

        // Enable/disable form components
        setFormEnabled(false);

        lblStatus.setText("Sẵn sàng");
        tableDiemThi.clearSelection();
    }

    /**
     * Load dữ liệu từ row được chọn vào form
     */
    private void loadSelectedRowToForm(int row) {
        if (row >= 0 && row < tableModel.getRowCount()) {
            String soBaoDanh = (String) tableModel.getValueAt(row, 0);
            String maMon = (String) tableModel.getValueAt(row, 1);
            Double diem = (Double) tableModel.getValueAt(row, 3);
            String ngayThiStr = (String) tableModel.getValueAt(row, 4);

            cmbSoBaoDanh.setSelectedItem(soBaoDanh);
            cmbMaMon.setSelectedItem(maMon);
            spnDiem.setValue(diem);

            try {
                Date ngayThi = dateFormat.parse(ngayThiStr);
                spnNgayThi.setValue(ngayThi);
            } catch (Exception e) {
                spnNgayThi.setValue(new Date());
            }
        }
    }

    /**
     * Enable/disable form components
     */
    private void setFormEnabled(boolean enabled) {
        cmbSoBaoDanh.setEnabled(enabled);
        cmbMaMon.setEnabled(enabled);
        spnDiem.setEnabled(enabled);
        spnNgayThi.setEnabled(enabled);
    }

    /**
     * Bắt đầu chế độ thêm mới
     */
    public void startAddMode() {
        resetForm();
        setFormEnabled(true);
        isEditing = false;

        btnThem.setEnabled(false);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(true);
        btnHuy.setEnabled(true);

        lblStatus.setText("Chế độ thêm điểm thi mới");
        cmbSoBaoDanh.requestFocus();
    }

    /**
     * Bắt đầu chế độ sửa
     */
    public void startEditMode() {
        int selectedRow = tableDiemThi.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn một dòng để sửa!", "Thông báo", JOptionPane.WARNING_MESSAGE);
            return;
        }

        setFormEnabled(true);
        isEditing = true;
        editingSoBaoDanh = (String) tableModel.getValueAt(selectedRow, 0);
        editingMaMon = (String) tableModel.getValueAt(selectedRow, 1);

        btnThem.setEnabled(false);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(true);
        btnHuy.setEnabled(true);

        // Disable primary key fields during edit
        cmbSoBaoDanh.setEnabled(false);
        cmbMaMon.setEnabled(false);

        lblStatus.setText("Chế độ sửa điểm thi");
        spnDiem.requestFocus();
    }

    /**
     * Hủy thao tác hiện tại
     */
    public void cancelCurrentAction() {
        resetForm();
        updateStatistics();
    }

    // Getter methods for components (for controller access)
    public JComboBox<String> getCmbSoBaoDanh() { return cmbSoBaoDanh; }
    public JComboBox<String> getCmbMaMon() { return cmbMaMon; }
    public JSpinner getSpnDiem() { return spnDiem; }
    public JSpinner getSpnNgayThi() { return spnNgayThi; }
    public JTable getTableDiemThi() { return tableDiemThi; }
    public DefaultTableModel getTableModel() { return tableModel; }
    public JTextField getTxtTimKiem() { return txtTimKiem; }
    public JComboBox<String> getCmbTimKiemTheo() { return cmbTimKiemTheo; }
    public JButton getBtnThem() { return btnThem; }
    public JButton getBtnSua() { return btnSua; }
    public JButton getBtnXoa() { return btnXoa; }
    public JButton getBtnLuu() { return btnLuu; }
    public JButton getBtnHuy() { return btnHuy; }
    public JButton getBtnTimKiem() { return btnTimKiem; }
    public JButton getBtnLamMoi() { return btnLamMoi; }
    public JLabel getLblStatus() { return lblStatus; }

    // Status getters
    public boolean isEditing() { return isEditing; }
    public String getEditingSoBaoDanh() { return editingSoBaoDanh; }
    public String getEditingMaMon() { return editingMaMon; }

    /**
     * Cập nhật thống kê
     */
    public void updateStatistics() {
        int totalCount = tableModel.getRowCount();
        lblTongSoLuong.setText("Tổng số lượng: " + totalCount);

        if (totalCount > 0) {
            double sum = 0;
            double max = Double.MIN_VALUE;
            double min = Double.MAX_VALUE;

            for (int i = 0; i < totalCount; i++) {
                Double diem = (Double) tableModel.getValueAt(i, 3);
                if (diem != null) {
                    sum += diem;
                    max = Math.max(max, diem);
                    min = Math.min(min, diem);
                }
            }

            double average = sum / totalCount;
            lblDiemTrungBinh.setText(String.format("Điểm trung bình: %.2f", average));
            lblDiemCao.setText(String.format("Điểm cao nhất: %.2f", max));
            lblDiemThap.setText(String.format("Điểm thấp nhất: %.2f", min));
        } else {
            lblDiemTrungBinh.setText("Điểm trung bình: 0.0");
            lblDiemCao.setText("Điểm cao nhất: 0.0");
            lblDiemThap.setText("Điểm thấp nhất: 0.0");
        }
    }

    /**
     * Hiển thị thông báo
     */
    public void showMessage(String message, String title, int messageType) {
        JOptionPane.showMessageDialog(this, message, title, messageType);
    }

    /**
     * Hiển thị thông báo xác nhận
     */
    public int showConfirmDialog(String message, String title) {
        return JOptionPane.showConfirmDialog(this, message, title, JOptionPane.YES_NO_OPTION);
    }

    /**
     * Thêm ActionListener cho các buttons
     */
    public void addThemActionListener(ActionListener listener) { btnThem.addActionListener(listener); }
    public void addSuaActionListener(ActionListener listener) { btnSua.addActionListener(listener); }
    public void addXoaActionListener(ActionListener listener) { btnXoa.addActionListener(listener); }
    public void addLuuActionListener(ActionListener listener) { btnLuu.addActionListener(listener); }
    public void addHuyActionListener(ActionListener listener) { btnHuy.addActionListener(listener); }
    public void addTimKiemActionListener(ActionListener listener) { btnTimKiem.addActionListener(listener); }
    public void addLamMoiActionListener(ActionListener listener) { btnLamMoi.addActionListener(listener); }

    // Alternative method names for controller compatibility
    public void addThemListener(ActionListener listener) { addThemActionListener(listener); }
    public void addSuaListener(ActionListener listener) { addSuaActionListener(listener); }
    public void addXoaListener(ActionListener listener) { addXoaActionListener(listener); }
    public void addLuuListener(ActionListener listener) { addLuuActionListener(listener); }
    public void addHuyListener(ActionListener listener) { addHuyActionListener(listener); }
    public void addTimKiemListener(ActionListener listener) { addTimKiemActionListener(listener); }
    public void addLamMoiListener(ActionListener listener) { addLamMoiActionListener(listener); }

    /**
     * Lấy dữ liệu từ form
     */
    public DiemThi getFormData() {
        String soBaoDanh = (String) cmbSoBaoDanh.getSelectedItem();
        String maMon = (String) cmbMaMon.getSelectedItem();
        Double diem = (Double) spnDiem.getValue();
        Date ngayThi = (Date) spnNgayThi.getValue();

        if (soBaoDanh == null || maMon == null) {
            return null;
        }

        return new DiemThi(soBaoDanh, maMon, diem, ngayThi);
    }

    /**
     * Validate form data
     */
    public boolean validateForm() {
        if (cmbSoBaoDanh.getSelectedItem() == null) {
            showMessage("Vui lòng chọn số báo danh!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            cmbSoBaoDanh.requestFocus();
            return false;
        }

        if (cmbMaMon.getSelectedItem() == null) {
            showMessage("Vui lòng chọn mã môn!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            cmbMaMon.requestFocus();
            return false;
        }

        Double diem = (Double) spnDiem.getValue();
        if (diem < 0 || diem > 10) {
            showMessage("Điểm phải từ 0 đến 10!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            spnDiem.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * Load danh sách thí sinh vào ComboBox
     */
    public void loadThiSinhList(List<ThiSinh> thiSinhList) {
        cmbSoBaoDanh.removeAllItems();
        for (ThiSinh ts : thiSinhList) {
            cmbSoBaoDanh.addItem(ts.getSoBaoDanh());
        }
    }

    /**
     * Load danh sách môn thi vào ComboBox
     */
    public void loadMonThiList(List<MonThi> monThiList) {
        cmbMaMon.removeAllItems();
        for (MonThi mt : monThiList) {
            cmbMaMon.addItem(mt.getMaMon());
        }
    }

    /**
     * Load danh sách điểm thi vào table
     */
    public void loadDiemThiList(List<DiemThi> diemThiList, List<MonThi> monThiList) {
        tableModel.setRowCount(0);

        for (DiemThi dt : diemThiList) {
            String tenMon = "N/A";
            for (MonThi mt : monThiList) {
                if (mt.getMaMon().equals(dt.getMaMon())) {
                    tenMon = mt.getTenMon();
                    break;
                }
            }

            String xepLoai = dt.getXepLoai();

            Object[] row = {
                dt.getSoBaoDanh(),
                dt.getMaMon(),
                tenMon,
                dt.getDiem(),
                dt.getNgayThi(),
                xepLoai
            };
            tableModel.addRow(row);
        }

        updateStatistics();
    }

    // Additional methods for controller compatibility
    public void updateTable(List<DiemThi> diemThiList, List<ThiSinh> thiSinhList, List<MonThi> monThiList) {
        loadDiemThiList(diemThiList, monThiList);
    }

    public void updateThiSinhList(List<ThiSinh> thiSinhList) {
        loadThiSinhList(thiSinhList);
    }

    public void updateMonThiList(List<MonThi> monThiList) {
        loadMonThiList(monThiList);
    }

    public void updateStatus(String status) {
        lblStatus.setText(status);
    }

    public void showError(String message) {
        showMessage(message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    public void showSuccess(String message) {
        showMessage(message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }

    public boolean confirmDelete(String soBaoDanh, String maMon) {
        String message = "Bạn có chắc chắn muốn xóa điểm thi của thí sinh " + soBaoDanh + " môn " + maMon + "?";
        return showConfirmDialog(message, "Xác nhận xóa") == JOptionPane.YES_OPTION;
    }

    public void setAddMode() {
        startAddMode();
    }

    public void setEditMode() {
        startEditMode();
    }

    public DiemThi getSelectedDiemThi() {
        int selectedRow = tableDiemThi.getSelectedRow();
        if (selectedRow >= 0) {
            String soBaoDanh = (String) tableModel.getValueAt(selectedRow, 0);
            String maMon = (String) tableModel.getValueAt(selectedRow, 1);
            Double diem = (Double) tableModel.getValueAt(selectedRow, 3);
            String ngayThiStr = (String) tableModel.getValueAt(selectedRow, 4);

            try {
                Date ngayThi = dateFormat.parse(ngayThiStr);
                return new DiemThi(soBaoDanh, maMon, diem, ngayThi);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }
}