package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.DiemThi;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Màn hình quản lý điểm thi
 */
public class DiemThiManagementView extends JFrame {
    
    // Components cho form nhập liệu
    private JComboBox<String> cmbSoBaoDanh, cmbMaMon;
    private JSpinner spnDiem;
    private JSpinner spnNgayThi;
    
    // Components cho bảng dữ liệu
    private JTable tableDiemThi;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    
    // Components cho tìm kiếm
    private JTextField txtTimKiem;
    private JComboBox<String> cmbTimKiemTheo;
    private JButton btnTimKiem, btnLamMoi;
    
    // Components cho thao tác
    private JButton btnThem, btnSua, btnXoa, btnLuu, btnHuy;
    
    // Components cho thống kê
    private JLabel lblTongSoLuong, lblDiemTrungBinh, lblDiemCao, lblDiemThap;
    
    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingKey = null; // soBaoDanh|maMon
    
    // Colors
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240); // #0071F0
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69);
    private static final Color DANGER_COLOR = new Color(220, 53, 69);
    private static final Color WARNING_COLOR = new Color(255, 193, 7);
    
    // Date formatter
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
    
    public DiemThiManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupEvents();
        setupWindow();
        resetForm();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components
        cmbSoBaoDanh = new JComboBox<>();
        cmbMaMon = new JComboBox<>();
        
        // Spinner cho điểm (0.0 - 10.0)
        spnDiem = new JSpinner(new SpinnerNumberModel(0.0, 0.0, 10.0, 0.1));
        JSpinner.NumberEditor diemEditor = new JSpinner.NumberEditor(spnDiem, "0.0");
        spnDiem.setEditor(diemEditor);
        
        // Spinner cho ngày thi
        spnNgayThi = new JSpinner(new SpinnerDateModel());
        JSpinner.DateEditor dateEditor = new JSpinner.DateEditor(spnNgayThi, "dd/MM/yyyy");
        spnNgayThi.setEditor(dateEditor);
        spnNgayThi.setValue(new Date());
        
        // Search components
        txtTimKiem = new JTextField(20);
        cmbTimKiemTheo = new JComboBox<>(new String[]{"Tất cả", "Theo thí sinh", "Theo môn thi"});
        btnTimKiem = new JButton("Tìm kiếm");
        btnLamMoi = new JButton("Làm mới");
        
        // Action buttons
        btnThem = new JButton("Nhập điểm mới");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        btnLuu = new JButton("Lưu");
        btnHuy = new JButton("Hủy");
        
        // Statistics labels
        lblTongSoLuong = new JLabel("Tổng số: 0");
        lblDiemTrungBinh = new JLabel("Điểm TB: 0.0");
        lblDiemCao = new JLabel("Cao nhất: 0.0");
        lblDiemThap = new JLabel("Thấp nhất: 0.0");
        
        // Status
        lblStatus = new JLabel("Sẵn sàng");
        
        // Table
        String[] columnNames = {"Số báo danh", "Tên thí sinh", "Mã môn", "Tên môn", "Điểm", "Xếp loại", "Ngày thi"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho edit trực tiếp trên table
            }
        };
        tableDiemThi = new JTable(tableModel);
        scrollPane = new JScrollPane(tableDiemThi);
        
        // Style components
        styleComponents();
    }
    
    /**
     * Style các components
     */
    private void styleComponents() {
        // Style buttons
        btnThem.setBackground(SUCCESS_COLOR);
        btnThem.setForeground(Color.WHITE);
        btnThem.setFont(new Font("Arial", Font.BOLD, 14));
        btnThem.setPreferredSize(new Dimension(150, 40));
        
        btnSua.setBackground(WARNING_COLOR);
        btnSua.setForeground(Color.BLACK);
        btnSua.setFont(new Font("Arial", Font.BOLD, 14));
        btnSua.setPreferredSize(new Dimension(100, 40));
        
        btnXoa.setBackground(DANGER_COLOR);
        btnXoa.setForeground(Color.WHITE);
        btnXoa.setFont(new Font("Arial", Font.BOLD, 14));
        btnXoa.setPreferredSize(new Dimension(100, 40));
        
        btnLuu.setBackground(PRIMARY_COLOR);
        btnLuu.setForeground(Color.WHITE);
        btnLuu.setFont(new Font("Arial", Font.BOLD, 14));
        btnLuu.setPreferredSize(new Dimension(100, 40));
        
        btnHuy.setBackground(Color.GRAY);
        btnHuy.setForeground(Color.WHITE);
        btnHuy.setFont(new Font("Arial", Font.BOLD, 14));
        btnHuy.setPreferredSize(new Dimension(100, 40));
        
        btnTimKiem.setBackground(PRIMARY_COLOR);
        btnTimKiem.setForeground(Color.WHITE);
        btnTimKiem.setFont(new Font("Arial", Font.BOLD, 12));
        btnTimKiem.setPreferredSize(new Dimension(100, 35));
        
        btnLamMoi.setBackground(Color.GRAY);
        btnLamMoi.setForeground(Color.WHITE);
        btnLamMoi.setFont(new Font("Arial", Font.BOLD, 12));
        btnLamMoi.setPreferredSize(new Dimension(100, 35));
        
        // Style form components với padding và border đẹp hơn
        Font componentFont = new Font("Arial", Font.PLAIN, 14);

        cmbSoBaoDanh.setFont(componentFont);
        cmbSoBaoDanh.setPreferredSize(new Dimension(250, 35));

        cmbMaMon.setFont(componentFont);
        cmbMaMon.setPreferredSize(new Dimension(250, 35));

        spnDiem.setFont(componentFont);
        spnDiem.setPreferredSize(new Dimension(250, 35));

        spnNgayThi.setFont(componentFont);
        spnNgayThi.setPreferredSize(new Dimension(250, 35));

        txtTimKiem.setFont(componentFont);
        txtTimKiem.setPreferredSize(new Dimension(200, 35));
        txtTimKiem.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(206, 212, 218), 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));

        cmbTimKiemTheo.setFont(componentFont);
        cmbTimKiemTheo.setPreferredSize(new Dimension(150, 35));
        
        // Style table
        tableDiemThi.setFont(new Font("Arial", Font.PLAIN, 12));
        tableDiemThi.getTableHeader().setFont(new Font("Arial", Font.BOLD, 12));
        tableDiemThi.getTableHeader().setBackground(PRIMARY_COLOR);
        tableDiemThi.getTableHeader().setForeground(Color.WHITE);
        tableDiemThi.setRowHeight(25);
        tableDiemThi.setSelectionBackground(new Color(230, 240, 255));
        
        // Style statistics labels
        Font statsFont = new Font("Arial", Font.BOLD, 12);
        lblTongSoLuong.setFont(statsFont);
        lblDiemTrungBinh.setFont(statsFont);
        lblDiemCao.setFont(statsFont);
        lblDiemThap.setFont(statsFont);
        
        lblTongSoLuong.setForeground(PRIMARY_COLOR);
        lblDiemTrungBinh.setForeground(PRIMARY_COLOR);
        lblDiemCao.setForeground(SUCCESS_COLOR);
        lblDiemThap.setForeground(DANGER_COLOR);
        
        // Style status
        lblStatus.setFont(new Font("Arial", Font.ITALIC, 12));
        lblStatus.setForeground(PRIMARY_COLOR);
    }
    
    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        getContentPane().setBackground(new Color(248, 249, 250)); // Light background

        // Header panel
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // Main content panel với padding
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(248, 249, 250));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Form panel (left)
        JPanel formPanel = createFormPanel();

        // Table panel (right)
        JPanel tablePanel = createTablePanel();

        // Split pane với styling
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, formPanel, tablePanel);
        splitPane.setDividerLocation(550);
        splitPane.setResizeWeight(0.4);
        splitPane.setDividerSize(8);
        splitPane.setBorder(null);
        splitPane.setBackground(new Color(248, 249, 250));

        mainPanel.add(splitPane, BorderLayout.CENTER);
        add(mainPanel, BorderLayout.CENTER);

        // Status panel với styling
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 8));
        statusPanel.setBackground(Color.WHITE);
        statusPanel.add(lblStatus);
        statusPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Tạo header panel
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JLabel titleLabel = new JLabel("QUẢN LÝ ĐIỂM THI", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(PRIMARY_COLOR);
        
        panel.add(titleLabel, BorderLayout.CENTER);
        return panel;
    }
    
    /**
     * Tạo form panel
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(222, 226, 230), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        // Title
        JLabel titleLabel = new JLabel("THÔNG TIN ĐIỂM THI");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 16));
        titleLabel.setForeground(PRIMARY_COLOR);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));

        panel.add(titleLabel, BorderLayout.NORTH);

        // Form input panel với spacing tốt hơn
        JPanel inputPanel = new JPanel(new GridBagLayout());
        inputPanel.setBackground(Color.WHITE);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(12, 10, 12, 10);
        gbc.anchor = GridBagConstraints.WEST;

        // Row 0: Số báo danh
        gbc.gridx = 0; gbc.gridy = 0;
        JLabel lblSoBaoDanh = new JLabel("Số báo danh:");
        lblSoBaoDanh.setFont(new Font("Arial", Font.BOLD, 14));
        inputPanel.add(lblSoBaoDanh, gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        inputPanel.add(cmbSoBaoDanh, gbc);

        // Row 1: Mã môn
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JLabel lblMaMon = new JLabel("Môn thi:");
        lblMaMon.setFont(new Font("Arial", Font.BOLD, 14));
        inputPanel.add(lblMaMon, gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        inputPanel.add(cmbMaMon, gbc);

        // Row 2: Điểm
        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JLabel lblDiem = new JLabel("Điểm:");
        lblDiem.setFont(new Font("Arial", Font.BOLD, 14));
        inputPanel.add(lblDiem, gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        inputPanel.add(spnDiem, gbc);

        // Row 3: Ngày thi
        gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JLabel lblNgayThi = new JLabel("Ngày thi:");
        lblNgayThi.setFont(new Font("Arial", Font.BOLD, 14));
        inputPanel.add(lblNgayThi, gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        inputPanel.add(spnNgayThi, gbc);

        // Container cho input và statistics
        JPanel contentPanel = new JPanel(new BorderLayout(0, 20));
        contentPanel.setBackground(Color.WHITE);
        contentPanel.add(inputPanel, BorderLayout.NORTH);

        // Statistics panel
        JPanel statsPanel = createStatisticsPanel();
        contentPanel.add(statsPanel, BorderLayout.CENTER);

        panel.add(contentPanel, BorderLayout.CENTER);

        // Buttons panel với spacing đẹp hơn
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 20));
        buttonsPanel.setBackground(Color.WHITE);
        buttonsPanel.add(btnThem);
        buttonsPanel.add(btnSua);
        buttonsPanel.add(btnXoa);
        buttonsPanel.add(btnLuu);
        buttonsPanel.add(btnHuy);

        panel.add(buttonsPanel, BorderLayout.SOUTH);
        
        return panel;
    }

    /**
     * Tạo statistics panel
     */
    private JPanel createStatisticsPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 2, 15, 10));
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(222, 226, 230), 1),
            BorderFactory.createEmptyBorder(15, 15, 15, 15)
        ));

        // Title
        JLabel titleLabel = new JLabel("THỐNG KÊ ĐIỂM THI");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 14));
        titleLabel.setForeground(PRIMARY_COLOR);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);

        // Wrapper panel để có title
        JPanel wrapperPanel = new JPanel(new BorderLayout(0, 10));
        wrapperPanel.setBackground(Color.WHITE);
        wrapperPanel.add(titleLabel, BorderLayout.NORTH);
        wrapperPanel.add(panel, BorderLayout.CENTER);

        panel.add(lblTongSoLuong);
        panel.add(lblDiemTrungBinh);
        panel.add(lblDiemCao);
        panel.add(lblDiemThap);

        return wrapperPanel;
    }

    /**
     * Tạo table panel
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(222, 226, 230), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        // Title
        JLabel titleLabel = new JLabel("DANH SÁCH ĐIỂM THI");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 16));
        titleLabel.setForeground(PRIMARY_COLOR);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));

        panel.add(titleLabel, BorderLayout.NORTH);

        // Search panel với styling đẹp hơn
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 10));
        searchPanel.setBackground(Color.WHITE);
        searchPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));

        JLabel lblTimKiem = new JLabel("Tìm kiếm:");
        lblTimKiem.setFont(new Font("Arial", Font.BOLD, 14));
        searchPanel.add(lblTimKiem);
        searchPanel.add(cmbTimKiemTheo);
        searchPanel.add(txtTimKiem);
        searchPanel.add(btnTimKiem);
        searchPanel.add(btnLamMoi);

        // Main content panel
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBackground(Color.WHITE);
        contentPanel.add(searchPanel, BorderLayout.NORTH);
        contentPanel.add(scrollPane, BorderLayout.CENTER);

        panel.add(contentPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableDiemThi.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Set column widths
        tableDiemThi.getColumnModel().getColumn(0).setPreferredWidth(100); // Số báo danh
        tableDiemThi.getColumnModel().getColumn(1).setPreferredWidth(150); // Tên thí sinh
        tableDiemThi.getColumnModel().getColumn(2).setPreferredWidth(80);  // Mã môn
        tableDiemThi.getColumnModel().getColumn(3).setPreferredWidth(120); // Tên môn
        tableDiemThi.getColumnModel().getColumn(4).setPreferredWidth(60);  // Điểm
        tableDiemThi.getColumnModel().getColumn(5).setPreferredWidth(80);  // Xếp loại
        tableDiemThi.getColumnModel().getColumn(6).setPreferredWidth(100); // Ngày thi
    }

    /**
     * Thiết lập events
     */
    private void setupEvents() {
        // Table selection event
        tableDiemThi.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedDiemThi();
            }
        });
    }

    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản lý Điểm thi");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1400, 800);
        setLocationRelativeTo(null);

        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception ex) {
            // Use default look and feel
        }
    }

    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        if (cmbSoBaoDanh.getItemCount() > 0) {
            cmbSoBaoDanh.setSelectedIndex(0);
        }
        if (cmbMaMon.getItemCount() > 0) {
            cmbMaMon.setSelectedIndex(0);
        }
        spnDiem.setValue(0.0);
        spnNgayThi.setValue(new Date());

        cmbSoBaoDanh.setEnabled(true);
        cmbMaMon.setEnabled(true);
        isEditing = false;
        editingKey = null;

        btnThem.setEnabled(true);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(false);
        btnHuy.setEnabled(false);

        tableDiemThi.clearSelection();
        updateStatus("Sẵn sàng");
    }

    /**
     * Load dữ liệu điểm thi được chọn vào form
     */
    public void loadSelectedDiemThi() {
        int selectedRow = tableDiemThi.getSelectedRow();
        if (selectedRow >= 0) {
            String soBaoDanh = (String) tableModel.getValueAt(selectedRow, 0);
            String maMon = (String) tableModel.getValueAt(selectedRow, 2);
            Double diem = (Double) tableModel.getValueAt(selectedRow, 4);
            String ngayThiStr = (String) tableModel.getValueAt(selectedRow, 6);

            // Set form values
            cmbSoBaoDanh.setSelectedItem(soBaoDanh);
            cmbMaMon.setSelectedItem(maMon);
            spnDiem.setValue(diem);

            try {
                Date ngayThi = dateFormat.parse(ngayThiStr);
                spnNgayThi.setValue(ngayThi);
            } catch (Exception e) {
                spnNgayThi.setValue(new Date());
            }

            btnSua.setEnabled(true);
            btnXoa.setEnabled(true);
            btnThem.setEnabled(false);
        } else {
            btnSua.setEnabled(false);
            btnXoa.setEnabled(false);
            btnThem.setEnabled(true);
        }
    }

    /**
     * Cập nhật bảng dữ liệu
     */
    public void updateTable(List<DiemThi> diemThis, List<ThiSinh> thiSinhs, List<MonThi> monThis) {
        tableModel.setRowCount(0);
        for (DiemThi diemThi : diemThis) {
            // Tìm tên thí sinh
            String tenThiSinh = "";
            for (ThiSinh ts : thiSinhs) {
                if (ts.getSoBaoDanh().equals(diemThi.getSoBaoDanh())) {
                    tenThiSinh = ts.getHoTen();
                    break;
                }
            }

            // Tìm tên môn thi
            String tenMon = "";
            for (MonThi mt : monThis) {
                if (mt.getMaMon().equals(diemThi.getMaMon())) {
                    tenMon = mt.getTenMon();
                    break;
                }
            }

            Object[] row = {
                diemThi.getSoBaoDanh(),
                tenThiSinh,
                diemThi.getMaMon(),
                tenMon,
                diemThi.getDiem(),
                diemThi.getXepLoai(),
                dateFormat.format(diemThi.getNgayThi())
            };
            tableModel.addRow(row);
        }

        // Update statistics
        updateStatistics(diemThis);
    }

    /**
     * Cập nhật thống kê
     */
    private void updateStatistics(List<DiemThi> diemThis) {
        if (diemThis.isEmpty()) {
            lblTongSoLuong.setText("Tổng số: 0");
            lblDiemTrungBinh.setText("Điểm TB: 0.0");
            lblDiemCao.setText("Cao nhất: 0.0");
            lblDiemThap.setText("Thấp nhất: 0.0");
            return;
        }

        double sum = 0;
        double max = diemThis.get(0).getDiem();
        double min = diemThis.get(0).getDiem();

        for (DiemThi dt : diemThis) {
            double diem = dt.getDiem();
            sum += diem;
            if (diem > max) max = diem;
            if (diem < min) min = diem;
        }

        double average = sum / diemThis.size();

        lblTongSoLuong.setText("Tổng số: " + diemThis.size());
        lblDiemTrungBinh.setText(String.format("Điểm TB: %.1f", average));
        lblDiemCao.setText(String.format("Cao nhất: %.1f", max));
        lblDiemThap.setText(String.format("Thấp nhất: %.1f", min));
    }

    /**
     * Cập nhật danh sách thí sinh
     */
    public void updateThiSinhList(List<ThiSinh> thiSinhs) {
        cmbSoBaoDanh.removeAllItems();
        for (ThiSinh ts : thiSinhs) {
            cmbSoBaoDanh.addItem(ts.getSoBaoDanh());
        }
    }

    /**
     * Cập nhật danh sách môn thi
     */
    public void updateMonThiList(List<MonThi> monThis) {
        cmbMaMon.removeAllItems();
        for (MonThi mt : monThis) {
            cmbMaMon.addItem(mt.getMaMon());
        }
    }

    /**
     * Lấy dữ liệu từ form
     */
    public DiemThi getFormData() {
        String soBaoDanh = (String) cmbSoBaoDanh.getSelectedItem();
        String maMon = (String) cmbMaMon.getSelectedItem();
        double diem = (Double) spnDiem.getValue();
        Date ngayThi = (Date) spnNgayThi.getValue();

        return new DiemThi(soBaoDanh, maMon, diem, ngayThi);
    }

    /**
     * Cập nhật trạng thái
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }

    /**
     * Hiển thị thông báo lỗi
     */
    public void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    /**
     * Hiển thị thông báo thành công
     */
    public void showSuccess(String message) {
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * Xác nhận xóa
     */
    public boolean confirmDelete(String soBaoDanh, String maMon) {
        return JOptionPane.showConfirmDialog(this,
            "Bạn có chắc chắn muốn xóa điểm thi của thí sinh " + soBaoDanh + " môn " + maMon + "?",
            "Xác nhận xóa",
            JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION;
    }

    // Getter methods cho controller
    public JTextField getTxtTimKiem() { return txtTimKiem; }
    public JComboBox<String> getCmbTimKiemTheo() { return cmbTimKiemTheo; }
    public boolean isEditing() { return isEditing; }
    public String getEditingKey() { return editingKey; }
    public DefaultTableModel getTableModel() { return tableModel; }
    public JTable getTableDiemThi() { return tableDiemThi; }

    // Event listener methods
    public void addThemListener(ActionListener listener) { btnThem.addActionListener(listener); }
    public void addSuaListener(ActionListener listener) { btnSua.addActionListener(listener); }
    public void addXoaListener(ActionListener listener) { btnXoa.addActionListener(listener); }
    public void addLuuListener(ActionListener listener) { btnLuu.addActionListener(listener); }
    public void addHuyListener(ActionListener listener) { btnHuy.addActionListener(listener); }
    public void addTimKiemListener(ActionListener listener) { btnTimKiem.addActionListener(listener); }
    public void addLamMoiListener(ActionListener listener) { btnLamMoi.addActionListener(listener); }

    /**
     * Chuyển sang chế độ thêm mới
     */
    public void setAddMode() {
        // Clear form data
        cmbSoBaoDanh.setSelectedIndex(-1);
        cmbMaMon.setSelectedIndex(-1);
        spnDiem.setValue(0.0);
        spnNgayThi.setValue(new Date());

        // Set form state for adding
        cmbSoBaoDanh.setEnabled(true);
        cmbMaMon.setEnabled(true);
        isEditing = false;
        editingKey = null;

        // Enable/disable buttons for add mode
        btnThem.setEnabled(false);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(true);
        btnHuy.setEnabled(true);

        tableDiemThi.clearSelection();
        updateStatus("Chế độ nhập điểm mới - Chọn thí sinh, môn thi và nhập điểm");
    }

    /**
     * Chuyển sang chế độ sửa
     */
    public void setEditMode() {
        if (tableDiemThi.getSelectedRow() >= 0) {
            // Load selected data to form first
            loadSelectedDiemThi();

            // Set edit state
            isEditing = true;
            String soBaoDanh = (String) cmbSoBaoDanh.getSelectedItem();
            String maMon = (String) cmbMaMon.getSelectedItem();
            editingKey = soBaoDanh + "|" + maMon;

            // Disable combo boxes (không cho thay đổi thí sinh và môn thi)
            cmbSoBaoDanh.setEnabled(false);
            cmbMaMon.setEnabled(false);

            // Enable/disable buttons for edit mode
            btnThem.setEnabled(false);
            btnSua.setEnabled(false);
            btnXoa.setEnabled(false);
            btnLuu.setEnabled(true);
            btnHuy.setEnabled(true);

            updateStatus("Chế độ chỉnh sửa điểm - Sửa điểm và bấm Lưu");
        }
    }
}
