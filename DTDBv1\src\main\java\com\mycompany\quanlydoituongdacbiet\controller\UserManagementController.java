package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.model.User;
import com.mycompany.quanlydoituongdacbiet.service.UserService;
import com.mycompany.quanlydoituongdacbiet.view.UserManagementView;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Controller cho quản lý người dùng
 */
public class UserManagementController {
    
    private UserManagementView view;
    private UserService userService;
    private boolean isStandalone;
    
    public UserManagementController(UserManagementView view, boolean isStandalone) {
        this.view = view;
        this.userService = UserService.getInstance();
        this.isStandalone = isStandalone;
        
        initController();
        loadData();
    }
    
    private void initController() {
        // Add event listeners
        view.addAddListener(new AddListener());
        view.addEditListener(new EditListener());
        view.addDeleteListener(new DeleteListener());
        view.addSaveListener(new SaveListener());
        view.addCancelListener(new CancelListener());
        view.addSearchListener(new SearchListener());
        view.addRefreshListener(new RefreshListener());
        view.addTableSelectionListener(new TableSelectionListener());
        
        // Set window close operation
        if (isStandalone) {
            view.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        } else {
            view.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        }
    }
    
    private void loadData() {
        try {
            List<User> users = userService.getAllUsers();
            view.updateTable(users);
            view.updateStatus("Đã tải " + users.size() + " người dùng");
        } catch (Exception e) {
            view.showError("Lỗi khi tải dữ liệu: " + e.getMessage());
        }
    }
    
    public void showView() {
        view.setVisible(true);
    }
    
    /**
     * Listener cho nút Thêm
     */
    private class AddListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.setAddMode();
        }
    }
    
    /**
     * Listener cho nút Sửa
     */
    private class EditListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            User selectedUser = view.getSelectedUser();
            if (selectedUser != null) {
                view.setEditMode(selectedUser);
            } else {
                view.showWarning("Vui lòng chọn người dùng cần sửa!");
            }
        }
    }
    
    /**
     * Listener cho nút Xóa
     */
    private class DeleteListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            User selectedUser = view.getSelectedUser();
            if (selectedUser == null) {
                view.showWarning("Vui lòng chọn người dùng cần xóa!");
                return;
            }
            
            // Kiểm tra không cho xóa admin cuối cùng
            if ("ADMIN".equals(selectedUser.getRole()) && userService.getAdminCount() <= 1) {
                view.showError("Không thể xóa admin cuối cùng trong hệ thống!");
                return;
            }
            
            int option = JOptionPane.showConfirmDialog(view,
                "Bạn có chắc chắn muốn xóa người dùng: " + selectedUser.getUsername() + "?",
                "Xác nhận xóa",
                JOptionPane.YES_NO_OPTION);
                
            if (option == JOptionPane.YES_OPTION) {
                try {
                    if (userService.deleteUser(selectedUser.getUsername())) {
                        view.showSuccess("Xóa người dùng thành công!");
                        loadData();
                        view.setDefaultMode();
                    } else {
                        view.showError("Không thể xóa người dùng!");
                    }
                } catch (Exception ex) {
                    view.showError("Lỗi khi xóa người dùng: " + ex.getMessage());
                }
            }
        }
    }
    
    /**
     * Listener cho nút Lưu
     */
    private class SaveListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (!validateInput()) {
                return;
            }
            
            try {
                String username = view.getUsername();
                String password = view.getPassword();
                String fullName = view.getFullName();
                String email = view.getEmail();
                String role = view.getRole();
                
                User user = new User(username, password, fullName, email, role);
                
                // Validate user data
                String validationError = userService.validateUser(user);
                if (validationError != null) {
                    view.showError(validationError);
                    return;
                }
                
                boolean success;
                if (view.isEditMode()) {
                    // Update existing user
                    success = userService.updateUser(user);
                    if (success) {
                        view.showSuccess("Cập nhật người dùng thành công!");
                    } else {
                        view.showError("Không thể cập nhật người dùng!");
                    }
                } else {
                    // Add new user
                    success = userService.addUser(user);
                    if (success) {
                        view.showSuccess("Thêm người dùng thành công!");
                    } else {
                        view.showError("Không thể thêm người dùng! Tên đăng nhập có thể đã tồn tại.");
                    }
                }
                
                if (success) {
                    loadData();
                    view.setDefaultMode();
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi khi lưu người dùng: " + ex.getMessage());
            }
        }
        
        private boolean validateInput() {
            if (view.getUsername().isEmpty()) {
                view.showError("Vui lòng nhập tên đăng nhập!");
                return false;
            }
            
            if (view.getPassword().isEmpty()) {
                view.showError("Vui lòng nhập mật khẩu!");
                return false;
            }
            
            if (view.getFullName().isEmpty()) {
                view.showError("Vui lòng nhập họ tên!");
                return false;
            }
            
            if (view.getUsername().length() < 3) {
                view.showError("Tên đăng nhập phải có ít nhất 3 ký tự!");
                return false;
            }
            
            if (view.getPassword().length() < 4) {
                view.showError("Mật khẩu phải có ít nhất 4 ký tự!");
                return false;
            }
            
            return true;
        }
    }
    
    /**
     * Listener cho nút Hủy
     */
    private class CancelListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.setDefaultMode();
        }
    }
    
    /**
     * Listener cho nút Tìm kiếm
     */
    private class SearchListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                String keyword = view.getSearchKeyword();
                List<User> users;
                
                if (keyword.isEmpty()) {
                    users = userService.getAllUsers();
                } else {
                    users = userService.searchUsers(keyword);
                }
                
                view.updateTable(users);
                view.updateStatus("Tìm thấy " + users.size() + " người dùng");
                
            } catch (Exception ex) {
                view.showError("Lỗi khi tìm kiếm: " + ex.getMessage());
            }
        }
    }
    
    /**
     * Listener cho nút Làm mới
     */
    private class RefreshListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            loadData();
            view.setDefaultMode();
        }
    }
    
    /**
     * Listener cho table selection
     */
    private class TableSelectionListener implements ListSelectionListener {
        @Override
        public void valueChanged(ListSelectionEvent e) {
            if (!e.getValueIsAdjusting()) {
                User selectedUser = view.getSelectedUser();
                if (selectedUser != null) {
                    // Enable edit and delete buttons when a row is selected
                    view.setButtonsEnabled(true, true);
                    view.updateStatus("Đã chọn người dùng: " + selectedUser.getUsername());
                } else {
                    // Disable edit and delete buttons when no row is selected
                    if (!view.isEditMode()) {
                        view.setButtonsEnabled(false, false);
                    }
                }
            }
        }
    }
}
