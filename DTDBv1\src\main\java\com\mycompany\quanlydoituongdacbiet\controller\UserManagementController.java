package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.view.UserManagementView;
import com.mycompany.quanlydoituongdacbiet.dao.UserDAO;
import com.mycompany.quanlydoituongdacbiet.model.User;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Controller cho quản lý người dùng
 */
public class UserManagementController {
    
    private UserManagementView view;
    private UserDAO userDAO;
    
    public UserManagementController(UserManagementView view) {
        this.view = view;
        this.userDAO = new UserDAO();
        
        initController();
    }
    
    /**
     * Khởi tạo controller
     */
    private void initController() {
        // Load dữ liệu ban đầu
        loadAllUsers();
        
        // Thiết lập event handlers
        setupEventHandlers();
    }
    
    /**
     * Thiết lập event handlers
     */
    private void setupEventHandlers() {
        view.setAddActionListener(new AddActionListener());
        view.setEditActionListener(new EditActionListener());
        view.setDeleteActionListener(new DeleteActionListener());
        view.setSaveActionListener(new SaveActionListener());
        view.setCancelActionListener(new CancelActionListener());
        view.setSearchActionListener(new SearchActionListener());
        view.setRefreshActionListener(new RefreshActionListener());
    }
    
    /**
     * Load tất cả users
     */
    private void loadAllUsers() {
        try {
            List<User> users = userDAO.getAllUsers();
            view.loadUserData(users);
            view.updateStatus("Đã tải " + users.size() + " người dùng");
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(view, 
                "Lỗi khi tải danh sách người dùng: " + e.getMessage(),
                "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * Hiển thị view
     */
    public void showView() {
        view.setVisible(true);
    }
    
    /**
     * Xử lý sự kiện thêm
     */
    private class AddActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.startAdding();
        }
    }
    
    /**
     * Xử lý sự kiện sửa
     */
    private class EditActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.startEditing();
        }
    }
    
    /**
     * Xử lý sự kiện xóa
     */
    private class DeleteActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            User user = view.getUserFromForm();
            
            if (user.getUsername().isEmpty()) {
                JOptionPane.showMessageDialog(view, "Vui lòng chọn người dùng cần xóa!");
                return;
            }
            
            // Không cho phép xóa admin
            if ("admin".equals(user.getUsername())) {
                JOptionPane.showMessageDialog(view, 
                    "Không thể xóa tài khoản admin mặc định!",
                    "Cảnh báo", JOptionPane.WARNING_MESSAGE);
                return;
            }
            
            int option = JOptionPane.showConfirmDialog(view,
                "Bạn có chắc chắn muốn xóa người dùng '" + user.getUsername() + "'?",
                "Xác nhận xóa",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE);
            
            if (option == JOptionPane.YES_OPTION) {
                try {
                    boolean success = userDAO.deleteUser(user.getUsername());
                    
                    if (success) {
                        JOptionPane.showMessageDialog(view, "Xóa người dùng thành công!");
                        loadAllUsers();
                        view.resetForm();
                    } else {
                        JOptionPane.showMessageDialog(view, "Không thể xóa người dùng!",
                            "Lỗi", JOptionPane.ERROR_MESSAGE);
                    }
                    
                } catch (Exception ex) {
                    ex.printStackTrace();
                    JOptionPane.showMessageDialog(view, "Lỗi khi xóa người dùng: " + ex.getMessage(),
                        "Lỗi", JOptionPane.ERROR_MESSAGE);
                }
            }
        }
    }
    
    /**
     * Xử lý sự kiện lưu
     */
    private class SaveActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                // Validate form
                String error = view.validateForm();
                if (error != null) {
                    JOptionPane.showMessageDialog(view, error, "Lỗi", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                
                User user = view.getUserFromForm();
                
                boolean success;
                String message;
                
                if (view.isEditing()) {
                    // Cập nhật người dùng
                    User existingUser = userDAO.findByUsername(view.getEditingUsername());
                    if (existingUser == null) {
                        JOptionPane.showMessageDialog(view, "Không tìm thấy người dùng cần cập nhật!");
                        return;
                    }
                    
                    // Giữ nguyên password cũ nếu không nhập password mới
                    String newPassword = user.getPassword();
                    if (newPassword == null || newPassword.isEmpty()) {
                        user.setPassword(existingUser.getPassword());
                    }
                    
                    success = userDAO.updateUser(user);
                    message = success ? "Cập nhật người dùng thành công!" : "Không thể cập nhật người dùng!";
                } else {
                    // Thêm người dùng mới
                    // Kiểm tra username đã tồn tại
                    User existing = userDAO.findByUsername(user.getUsername());
                    if (existing != null) {
                        JOptionPane.showMessageDialog(view, 
                            "Tên đăng nhập '" + user.getUsername() + "' đã tồn tại!",
                            "Lỗi", JOptionPane.ERROR_MESSAGE);
                        return;
                    }
                    
                    success = userDAO.addUser(user);
                    message = success ? "Thêm người dùng thành công!" : "Không thể thêm người dùng!";
                }
                
                if (success) {
                    JOptionPane.showMessageDialog(view, message);
                    loadAllUsers();
                    view.resetForm();
                } else {
                    JOptionPane.showMessageDialog(view, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
                }
                
            } catch (Exception ex) {
                ex.printStackTrace();
                JOptionPane.showMessageDialog(view, "Lỗi khi lưu người dùng: " + ex.getMessage(),
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Xử lý sự kiện hủy
     */
    private class CancelActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.resetForm();
        }
    }
    
    /**
     * Xử lý sự kiện tìm kiếm
     */
    private class SearchActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                String searchText = view.getSearchText();
                
                if (searchText.isEmpty()) {
                    loadAllUsers();
                    return;
                }
                
                List<User> allUsers = userDAO.getAllUsers();
                List<User> results = allUsers.stream()
                    .filter(user -> 
                        user.getUsername().toLowerCase().contains(searchText.toLowerCase()) ||
                        user.getFullName().toLowerCase().contains(searchText.toLowerCase()) ||
                        user.getRole().toLowerCase().contains(searchText.toLowerCase())
                    )
                    .toList();
                
                view.loadUserData(results);
                view.updateStatus("Tìm thấy " + results.size() + " kết quả cho: " + searchText);
                
            } catch (Exception ex) {
                ex.printStackTrace();
                JOptionPane.showMessageDialog(view, "Lỗi khi tìm kiếm: " + ex.getMessage(),
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Xử lý sự kiện làm mới
     */
    private class RefreshActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            loadAllUsers();
            view.resetForm();
        }
    }
}
