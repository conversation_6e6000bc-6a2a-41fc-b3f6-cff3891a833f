package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.controller.AuthenticationController;
import com.mycompany.quanlydoituongdacbiet.model.User;

import java.util.Scanner;

/**
 * View cho màn hình đăng nhập
 */
public class LoginView {
    private AuthenticationController authController;
    private Scanner scanner;
    
    public LoginView() {
        this.authController = new AuthenticationController();
        this.scanner = new Scanner(System.in);
    }
    
    /**
     * Hiển thị màn hình đăng nhập chính
     */
    public void showLoginScreen() {
        while (!authController.isLoggedIn()) {
            clearScreen();
            showLoginHeader();
            showLoginMenu();
            
            int choice = getChoice();
            switch (choice) {
                case 1:
                    handleLogin();
                    break;
                case 2:
                    showAbout();
                    break;
                case 0:
                    System.out.println("Tạm biệt!");
                    System.exit(0);
                    break;
                default:
                    System.out.println("Lựa chọn không hợp lệ!");
                    pauseScreen();
            }
        }
        
        // Đăng nhập thành công, chuyển đến màn hình chính
        showMainScreen();
    }
    
    /**
     * Hiển thị header đăng nhập
     */
    private void showLoginHeader() {
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║              HỆ THỐNG QUẢN LÝ ĐỐI TƯỢNG ĐẶC BIỆT              ║");
        System.out.println("║                        ĐĂNG NHẬP HỆ THỐNG                     ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
    }
    
    /**
     * Hiển thị menu đăng nhập
     */
    private void showLoginMenu() {
        System.out.println("┌──────────────────────────────────────────────────────────────┐");
        System.out.println("│                         MENU CHÍNH                          │");
        System.out.println("├──────────────────────────────────────────────────────────────┤");
        System.out.println("│  1. Đăng nhập                                                │");
        System.out.println("│  2. Thông tin hệ thống                                      │");
        System.out.println("│  0. Thoát                                                    │");
        System.out.println("└──────────────────────────────────────────────────────────────┘");
        System.out.print("Nhập lựa chọn của bạn: ");
    }
    
    /**
     * Xử lý đăng nhập
     */
    private void handleLogin() {
        clearScreen();
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                          ĐĂNG NHẬP                          ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
        
        System.out.print("Tên đăng nhập: ");
        String username = scanner.nextLine();
        
        System.out.print("Mật khẩu: ");
        String password = scanner.nextLine();
        
        // Validate input
        String validationError = authController.validateLoginInput(username, password);
        if (validationError != null) {
            System.out.println("\n❌ " + validationError);
            pauseScreen();
            return;
        }
        
        // Thực hiện đăng nhập
        if (authController.login(username, password)) {
            User currentUser = authController.getCurrentUser();
            System.out.println("\n✅ Đăng nhập thành công!");
            System.out.println("Chào mừng " + currentUser.getFullName() + " (" + currentUser.getRole() + ")");
            pauseScreen();
        } else {
            System.out.println("\n❌ Tên đăng nhập hoặc mật khẩu không đúng!");
            pauseScreen();
        }
    }
    
    /**
     * Hiển thị thông tin hệ thống
     */
    private void showAbout() {
        clearScreen();
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                    THÔNG TIN HỆ THỐNG                       ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
        System.out.println("Hệ thống quản lý đối tượng đặc biệt");
        System.out.println("Phiên bản: 1.0");
        System.out.println("Tác giả: Your Name");
        System.out.println();
        System.out.println("Tài khoản mặc định:");
        System.out.println("- Username: admin");
        System.out.println("- Password: admin123");
        System.out.println("- Role: ADMIN");
        System.out.println();
        pauseScreen();
    }
    
    /**
     * Hiển thị màn hình chính sau khi đăng nhập
     */
    private void showMainScreen() {
        while (authController.isLoggedIn()) {
            clearScreen();
            showMainHeader();
            showMainMenu();
            
            int choice = getChoice();
            switch (choice) {
                case 1:
                    // TODO: Chuyển đến quản lý thí sinh
                    System.out.println("Chức năng quản lý thí sinh sẽ được triển khai...");
                    pauseScreen();
                    break;
                case 2:
                    // TODO: Chuyển đến quản lý môn thi
                    System.out.println("Chức năng quản lý môn thi sẽ được triển khai...");
                    pauseScreen();
                    break;
                case 3:
                    // TODO: Chuyển đến quản lý khối thi
                    System.out.println("Chức năng quản lý khối thi sẽ được triển khai...");
                    pauseScreen();
                    break;
                case 4:
                    // TODO: Chuyển đến quản lý điểm thi
                    System.out.println("Chức năng quản lý điểm thi sẽ được triển khai...");
                    pauseScreen();
                    break;
                case 5:
                    if (authController.isCurrentUserAdmin()) {
                        showUserManagement();
                    } else {
                        System.out.println("❌ Bạn không có quyền truy cập chức năng này!");
                        pauseScreen();
                    }
                    break;
                case 6:
                    showChangePassword();
                    break;
                case 7:
                    showUserInfo();
                    break;
                case 0:
                    handleLogout();
                    break;
                default:
                    System.out.println("Lựa chọn không hợp lệ!");
                    pauseScreen();
            }
        }
    }
    
    /**
     * Hiển thị header màn hình chính
     */
    private void showMainHeader() {
        User currentUser = authController.getCurrentUser();
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║              HỆ THỐNG QUẢN LÝ ĐỐI TƯỢNG ĐẶC BIỆT              ║");
        System.out.println("║  Người dùng: " + String.format("%-47s", currentUser.getFullName()) + "║");
        System.out.println("║  Quyền: " + String.format("%-52s", currentUser.getRole()) + "║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
    }
    
    /**
     * Hiển thị menu chính
     */
    private void showMainMenu() {
        System.out.println("┌──────────────────────────────────────────────────────────────┐");
        System.out.println("│                         MENU CHÍNH                          │");
        System.out.println("├──────────────────────────────────────────────────────────────┤");
        System.out.println("│  1. Quản lý thí sinh                                        │");
        System.out.println("│  2. Quản lý môn thi                                         │");
        System.out.println("│  3. Quản lý khối thi                                        │");
        System.out.println("│  4. Quản lý điểm thi                                        │");
        
        if (authController.isCurrentUserAdmin()) {
            System.out.println("│  5. Quản lý người dùng (Admin)                              │");
        }
        
        System.out.println("│  6. Đổi mật khẩu                                            │");
        System.out.println("│  7. Thông tin tài khoản                                     │");
        System.out.println("│  0. Đăng xuất                                               │");
        System.out.println("└──────────────────────────────────────────────────────────────┘");
        System.out.print("Nhập lựa chọn của bạn: ");
    }
    
    /**
     * Xử lý đăng xuất
     */
    private void handleLogout() {
        System.out.print("Bạn có chắc chắn muốn đăng xuất? (y/n): ");
        String confirm = scanner.nextLine();
        if ("y".equalsIgnoreCase(confirm) || "yes".equalsIgnoreCase(confirm)) {
            authController.logout();
            System.out.println("✅ Đã đăng xuất thành công!");
            pauseScreen();
        }
    }
    
    /**
     * Hiển thị thông tin tài khoản
     */
    private void showUserInfo() {
        clearScreen();
        User currentUser = authController.getCurrentUser();
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                    THÔNG TIN TÀI KHOẢN                      ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
        System.out.println("Tên đăng nhập: " + currentUser.getUsername());
        System.out.println("Họ và tên: " + currentUser.getFullName());
        System.out.println("Quyền: " + currentUser.getRole());
        System.out.println();
        pauseScreen();
    }
    
    /**
     * Đổi mật khẩu
     */
    private void showChangePassword() {
        clearScreen();
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                        ĐỔI MẬT KHẨU                         ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
        
        System.out.print("Mật khẩu hiện tại: ");
        String oldPassword = scanner.nextLine();
        
        System.out.print("Mật khẩu mới: ");
        String newPassword = scanner.nextLine();
        
        System.out.print("Xác nhận mật khẩu mới: ");
        String confirmPassword = scanner.nextLine();
        
        if (!newPassword.equals(confirmPassword)) {
            System.out.println("\n❌ Mật khẩu xác nhận không khớp!");
            pauseScreen();
            return;
        }
        
        if (authController.changePassword(oldPassword, newPassword)) {
            System.out.println("\n✅ Đổi mật khẩu thành công!");
        } else {
            System.out.println("\n❌ Đổi mật khẩu thất bại! Kiểm tra lại mật khẩu hiện tại.");
        }
        
        pauseScreen();
    }
    
    /**
     * Quản lý người dùng (chỉ admin)
     */
    private void showUserManagement() {
        while (true) {
            clearScreen();
            System.out.println("╔══════════════════════════════════════════════════════════════╗");
            System.out.println("║                    QUẢN LÝ NGƯỜI DÙNG                       ║");
            System.out.println("╚══════════════════════════════════════════════════════════════╝");
            System.out.println();

            System.out.println("┌──────────────────────────────────────────────────────────────┐");
            System.out.println("│                    MENU QUẢN LÝ USER                        │");
            System.out.println("├──────────────────────────────────────────────────────────────┤");
            System.out.println("│  1. Xem danh sách người dùng                                │");
            System.out.println("│  2. Thêm người dùng mới                                     │");
            System.out.println("│  3. Xóa người dùng                                          │");
            System.out.println("│  0. Quay lại                                                │");
            System.out.println("└──────────────────────────────────────────────────────────────┘");
            System.out.print("Nhập lựa chọn của bạn: ");

            int choice = getChoice();
            switch (choice) {
                case 1:
                    showUserList();
                    break;
                case 2:
                    addNewUser();
                    break;
                case 3:
                    deleteUser();
                    break;
                case 0:
                    return;
                default:
                    System.out.println("Lựa chọn không hợp lệ!");
                    pauseScreen();
            }
        }
    }

    /**
     * Hiển thị danh sách người dùng
     */
    private void showUserList() {
        clearScreen();
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                  DANH SÁCH NGƯỜI DÙNG                       ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();

        java.util.List<User> users = authController.getAllUsers();
        if (users.isEmpty()) {
            System.out.println("Không có người dùng nào trong hệ thống.");
        } else {
            System.out.printf("%-15s %-25s %-10s%n", "Username", "Họ tên", "Quyền");
            System.out.println("─".repeat(60));
            for (User user : users) {
                System.out.printf("%-15s %-25s %-10s%n",
                    user.getUsername(),
                    user.getFullName(),
                    user.getRole());
            }
        }

        System.out.println();
        pauseScreen();
    }

    /**
     * Thêm người dùng mới
     */
    private void addNewUser() {
        clearScreen();
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                   THÊM NGƯỜI DÙNG MỚI                       ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();

        System.out.print("Tên đăng nhập: ");
        String username = scanner.nextLine();

        System.out.print("Mật khẩu: ");
        String password = scanner.nextLine();

        System.out.print("Họ và tên: ");
        String fullName = scanner.nextLine();

        System.out.print("Quyền (ADMIN/USER) [mặc định: USER]: ");
        String role = scanner.nextLine();
        if (role.trim().isEmpty()) {
            role = "USER";
        }

        // Validate input
        String validationError = authController.validateRegistrationInput(username, password, fullName);
        if (validationError != null) {
            System.out.println("\n❌ " + validationError);
            pauseScreen();
            return;
        }

        if (authController.registerUser(username, password, fullName, role.toUpperCase())) {
            System.out.println("\n✅ Thêm người dùng thành công!");
        } else {
            System.out.println("\n❌ Thêm người dùng thất bại!");
        }

        pauseScreen();
    }

    /**
     * Xóa người dùng
     */
    private void deleteUser() {
        clearScreen();
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                     XÓA NGƯỜI DÙNG                          ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();

        // Hiển thị danh sách user trước
        java.util.List<User> users = authController.getAllUsers();
        if (users.size() <= 1) {
            System.out.println("❌ Không thể xóa vì chỉ còn 1 người dùng trong hệ thống!");
            pauseScreen();
            return;
        }

        System.out.println("Danh sách người dùng hiện tại:");
        System.out.printf("%-15s %-25s %-10s%n", "Username", "Họ tên", "Quyền");
        System.out.println("─".repeat(60));
        for (User user : users) {
            if (!user.getUsername().equals(authController.getCurrentUser().getUsername())) {
                System.out.printf("%-15s %-25s %-10s%n",
                    user.getUsername(),
                    user.getFullName(),
                    user.getRole());
            }
        }

        System.out.println();
        System.out.print("Nhập tên đăng nhập cần xóa: ");
        String username = scanner.nextLine();

        if (username.trim().isEmpty()) {
            System.out.println("\n❌ Tên đăng nhập không được để trống!");
            pauseScreen();
            return;
        }

        System.out.print("Bạn có chắc chắn muốn xóa người dùng '" + username + "'? (y/n): ");
        String confirm = scanner.nextLine();

        if ("y".equalsIgnoreCase(confirm) || "yes".equalsIgnoreCase(confirm)) {
            if (authController.deleteUser(username)) {
                System.out.println("\n✅ Xóa người dùng thành công!");
            } else {
                System.out.println("\n❌ Xóa người dùng thất bại! Kiểm tra lại tên đăng nhập.");
            }
        } else {
            System.out.println("\n❌ Đã hủy thao tác xóa.");
        }

        pauseScreen();
    }
    
    /**
     * Lấy lựa chọn từ người dùng
     */
    private int getChoice() {
        try {
            String input = scanner.nextLine();
            return Integer.parseInt(input);
        } catch (NumberFormatException e) {
            return -1;
        }
    }
    
    /**
     * Xóa màn hình
     */
    private void clearScreen() {
        // Sử dụng ANSI escape codes để xóa màn hình
        System.out.print("\033[2J\033[H");
        System.out.flush();
    }
    
    /**
     * Tạm dừng màn hình
     */
    private void pauseScreen() {
        System.out.print("\nNhấn Enter để tiếp tục...");
        scanner.nextLine();
    }
    
    /**
     * Lấy AuthenticationController
     */
    public AuthenticationController getAuthController() {
        return authController;
    }
}
