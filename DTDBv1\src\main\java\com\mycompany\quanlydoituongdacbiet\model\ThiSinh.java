package com.mycompany.quanlydoituongdacbiet.model;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Class đại diện cho thí sinh
 */
public class ThiSinh {
    private String soBaoDanh;
    private String hoTen;
    private String gioiTinh;
    private Date ngaySinh;
    private String diaChi;
    private String soDienThoai;
    private String email;
    private String maKhoi;
    
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
    
    // Constructor mặc định
    public ThiSinh() {}
    
    // Constructor đầy đủ
    public ThiSinh(String soBaoDanh, String hoTen, String gioiTinh, Date ngaySinh, 
                   String diaChi, String soDienThoai, String email, String maKhoi) {
        this.soBaoDanh = soBaoDanh;
        this.hoTen = hoTen;
        this.gioiTinh = gioiTinh;
        this.ngaySinh = ngaySinh;
        this.diaChi = diaChi;
        this.soDienThoai = soDienThoai;
        this.email = email;
        this.maKhoi = maKhoi;
    }
    
    /**
     * Chuyển đổi từ string trong file thành object
     */
    public static ThiSinh fromString(String line) {
        try {
            String[] parts = line.split("\\|");
            if (parts.length >= 8) {
                ThiSinh thiSinh = new ThiSinh();
                thiSinh.soBaoDanh = parts[0];
                thiSinh.hoTen = parts[1];
                thiSinh.gioiTinh = parts[2];
                thiSinh.ngaySinh = dateFormat.parse(parts[3]);
                thiSinh.diaChi = parts[4];
                thiSinh.soDienThoai = parts[5];
                thiSinh.email = parts[6];
                thiSinh.maKhoi = parts[7];
                return thiSinh;
            }
        } catch (Exception e) {
            System.err.println("Lỗi parse ThiSinh: " + line);
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Chuyển đổi object thành string để lưu vào file
     */
    @Override
    public String toString() {
        return soBaoDanh + "|" + hoTen + "|" + gioiTinh + "|" + 
               dateFormat.format(ngaySinh) + "|" + diaChi + "|" + 
               soDienThoai + "|" + email + "|" + maKhoi;
    }
    
    // Getters and Setters
    public String getSoBaoDanh() { return soBaoDanh; }
    public void setSoBaoDanh(String soBaoDanh) { this.soBaoDanh = soBaoDanh; }
    
    public String getHoTen() { return hoTen; }
    public void setHoTen(String hoTen) { this.hoTen = hoTen; }
    
    public String getGioiTinh() { return gioiTinh; }
    public void setGioiTinh(String gioiTinh) { this.gioiTinh = gioiTinh; }
    
    public Date getNgaySinh() { return ngaySinh; }
    public void setNgaySinh(Date ngaySinh) { this.ngaySinh = ngaySinh; }
    
    public String getDiaChi() { return diaChi; }
    public void setDiaChi(String diaChi) { this.diaChi = diaChi; }
    
    public String getSoDienThoai() { return soDienThoai; }
    public void setSoDienThoai(String soDienThoai) { this.soDienThoai = soDienThoai; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getMaKhoi() { return maKhoi; }
    public void setMaKhoi(String maKhoi) { this.maKhoi = maKhoi; }
}