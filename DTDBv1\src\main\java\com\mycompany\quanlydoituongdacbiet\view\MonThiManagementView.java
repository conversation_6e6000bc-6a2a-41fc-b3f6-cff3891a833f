package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * M<PERSON>n hình quản lý môn thi
 */
public class MonThiManagementView extends JFrame {
    
    // Components cho form nhập liệu
    private JTextField txtMaMon, txtTenMon, txtMoTa;
    private JSpinner spnThoiGianThi;
    
    // Components cho bảng dữ liệu
    private JTable tableMonThi;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    
    // Components cho tìm kiếm
    private JTextField txtTimKiem;
    private JButton btnTimKiem, btnLamMoi;
    
    // Components cho thao tác
    private JButton btnThem, btnSua, btnXoa, btnLuu, btnHuy;
    
    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingMaMon = null;
    
    // Colors
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240); // #0071F0
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69);
    private static final Color DANGER_COLOR = new Color(220, 53, 69);
    private static final Color WARNING_COLOR = new Color(255, 193, 7);
    
    public MonThiManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupEvents();
        setupWindow();
        resetForm();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components
        txtMaMon = new JTextField(15);
        txtTenMon = new JTextField(25);
        txtMoTa = new JTextField(30);
        
        // Spinner cho thời gian thi (phút)
        spnThoiGianThi = new JSpinner(new SpinnerNumberModel(90, 30, 300, 15));
        
        // Search components
        txtTimKiem = new JTextField(20);
        btnTimKiem = new JButton("Tìm kiếm");
        btnLamMoi = new JButton("Làm mới");
        
        // Action buttons
        btnThem = new JButton("Thêm môn thi");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        btnLuu = new JButton("Lưu");
        btnHuy = new JButton("Hủy");
        
        // Status
        lblStatus = new JLabel("Sẵn sàng");
        
        // Table
        String[] columnNames = {"Mã môn", "Tên môn", "Thời gian thi (phút)", "Mô tả"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho edit trực tiếp trên table
            }
        };
        tableMonThi = new JTable(tableModel);
        scrollPane = new JScrollPane(tableMonThi);
        
        // Style components
        styleComponents();
    }
    
    /**
     * Style các components
     */
    private void styleComponents() {
        // Style buttons
        btnThem.setBackground(SUCCESS_COLOR);
        btnThem.setForeground(Color.WHITE);
        btnThem.setFont(new Font("Arial", Font.BOLD, 14));
        btnThem.setPreferredSize(new Dimension(150, 40));
        
        btnSua.setBackground(WARNING_COLOR);
        btnSua.setForeground(Color.BLACK);
        btnSua.setFont(new Font("Arial", Font.BOLD, 14));
        btnSua.setPreferredSize(new Dimension(100, 40));
        
        btnXoa.setBackground(DANGER_COLOR);
        btnXoa.setForeground(Color.WHITE);
        btnXoa.setFont(new Font("Arial", Font.BOLD, 14));
        btnXoa.setPreferredSize(new Dimension(100, 40));
        
        btnLuu.setBackground(PRIMARY_COLOR);
        btnLuu.setForeground(Color.WHITE);
        btnLuu.setFont(new Font("Arial", Font.BOLD, 14));
        btnLuu.setPreferredSize(new Dimension(100, 40));
        
        btnHuy.setBackground(Color.GRAY);
        btnHuy.setForeground(Color.WHITE);
        btnHuy.setFont(new Font("Arial", Font.BOLD, 14));
        btnHuy.setPreferredSize(new Dimension(100, 40));
        
        btnTimKiem.setBackground(PRIMARY_COLOR);
        btnTimKiem.setForeground(Color.WHITE);
        btnTimKiem.setFont(new Font("Arial", Font.BOLD, 12));
        btnTimKiem.setPreferredSize(new Dimension(100, 35));
        
        btnLamMoi.setBackground(Color.GRAY);
        btnLamMoi.setForeground(Color.WHITE);
        btnLamMoi.setFont(new Font("Arial", Font.BOLD, 12));
        btnLamMoi.setPreferredSize(new Dimension(100, 35));
        
        // Style text fields
        Font textFieldFont = new Font("Arial", Font.PLAIN, 14);
        txtMaMon.setFont(textFieldFont);
        txtTenMon.setFont(textFieldFont);
        txtMoTa.setFont(textFieldFont);
        txtTimKiem.setFont(textFieldFont);
        
        // Style table
        tableMonThi.setFont(new Font("Arial", Font.PLAIN, 12));
        tableMonThi.getTableHeader().setFont(new Font("Arial", Font.BOLD, 12));
        tableMonThi.getTableHeader().setBackground(PRIMARY_COLOR);
        tableMonThi.getTableHeader().setForeground(Color.WHITE);
        tableMonThi.setRowHeight(25);
        tableMonThi.setSelectionBackground(new Color(230, 240, 255));
        
        // Style status
        lblStatus.setFont(new Font("Arial", Font.ITALIC, 12));
        lblStatus.setForeground(PRIMARY_COLOR);
    }
    
    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));
        
        // Header panel
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);
        
        // Main content panel
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        
        // Form panel (left)
        JPanel formPanel = createFormPanel();
        
        // Table panel (right)
        JPanel tablePanel = createTablePanel();
        
        // Split pane
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, formPanel, tablePanel);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.4);
        
        mainPanel.add(splitPane, BorderLayout.CENTER);
        add(mainPanel, BorderLayout.CENTER);
        
        // Status panel
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.add(lblStatus);
        statusPanel.setBorder(BorderFactory.createEtchedBorder());
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Tạo header panel
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JLabel titleLabel = new JLabel("QUẢN LÝ MÔN THI", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(PRIMARY_COLOR);
        
        panel.add(titleLabel, BorderLayout.CENTER);
        return panel;
    }
    
    /**
     * Tạo form panel
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Thông tin môn thi"));
        
        // Form fields
        JPanel fieldsPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Row 0: Mã môn
        gbc.gridx = 0; gbc.gridy = 0;
        fieldsPanel.add(new JLabel("Mã môn:"), gbc);
        gbc.gridx = 1;
        fieldsPanel.add(txtMaMon, gbc);
        
        // Row 1: Tên môn
        gbc.gridx = 0; gbc.gridy = 1;
        fieldsPanel.add(new JLabel("Tên môn:"), gbc);
        gbc.gridx = 1;
        fieldsPanel.add(txtTenMon, gbc);
        
        // Row 2: Thời gian thi
        gbc.gridx = 0; gbc.gridy = 2;
        fieldsPanel.add(new JLabel("Thời gian thi (phút):"), gbc);
        gbc.gridx = 1;
        fieldsPanel.add(spnThoiGianThi, gbc);
        
        // Row 3: Mô tả
        gbc.gridx = 0; gbc.gridy = 3;
        fieldsPanel.add(new JLabel("Mô tả:"), gbc);
        gbc.gridx = 1;
        fieldsPanel.add(txtMoTa, gbc);
        
        panel.add(fieldsPanel, BorderLayout.NORTH);
        
        // Buttons panel
        JPanel buttonsPanel = new JPanel(new FlowLayout());
        buttonsPanel.add(btnThem);
        buttonsPanel.add(btnSua);
        buttonsPanel.add(btnXoa);
        buttonsPanel.add(btnLuu);
        buttonsPanel.add(btnHuy);
        
        panel.add(buttonsPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * Tạo table panel
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Danh sách môn thi"));
        
        // Search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.add(new JLabel("Tìm kiếm:"));
        searchPanel.add(txtTimKiem);
        searchPanel.add(btnTimKiem);
        searchPanel.add(btnLamMoi);
        
        panel.add(searchPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }

    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableMonThi.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Set column widths
        tableMonThi.getColumnModel().getColumn(0).setPreferredWidth(100); // Mã môn
        tableMonThi.getColumnModel().getColumn(1).setPreferredWidth(200); // Tên môn
        tableMonThi.getColumnModel().getColumn(2).setPreferredWidth(150); // Thời gian
        tableMonThi.getColumnModel().getColumn(3).setPreferredWidth(250); // Mô tả
    }

    /**
     * Thiết lập events
     */
    private void setupEvents() {
        // Table selection event
        tableMonThi.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedMonThi();
            }
        });
    }

    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản lý Môn thi");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);

        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception ex) {
            // Use default look and feel
        }
    }

    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        txtMaMon.setText("");
        txtTenMon.setText("");
        txtMoTa.setText("");
        spnThoiGianThi.setValue(90);

        txtMaMon.setEditable(true);
        isEditing = false;
        editingMaMon = null;

        btnThem.setEnabled(true);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(false);
        btnHuy.setEnabled(false);

        tableMonThi.clearSelection();
        updateStatus("Sẵn sàng");
    }

    /**
     * Load dữ liệu môn thi được chọn vào form
     */
    public void loadSelectedMonThi() {
        int selectedRow = tableMonThi.getSelectedRow();
        if (selectedRow >= 0) {
            String maMon = (String) tableModel.getValueAt(selectedRow, 0);
            String tenMon = (String) tableModel.getValueAt(selectedRow, 1);
            Integer thoiGian = (Integer) tableModel.getValueAt(selectedRow, 2);
            String moTa = (String) tableModel.getValueAt(selectedRow, 3);

            txtMaMon.setText(maMon);
            txtTenMon.setText(tenMon);
            spnThoiGianThi.setValue(thoiGian);
            txtMoTa.setText(moTa);

            btnSua.setEnabled(true);
            btnXoa.setEnabled(true);
            btnThem.setEnabled(false);
        } else {
            btnSua.setEnabled(false);
            btnXoa.setEnabled(false);
            btnThem.setEnabled(true);
        }
    }

    /**
     * Cập nhật bảng dữ liệu
     */
    public void updateTable(List<MonThi> monThis) {
        tableModel.setRowCount(0);
        for (MonThi monThi : monThis) {
            Object[] row = {
                monThi.getMaMon(),
                monThi.getTenMon(),
                monThi.getThoiGianThi(),
                monThi.getMoTa()
            };
            tableModel.addRow(row);
        }
    }

    /**
     * Lấy dữ liệu từ form
     */
    public MonThi getFormData() {
        String maMon = txtMaMon.getText().trim();
        String tenMon = txtTenMon.getText().trim();
        int thoiGian = (Integer) spnThoiGianThi.getValue();
        String moTa = txtMoTa.getText().trim();

        return new MonThi(maMon, tenMon, thoiGian, moTa);
    }

    /**
     * Cập nhật trạng thái
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }

    /**
     * Hiển thị thông báo lỗi
     */
    public void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    /**
     * Hiển thị thông báo thành công
     */
    public void showSuccess(String message) {
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * Xác nhận xóa
     */
    public boolean confirmDelete(String maMon) {
        return JOptionPane.showConfirmDialog(this,
            "Bạn có chắc chắn muốn xóa môn thi: " + maMon + "?",
            "Xác nhận xóa",
            JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION;
    }

    // Getter methods cho controller
    public JTextField getTxtTimKiem() { return txtTimKiem; }
    public boolean isEditing() { return isEditing; }
    public String getEditingMaMon() { return editingMaMon; }
    public DefaultTableModel getTableModel() { return tableModel; }
    public JTable getTableMonThi() { return tableMonThi; }

    // Event listener methods
    public void addThemListener(ActionListener listener) { btnThem.addActionListener(listener); }
    public void addSuaListener(ActionListener listener) { btnSua.addActionListener(listener); }
    public void addXoaListener(ActionListener listener) { btnXoa.addActionListener(listener); }
    public void addLuuListener(ActionListener listener) { btnLuu.addActionListener(listener); }
    public void addHuyListener(ActionListener listener) { btnHuy.addActionListener(listener); }
    public void addTimKiemListener(ActionListener listener) { btnTimKiem.addActionListener(listener); }
    public void addLamMoiListener(ActionListener listener) { btnLamMoi.addActionListener(listener); }

    /**
     * Chuyển sang chế độ thêm mới
     */
    public void setAddMode() {
        resetForm();
        txtMaMon.setEditable(true);
        btnLuu.setEnabled(true);
        btnHuy.setEnabled(true);
        btnThem.setEnabled(false);
        updateStatus("Chế độ thêm mới");
    }

    /**
     * Chuyển sang chế độ sửa
     */
    public void setEditMode() {
        if (tableMonThi.getSelectedRow() >= 0) {
            isEditing = true;
            editingMaMon = txtMaMon.getText();
            txtMaMon.setEditable(false);
            btnLuu.setEnabled(true);
            btnHuy.setEnabled(true);
            btnSua.setEnabled(false);
            btnXoa.setEnabled(false);
            btnThem.setEnabled(false);
            updateStatus("Chế độ chỉnh sửa");
        }
    }
}
