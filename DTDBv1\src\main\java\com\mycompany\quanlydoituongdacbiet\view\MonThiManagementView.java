package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * M<PERSON>n hình quản lý môn thi
 */
public class MonThiManagementView extends JFrame {
    
    // Components cho form nhập liệu
    private JTextField txtMaMon, txtTenMon, txtMoTa;
    private JSpinner spnThoiGianThi;
    
    // Components cho bảng dữ liệu
    private JTable tableMonThi;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    
    // Components cho tìm kiếm
    private JTextField txtTimKiem;
    private JButton btnTimKiem, btnLamMoi;
    
    // Components cho thao tác
    private JButton btnThem, btnSua, btnXoa, btnLuu, btnHuy;
    
    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingMaMon = null;
    
    // Colors
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240); // #0071F0
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69);
    private static final Color DANGER_COLOR = new Color(220, 53, 69);
    private static final Color WARNING_COLOR = new Color(255, 193, 7);
    
    public MonThiManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupEvents();
        setupWindow();
        resetForm();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components
        txtMaMon = new JTextField(15);
        txtTenMon = new JTextField(25);
        txtMoTa = new JTextField(30);
        
        // Spinner cho thời gian thi (phút)
        spnThoiGianThi = new JSpinner(new SpinnerNumberModel(90, 30, 300, 15));
        
        // Search components
        txtTimKiem = new JTextField(20);
        btnTimKiem = new JButton("Tìm kiếm");
        btnLamMoi = new JButton("Làm mới");
        
        // Action buttons
        btnThem = new JButton("Thêm môn thi");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        btnLuu = new JButton("Lưu");
        btnHuy = new JButton("Hủy");
        
        // Status
        lblStatus = new JLabel("Sẵn sàng");
        
        // Table
        String[] columnNames = {"Mã môn", "Tên môn", "Thời gian thi (phút)", "Mô tả"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho edit trực tiếp trên table
            }
        };
        tableMonThi = new JTable(tableModel);
        scrollPane = new JScrollPane(tableMonThi);
        
        // Style components
        styleComponents();
    }
    
    /**
     * Style các components
     */
    private void styleComponents() {
        // Style buttons
        btnThem.setBackground(SUCCESS_COLOR);
        btnThem.setForeground(Color.WHITE);
        btnThem.setFont(new Font("Arial", Font.BOLD, 14));
        btnThem.setPreferredSize(new Dimension(150, 40));
        
        btnSua.setBackground(WARNING_COLOR);
        btnSua.setForeground(Color.BLACK);
        btnSua.setFont(new Font("Arial", Font.BOLD, 14));
        btnSua.setPreferredSize(new Dimension(100, 40));
        
        btnXoa.setBackground(DANGER_COLOR);
        btnXoa.setForeground(Color.WHITE);
        btnXoa.setFont(new Font("Arial", Font.BOLD, 14));
        btnXoa.setPreferredSize(new Dimension(100, 40));
        
        btnLuu.setBackground(PRIMARY_COLOR);
        btnLuu.setForeground(Color.WHITE);
        btnLuu.setFont(new Font("Arial", Font.BOLD, 14));
        btnLuu.setPreferredSize(new Dimension(100, 40));
        
        btnHuy.setBackground(Color.GRAY);
        btnHuy.setForeground(Color.WHITE);
        btnHuy.setFont(new Font("Arial", Font.BOLD, 14));
        btnHuy.setPreferredSize(new Dimension(100, 40));
        
        btnTimKiem.setBackground(PRIMARY_COLOR);
        btnTimKiem.setForeground(Color.WHITE);
        btnTimKiem.setFont(new Font("Arial", Font.BOLD, 12));
        btnTimKiem.setPreferredSize(new Dimension(100, 35));
        
        btnLamMoi.setBackground(Color.GRAY);
        btnLamMoi.setForeground(Color.WHITE);
        btnLamMoi.setFont(new Font("Arial", Font.BOLD, 12));
        btnLamMoi.setPreferredSize(new Dimension(100, 35));
        
        // Style text fields với padding và border đẹp hơn
        Font textFieldFont = new Font("Arial", Font.PLAIN, 14);

        txtMaMon.setFont(textFieldFont);
        txtMaMon.setPreferredSize(new Dimension(250, 35));
        txtMaMon.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(206, 212, 218), 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));

        txtTenMon.setFont(textFieldFont);
        txtTenMon.setPreferredSize(new Dimension(250, 35));
        txtTenMon.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(206, 212, 218), 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));

        txtMoTa.setFont(textFieldFont);
        txtMoTa.setPreferredSize(new Dimension(250, 35));
        txtMoTa.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(206, 212, 218), 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));

        txtTimKiem.setFont(textFieldFont);
        txtTimKiem.setPreferredSize(new Dimension(200, 35));
        txtTimKiem.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(206, 212, 218), 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));

        spnThoiGianThi.setFont(textFieldFont);
        spnThoiGianThi.setPreferredSize(new Dimension(250, 35));
        
        // Style table
        tableMonThi.setFont(new Font("Arial", Font.PLAIN, 12));
        tableMonThi.getTableHeader().setFont(new Font("Arial", Font.BOLD, 12));
        tableMonThi.getTableHeader().setBackground(PRIMARY_COLOR);
        tableMonThi.getTableHeader().setForeground(Color.WHITE);
        tableMonThi.setRowHeight(25);
        tableMonThi.setSelectionBackground(new Color(230, 240, 255));
        
        // Style status
        lblStatus.setFont(new Font("Arial", Font.ITALIC, 12));
        lblStatus.setForeground(PRIMARY_COLOR);
    }
    
    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        getContentPane().setBackground(new Color(248, 249, 250)); // Light background

        // Header panel
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // Main content panel với padding
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(248, 249, 250));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Form panel (left)
        JPanel formPanel = createFormPanel();

        // Table panel (right)
        JPanel tablePanel = createTablePanel();

        // Split pane với styling
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, formPanel, tablePanel);
        splitPane.setDividerLocation(450);
        splitPane.setResizeWeight(0.35);
        splitPane.setDividerSize(8);
        splitPane.setBorder(null);
        splitPane.setBackground(new Color(248, 249, 250));

        mainPanel.add(splitPane, BorderLayout.CENTER);
        add(mainPanel, BorderLayout.CENTER);

        // Status panel với styling
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 8));
        statusPanel.setBackground(Color.WHITE);
        statusPanel.add(lblStatus);
        statusPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Tạo header panel
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JLabel titleLabel = new JLabel("QUẢN LÝ MÔN THI", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(PRIMARY_COLOR);
        
        panel.add(titleLabel, BorderLayout.CENTER);
        return panel;
    }
    
    /**
     * Tạo form panel
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(222, 226, 230), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        // Title
        JLabel titleLabel = new JLabel("THÔNG TIN MÔN THI");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 16));
        titleLabel.setForeground(PRIMARY_COLOR);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));

        panel.add(titleLabel, BorderLayout.NORTH);

        // Form fields với spacing tốt hơn
        JPanel fieldsPanel = new JPanel(new GridBagLayout());
        fieldsPanel.setBackground(Color.WHITE);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(12, 10, 12, 10);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Row 0: Mã môn
        gbc.gridx = 0; gbc.gridy = 0;
        JLabel lblMaMon = new JLabel("Mã môn:");
        lblMaMon.setFont(new Font("Arial", Font.BOLD, 14));
        fieldsPanel.add(lblMaMon, gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        fieldsPanel.add(txtMaMon, gbc);

        // Row 1: Tên môn
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JLabel lblTenMon = new JLabel("Tên môn:");
        lblTenMon.setFont(new Font("Arial", Font.BOLD, 14));
        fieldsPanel.add(lblTenMon, gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        fieldsPanel.add(txtTenMon, gbc);

        // Row 2: Thời gian thi
        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JLabel lblThoiGian = new JLabel("Thời gian thi (phút):");
        lblThoiGian.setFont(new Font("Arial", Font.BOLD, 14));
        fieldsPanel.add(lblThoiGian, gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        fieldsPanel.add(spnThoiGianThi, gbc);

        // Row 3: Mô tả
        gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JLabel lblMoTa = new JLabel("Mô tả:");
        lblMoTa.setFont(new Font("Arial", Font.BOLD, 14));
        fieldsPanel.add(lblMoTa, gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        fieldsPanel.add(txtMoTa, gbc);

        panel.add(fieldsPanel, BorderLayout.CENTER);

        // Buttons panel với spacing đẹp hơn
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 20));
        buttonsPanel.setBackground(Color.WHITE);
        buttonsPanel.add(btnThem);
        buttonsPanel.add(btnSua);
        buttonsPanel.add(btnXoa);
        buttonsPanel.add(btnLuu);
        buttonsPanel.add(btnHuy);

        panel.add(buttonsPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * Tạo table panel
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(222, 226, 230), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        // Title
        JLabel titleLabel = new JLabel("DANH SÁCH MÔN THI");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 16));
        titleLabel.setForeground(PRIMARY_COLOR);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));

        panel.add(titleLabel, BorderLayout.NORTH);

        // Search panel với styling đẹp hơn
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 10));
        searchPanel.setBackground(Color.WHITE);
        searchPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));

        JLabel lblTimKiem = new JLabel("Tìm kiếm:");
        lblTimKiem.setFont(new Font("Arial", Font.BOLD, 14));
        searchPanel.add(lblTimKiem);
        searchPanel.add(txtTimKiem);
        searchPanel.add(btnTimKiem);
        searchPanel.add(btnLamMoi);

        // Main content panel
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBackground(Color.WHITE);
        contentPanel.add(searchPanel, BorderLayout.NORTH);
        contentPanel.add(scrollPane, BorderLayout.CENTER);

        panel.add(contentPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableMonThi.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Set column widths
        tableMonThi.getColumnModel().getColumn(0).setPreferredWidth(100); // Mã môn
        tableMonThi.getColumnModel().getColumn(1).setPreferredWidth(200); // Tên môn
        tableMonThi.getColumnModel().getColumn(2).setPreferredWidth(150); // Thời gian
        tableMonThi.getColumnModel().getColumn(3).setPreferredWidth(250); // Mô tả
    }

    /**
     * Thiết lập events
     */
    private void setupEvents() {
        // Table selection event
        tableMonThi.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedMonThi();
            }
        });
    }

    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản lý Môn thi");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);

        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception ex) {
            // Use default look and feel
        }
    }

    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        txtMaMon.setText("");
        txtTenMon.setText("");
        txtMoTa.setText("");
        spnThoiGianThi.setValue(90);

        txtMaMon.setEditable(true);
        isEditing = false;
        editingMaMon = null;

        btnThem.setEnabled(true);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(false);
        btnHuy.setEnabled(false);

        tableMonThi.clearSelection();
        updateStatus("Sẵn sàng");
    }

    /**
     * Load dữ liệu môn thi được chọn vào form
     */
    public void loadSelectedMonThi() {
        int selectedRow = tableMonThi.getSelectedRow();
        if (selectedRow >= 0) {
            String maMon = (String) tableModel.getValueAt(selectedRow, 0);
            String tenMon = (String) tableModel.getValueAt(selectedRow, 1);
            Integer thoiGian = (Integer) tableModel.getValueAt(selectedRow, 2);
            String moTa = (String) tableModel.getValueAt(selectedRow, 3);

            txtMaMon.setText(maMon);
            txtTenMon.setText(tenMon);
            spnThoiGianThi.setValue(thoiGian);
            txtMoTa.setText(moTa);

            btnSua.setEnabled(true);
            btnXoa.setEnabled(true);
            btnThem.setEnabled(false);
        } else {
            btnSua.setEnabled(false);
            btnXoa.setEnabled(false);
            btnThem.setEnabled(true);
        }
    }

    /**
     * Cập nhật bảng dữ liệu
     */
    public void updateTable(List<MonThi> monThis) {
        tableModel.setRowCount(0);
        for (MonThi monThi : monThis) {
            Object[] row = {
                monThi.getMaMon(),
                monThi.getTenMon(),
                monThi.getThoiGianThi(),
                monThi.getMoTa()
            };
            tableModel.addRow(row);
        }
    }

    /**
     * Lấy dữ liệu từ form
     */
    public MonThi getFormData() {
        String maMon = txtMaMon.getText().trim();
        String tenMon = txtTenMon.getText().trim();
        int thoiGian = (Integer) spnThoiGianThi.getValue();
        String moTa = txtMoTa.getText().trim();

        return new MonThi(maMon, tenMon, thoiGian, moTa);
    }

    /**
     * Cập nhật trạng thái
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }

    /**
     * Hiển thị thông báo lỗi
     */
    public void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    /**
     * Hiển thị thông báo thành công
     */
    public void showSuccess(String message) {
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * Xác nhận xóa
     */
    public boolean confirmDelete(String maMon) {
        return JOptionPane.showConfirmDialog(this,
            "Bạn có chắc chắn muốn xóa môn thi: " + maMon + "?",
            "Xác nhận xóa",
            JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION;
    }

    // Getter methods cho controller
    public JTextField getTxtTimKiem() { return txtTimKiem; }
    public boolean isEditing() { return isEditing; }
    public String getEditingMaMon() { return editingMaMon; }
    public DefaultTableModel getTableModel() { return tableModel; }
    public JTable getTableMonThi() { return tableMonThi; }

    // Event listener methods
    public void addThemListener(ActionListener listener) { btnThem.addActionListener(listener); }
    public void addSuaListener(ActionListener listener) { btnSua.addActionListener(listener); }
    public void addXoaListener(ActionListener listener) { btnXoa.addActionListener(listener); }
    public void addLuuListener(ActionListener listener) { btnLuu.addActionListener(listener); }
    public void addHuyListener(ActionListener listener) { btnHuy.addActionListener(listener); }
    public void addTimKiemListener(ActionListener listener) { btnTimKiem.addActionListener(listener); }
    public void addLamMoiListener(ActionListener listener) { btnLamMoi.addActionListener(listener); }

    /**
     * Chuyển sang chế độ thêm mới
     */
    public void setAddMode() {
        // Clear form data
        txtMaMon.setText("");
        txtTenMon.setText("");
        txtMoTa.setText("");
        spnThoiGianThi.setValue(90);

        // Set form state for adding
        txtMaMon.setEditable(true);
        isEditing = false;
        editingMaMon = null;

        // Enable/disable buttons for add mode
        btnThem.setEnabled(false);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(true);
        btnHuy.setEnabled(true);

        tableMonThi.clearSelection();
        updateStatus("Chế độ thêm mới - Nhập thông tin và bấm Lưu");
    }

    /**
     * Chuyển sang chế độ sửa
     */
    public void setEditMode() {
        if (tableMonThi.getSelectedRow() >= 0) {
            // Load selected data to form first
            loadSelectedMonThi();

            // Set edit state
            isEditing = true;
            editingMaMon = txtMaMon.getText();
            txtMaMon.setEditable(false); // Không cho sửa mã môn

            // Enable/disable buttons for edit mode
            btnThem.setEnabled(false);
            btnSua.setEnabled(false);
            btnXoa.setEnabled(false);
            btnLuu.setEnabled(true);
            btnHuy.setEnabled(true);

            updateStatus("Chế độ chỉnh sửa - Sửa thông tin và bấm Lưu");
        }
    }
}
