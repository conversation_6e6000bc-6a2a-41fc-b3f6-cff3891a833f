package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.view.LoginManagementView;
import com.mycompany.quanlydoituongdacbiet.view.ThiSinhManagementView;
import com.mycompany.quanlydoituongdacbiet.view.UserManagementView;
import com.mycompany.quanlydoituongdacbiet.model.User;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Controller cho giao diện đăng nhập
 */
public class LoginManagementController {
    
    private LoginManagementView view;
    private AuthenticationController authController;
    
    public LoginManagementController(LoginManagementView view) {
        this.view = view;
        this.authController = new AuthenticationController();
        
        initController();
    }
    
    /**
     * Khởi tạo controller
     */
    private void initController() {
        setupEventHandlers();
    }
    
    /**
     * Thiết lập event handlers
     */
    private void setupEventHandlers() {
        view.setLoginActionListener(new LoginActionListener());
        view.setExitActionListener(new ExitActionListener());
        view.setAboutActionListener(new AboutActionListener());
        view.setLogoutActionListener(new LogoutActionListener());
        view.setThiSinhActionListener(new ThiSinhActionListener());
        view.setMonThiActionListener(new MonThiActionListener());
        view.setKhoiThiActionListener(new KhoiThiActionListener());
        view.setDiemThiActionListener(new DiemThiActionListener());
        view.setUserManagementActionListener(new UserManagementActionListener());
        view.setChangePasswordActionListener(new ChangePasswordActionListener());
    }
    
    /**
     * Hiển thị view
     */
    public void showView() {
        view.setVisible(true);
    }
    
    /**
     * Xử lý đăng nhập
     */
    private class LoginActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String username = view.getUsername();
            String password = view.getPassword();
            
            // Validate input
            String error = authController.validateLoginInput(username, password);
            if (error != null) {
                view.setStatus(error, true);
                return;
            }
            
            // Attempt login
            boolean success = authController.login(username, password);
            
            if (success) {
                User currentUser = authController.getCurrentUser();
                view.setStatus("Đăng nhập thành công!", false);
                
                // Delay để user thấy thông báo thành công
                Timer timer = new Timer(1000, new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent e) {
                        view.showMainPanel(currentUser.getUsername(), 
                                         currentUser.getFullName(), 
                                         currentUser.getRole());
                        view.clearStatus();
                    }
                });
                timer.setRepeats(false);
                timer.start();
                
            } else {
                view.setStatus("Tên đăng nhập hoặc mật khẩu không đúng!", true);
            }
        }
    }
    
    /**
     * Xử lý thoát ứng dụng
     */
    private class ExitActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int option = JOptionPane.showConfirmDialog(view,
                "Bạn có chắc chắn muốn thoát ứng dụng?",
                "Xác nhận thoát",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE);
            
            if (option == JOptionPane.YES_OPTION) {
                System.exit(0);
            }
        }
    }
    
    /**
     * Xử lý hiển thị thông tin
     */
    private class AboutActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String message = "HỆ THỐNG QUẢN LÝ ĐỐI TƯỢNG ĐẶC BIỆT\n\n" +
                           "Phiên bản: 1.0\n" +
                           "Phát triển bởi: Nhóm phát triển\n" +
                           "Năm: 2024\n\n" +
                           "Hệ thống hỗ trợ quản lý:\n" +
                           "- Thí sinh\n" +
                           "- Môn thi\n" +
                           "- Khối thi\n" +
                           "- Điểm thi\n" +
                           "- Người dùng (Admin)\n\n" +
                           "Tài khoản mặc định:\n" +
                           "Username: admin\n" +
                           "Password: admin123";
            
            JOptionPane.showMessageDialog(view, message, 
                "Thông tin hệ thống", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    /**
     * Xử lý đăng xuất
     */
    private class LogoutActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int option = JOptionPane.showConfirmDialog(view,
                "Bạn có chắc chắn muốn đăng xuất?",
                "Xác nhận đăng xuất",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE);
            
            if (option == JOptionPane.YES_OPTION) {
                authController.logout();
                view.showLoginPanel();
                view.setSize(500, 400);
                view.setLocationRelativeTo(null);
            }
        }
    }
    
    /**
     * Xử lý mở quản lý thí sinh
     */
    private class ThiSinhActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                ThiSinhManagementView thiSinhView = new ThiSinhManagementView();
                ThiSinhController thiSinhController = new ThiSinhController(thiSinhView);
                thiSinhController.showView();
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(view,
                    "Lỗi khi mở quản lý thí sinh: " + ex.getMessage(),
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Xử lý mở quản lý môn thi
     */
    private class MonThiActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JOptionPane.showMessageDialog(view,
                "Chức năng quản lý môn thi đang được phát triển!",
                "Thông báo", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    /**
     * Xử lý mở quản lý khối thi
     */
    private class KhoiThiActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JOptionPane.showMessageDialog(view,
                "Chức năng quản lý khối thi đang được phát triển!",
                "Thông báo", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    /**
     * Xử lý mở quản lý điểm thi
     */
    private class DiemThiActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JOptionPane.showMessageDialog(view,
                "Chức năng quản lý điểm thi đang được phát triển!",
                "Thông báo", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    /**
     * Xử lý mở quản lý người dùng (Admin only)
     */
    private class UserManagementActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (!authController.isCurrentUserAdmin()) {
                JOptionPane.showMessageDialog(view,
                    "Bạn không có quyền truy cập chức năng này!",
                    "Lỗi quyền truy cập", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            try {
                // Tạo dialog quản lý user đơn giản
                showUserManagementDialog();
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(view,
                    "Lỗi khi mở quản lý người dùng: " + ex.getMessage(),
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Xử lý đổi mật khẩu
     */
    private class ChangePasswordActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            showChangePasswordDialog();
        }
    }
    
    /**
     * Hiển thị dialog quản lý người dùng
     */
    private void showUserManagementDialog() {
        JDialog dialog = new JDialog(view, "Quản lý Người dùng", true);
        dialog.setSize(400, 300);
        dialog.setLocationRelativeTo(view);
        
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // Danh sách users
        JTextArea userList = new JTextArea(10, 30);
        userList.setEditable(false);
        userList.setFont(new Font("Monospaced", Font.PLAIN, 12));
        
        StringBuilder sb = new StringBuilder();
        sb.append("DANH SÁCH NGƯỜI DÙNG:\n");
        sb.append("=" .repeat(50)).append("\n");
        
        for (User user : authController.getAllUsers()) {
            sb.append(String.format("%-15s | %-20s | %s\n", 
                user.getUsername(), user.getFullName(), user.getRole()));
        }
        
        userList.setText(sb.toString());
        JScrollPane scrollPane = new JScrollPane(userList);
        
        panel.add(scrollPane);
        
        JButton closeButton = new JButton("Đóng");
        closeButton.addActionListener(e -> dialog.dispose());
        
        JPanel buttonPanel = new JPanel();
        buttonPanel.add(closeButton);
        panel.add(buttonPanel);
        
        dialog.add(panel);
        dialog.setVisible(true);
    }
    
    /**
     * Hiển thị dialog đổi mật khẩu
     */
    private void showChangePasswordDialog() {
        JDialog dialog = new JDialog(view, "Đổi mật khẩu", true);
        dialog.setSize(350, 200);
        dialog.setLocationRelativeTo(view);
        
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        JPasswordField oldPassword = new JPasswordField(15);
        JPasswordField newPassword = new JPasswordField(15);
        JPasswordField confirmPassword = new JPasswordField(15);
        
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.EAST;
        panel.add(new JLabel("Mật khẩu cũ:"), gbc);
        gbc.gridx = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(oldPassword, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.EAST;
        panel.add(new JLabel("Mật khẩu mới:"), gbc);
        gbc.gridx = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(newPassword, gbc);
        
        gbc.gridx = 0; gbc.gridy = 2; gbc.anchor = GridBagConstraints.EAST;
        panel.add(new JLabel("Xác nhận:"), gbc);
        gbc.gridx = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(confirmPassword, gbc);
        
        JButton changeButton = new JButton("Đổi mật khẩu");
        JButton cancelButton = new JButton("Hủy");
        
        changeButton.addActionListener(e -> {
            String oldPass = new String(oldPassword.getPassword());
            String newPass = new String(newPassword.getPassword());
            String confirmPass = new String(confirmPassword.getPassword());
            
            if (oldPass.isEmpty() || newPass.isEmpty() || confirmPass.isEmpty()) {
                JOptionPane.showMessageDialog(dialog, "Vui lòng nhập đầy đủ thông tin!");
                return;
            }
            
            if (!newPass.equals(confirmPass)) {
                JOptionPane.showMessageDialog(dialog, "Mật khẩu mới không khớp!");
                return;
            }
            
            if (newPass.length() < 3) {
                JOptionPane.showMessageDialog(dialog, "Mật khẩu mới phải có ít nhất 3 ký tự!");
                return;
            }
            
            boolean success = authController.changePassword(oldPass, newPass);
            if (success) {
                JOptionPane.showMessageDialog(dialog, "Đổi mật khẩu thành công!");
                dialog.dispose();
            } else {
                JOptionPane.showMessageDialog(dialog, "Mật khẩu cũ không đúng!");
            }
        });
        
        cancelButton.addActionListener(e -> dialog.dispose());
        
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 2;
        gbc.anchor = GridBagConstraints.CENTER;
        JPanel buttonPanel = new JPanel();
        buttonPanel.add(changeButton);
        buttonPanel.add(cancelButton);
        panel.add(buttonPanel, gbc);
        
        dialog.add(panel);
        dialog.setVisible(true);
    }
}
