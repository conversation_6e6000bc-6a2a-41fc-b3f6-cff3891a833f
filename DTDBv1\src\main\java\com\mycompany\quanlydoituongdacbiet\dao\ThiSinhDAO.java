package com.mycompany.quanlydoituongdacbiet.dao;

import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import com.mycompany.quanlydoituongdacbiet.utils.FileManager;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO để quản lý dữ liệu thí sinh
 */
public class ThiSinhDAO {
    
    /**
     * Lấy tất cả thí sinh
     */
    public List<ThiSinh> getAllThiSinh() {
        List<ThiSinh> thiSinhs = new ArrayList<>();
        List<String> lines = FileManager.readAllLines(FileManager.THISINHS_FILE);
        
        for (String line : lines) {
            ThiSinh thiSinh = ThiSinh.fromString(line);
            if (thiSinh != null) {
                thiSinhs.add(thiSinh);
            }
        }
        
        return thiSinhs;
    }
    
    /**
     * Thêm thí sinh mới
     */
    public boolean addThiSinh(ThiSinh thiSinh) {
        try {
            // Kiểm tra trùng số báo danh
            if (getThiSinhBySoBaoDanh(thiSinh.getSoBaoDanh()) != null) {
                return false; // Đã tồn tại
            }
            
            FileManager.appendLine(FileManager.THISINHS_FILE, thiSinh.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Cập nhật thông tin thí sinh
     */
    public boolean updateThiSinh(ThiSinh thiSinh) {
        try {
            FileManager.updateLine(FileManager.THISINHS_FILE, 
                                 thiSinh.getSoBaoDanh(), 0, thiSinh.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Xóa thí sinh
     */
    public boolean deleteThiSinh(String soBaoDanh) {
        try {
            FileManager.deleteLine(FileManager.THISINHS_FILE, soBaoDanh, 0);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Tìm thí sinh theo số báo danh
     */
    public ThiSinh getThiSinhBySoBaoDanh(String soBaoDanh) {
        List<String> lines = FileManager.searchLines(FileManager.THISINHS_FILE, soBaoDanh, 0);
        if (!lines.isEmpty()) {
            return ThiSinh.fromString(lines.get(0));
        }
        return null;
    }
    
    /**
     * Tìm kiếm thí sinh theo tên
     */
    public List<ThiSinh> searchThiSinhByName(String name) {
        List<ThiSinh> result = new ArrayList<>();
        List<String> lines = FileManager.searchLines(FileManager.THISINHS_FILE, name, 1);
        
        for (String line : lines) {
            ThiSinh thiSinh = ThiSinh.fromString(line);
            if (thiSinh != null) {
                result.add(thiSinh);
            }
        }
        
        return result;
    }
    
    /**
     * Lấy thí sinh theo khối thi
     */
    public List<ThiSinh> getThiSinhByKhoi(String maKhoi) {
        List<ThiSinh> result = new ArrayList<>();
        List<String> lines = FileManager.searchLines(FileManager.THISINHS_FILE, maKhoi, 7);
        
        for (String line : lines) {
            ThiSinh thiSinh = ThiSinh.fromString(line);
            if (thiSinh != null) {
                result.add(thiSinh);
            }
        }
        
        return result;
    }

    public boolean exists(String soBaoDanh) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }
}