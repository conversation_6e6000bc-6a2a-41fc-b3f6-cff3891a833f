import com.mycompany.quanlydoituongdacbiet.service.UserService;
import com.mycompany.quanlydoituongdacbiet.model.User;

/**
 * Test đơn giản để kiểm tra backend
 */
public class TestLogin {
    
    public static void main(String[] args) {
        System.out.println("=== TEST BACKEND FUNCTIONALITY ===");
        
        UserService userService = UserService.getInstance();
        
        // Test 1: Kiểm tra đăng nhập admin
        System.out.println("\n1. Test đăng nhập admin:");
        User admin = userService.login("admin", "admin");
        if (admin != null) {
            System.out.println("✅ Đăng nhập admin thành công!");
            System.out.println("   - Username: " + admin.getUsername());
            System.out.println("   - Full Name: " + admin.getFullName());
            System.out.println("   - Role: " + admin.getRole());
        } else {
            System.out.println("❌ Đăng nhập admin thất bại!");
        }
        
        // Test 2: Kiểm tra đăng nhập sai
        System.out.println("\n2. Test đăng nhập sai:");
        User wrongUser = userService.login("wrong", "wrong");
        if (wrongUser == null) {
            System.out.println("✅ Đăng nhập sai được từ chối đúng!");
        } else {
            System.out.println("❌ Lỗi: Đăng nhập sai nhưng vẫn thành công!");
        }
        
        // Test 3: Kiểm tra số lượng user
        System.out.println("\n3. Test số lượng user:");
        int userCount = userService.getAllUsers().size();
        System.out.println("✅ Có " + userCount + " user trong hệ thống");
        
        // Test 4: Thêm user mới
        System.out.println("\n4. Test thêm user mới:");
        User newUser = new User("testuser", "1234", "Test User", "<EMAIL>", "USER");
        boolean added = userService.addUser(newUser);
        if (added) {
            System.out.println("✅ Thêm user mới thành công!");
            
            // Test đăng nhập với user mới
            User loginTest = userService.login("testuser", "1234");
            if (loginTest != null) {
                System.out.println("✅ Đăng nhập với user mới thành công!");
            } else {
                System.out.println("❌ Không thể đăng nhập với user mới!");
            }
        } else {
            System.out.println("❌ Thêm user mới thất bại!");
        }
        
        // Test 5: Kiểm tra validation
        System.out.println("\n5. Test validation:");
        User invalidUser = new User("", "", "", "", "");
        String validationResult = userService.validateUser(invalidUser);
        if (validationResult != null) {
            System.out.println("✅ Validation hoạt động đúng - từ chối user không hợp lệ!");
            System.out.println("   Lỗi: " + validationResult);
        } else {
            System.out.println("❌ Lỗi validation - chấp nhận user không hợp lệ!");
        }
        
        // Test 6: Tìm kiếm user
        System.out.println("\n6. Test tìm kiếm:");
        var searchResults = userService.searchUsers("admin");
        System.out.println("✅ Tìm thấy " + searchResults.size() + " user với từ khóa 'admin'");
        
        System.out.println("\n=== KẾT QUẢ TEST ===");
        System.out.println("Backend hoạt động tốt! Có thể sử dụng GUI để test tiếp.");
        System.out.println("Hướng dẫn:");
        System.out.println("1. Chạy MainApplication");
        System.out.println("2. Đăng nhập với admin/admin");
        System.out.println("3. Test các chức năng User Management");
    }
}
