package com.mycompany.quanlydoituongdacbiet.model;

/**
 * Model đại diện cho User trong hệ thống
 */
public class User {
    private String username;
    private String password;
    private String fullName;
    private String email;
    private String role; // ADMIN hoặc USER
    
    public User() {
    }
    
    public User(String username, String password, String fullName, String role) {
        this.username = username;
        this.password = password;
        this.fullName = fullName;
        this.role = role;
    }

    public User(String username, String password, String fullName, String email, String role) {
        this.username = username;
        this.password = password;
        this.fullName = fullName;
        this.email = email;
        this.role = role;
    }

    public User(String username, String fullName, String role) {
        this.username = username;
        this.fullName = fullName;
        this.role = role;
    }
    
    // Getters and Setters
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    
    /**
     * Chuyển đổi User thành chuỗi để lưu vào file
     * Format: username|password|fullName|email|role
     */
    public String toFileString() {
        String emailStr = (email != null) ? email : "";
        return username + "|" + password + "|" + fullName + "|" + emailStr + "|" + role;
    }

    /**
     * Tạo User từ chuỗi đọc từ file
     */
    public static User fromFileString(String line) {
        String[] parts = line.split("\\|");
        if (parts.length >= 4) {
            User user = new User(parts[0], parts[1], parts[2], parts[parts.length - 1]);
            // Nếu có email (format mới với 5 parts)
            if (parts.length >= 5) {
                String emailPart = parts[3];
                if (!emailPart.trim().isEmpty()) {
                    user.setEmail(emailPart);
                }
            }
            return user;
        }
        return null;
    }
    
    /**
     * Kiểm tra xem user có phải là admin không
     */
    public boolean isAdmin() {
        return "ADMIN".equals(role);
    }
    
    @Override
    public String toString() {
        return "User{" +
                "username='" + username + '\'' +
                ", fullName='" + fullName + '\'' +
                ", role='" + role + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        User user = (User) obj;
        return username != null ? username.equals(user.username) : user.username == null;
    }
    
    @Override
    public int hashCode() {
        return username != null ? username.hashCode() : 0;
    }
}
