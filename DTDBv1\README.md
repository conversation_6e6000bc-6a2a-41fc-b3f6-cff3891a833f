# HỆ THỐNG QUẢN LÝ ĐIỂM THI ĐẠI HỌC

## 🎯 Tổng quan
Hệ thống quản lý điểm thi đại học với giao diện đẹp, hiện đại và đầy đủ chức năng. <PERSON><PERSON><PERSON><PERSON> thiết kế theo kiến trúc MVC với màu sắc chủ đạo #0071F0.

## ✨ Tính năng chính

### 🔐 Hệ thống đăng nhập
- Giao diện login đẹp với màu sắc chuyên nghiệp
- Xác thực người dùng an toàn
- Phân quyền ADMIN/USER
- Main menu sau khi đăng nhập thành công

### 👥 Quản lý người dùng (Chỉ ADMIN)
- **Thêm người dùng mới**: Userna<PERSON>, mật khẩu, họ tên, email, vai trò
- **Sửa thông tin**: C<PERSON><PERSON> nhật thông tin người dùng (trừ username)
- **<PERSON><PERSON><PERSON> người dùng**: <PERSON><PERSON><PERSON> bảo vệ không xóa admin cuối cùng
- **Tì<PERSON> kiếm**: Theo username, họ tên, email, vai trò
- **Validation**: Kiểm tra dữ liệu đầu vào đầy đủ

### 🎓 Quản lý thí sinh
- Tích hợp với ThiSinhManagementView hiện có
- Quản lý thông tin thí sinh và điểm thi

## 🏗️ Kiến trúc hệ thống

### Model-View-Controller (MVC)
```
📁 model/
├── User.java                 # Model người dùng với email support
└── ...

📁 view/
├── LoginManagementView.java  # Giao diện đăng nhập mới
├── UserManagementView.java   # Giao diện quản lý user mới
└── ThiSinhManagementView.java # Giao diện quản lý thí sinh

📁 controller/
├── LoginController.java      # Controller đăng nhập
├── UserController.java       # Controller quản lý user
└── ...

📁 service/
├── UserService.java          # Business logic cho User
└── ...

📁 QuanLyDoiTuong/
└── MainApplication.java      # Entry point của ứng dụng
```

### 💾 Lưu trữ dữ liệu
- **File-based storage** với UTF-8 encoding
- **Thư mục data/**: Chứa file users.txt
- **Format**: username|password|fullName|email|role
- **Tự động tạo admin mặc định** nếu chưa có dữ liệu

## 🚀 Cách chạy ứng dụng

### Compile
```bash
cd DTDBv1/src/main/java
javac -cp . com/mycompany/quanlydoituongdacbiet/QuanLyDoiTuong/MainApplication.java
```

### Run
```bash
java -cp . com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong.MainApplication
```

### Tài khoản mặc định
- **Username**: admin
- **Password**: admin
- **Role**: ADMIN

## 🎨 Thiết kế UI

### Màu sắc chủ đạo
- **Primary**: #0071F0 (Xanh dương chuyên nghiệp)
- **Secondary**: #6c757d (Xám)
- **Success**: #28a745 (Xanh lá)
- **Danger**: #dc3545 (Đỏ)
- **Light**: #f8f9fa (Trắng nhạt)

### Layout chuẩn
- **BorderLayout**: North (Header/Form), Center (Table), South (Status)
- **GridBagLayout**: Form fields với spacing hoàn hảo
- **Responsive design**: Tự động điều chỉnh kích thước
- **Professional styling**: Borders, shadows, hover effects

## 📋 Hướng dẫn sử dụng

### 1. Đăng nhập
1. Khởi động ứng dụng
2. Nhập username/password
3. Click "Đăng nhập"
4. Chọn chức năng từ main menu

### 2. Quản lý người dùng (ADMIN)
1. Từ main menu chọn "Quản lý Người dùng"
2. **Thêm mới**: Click "Thêm" → Nhập thông tin → "Lưu"
3. **Sửa**: Chọn user → "Sửa" → Cập nhật → "Lưu"
4. **Xóa**: Chọn user → "Xóa" → Xác nhận
5. **Tìm kiếm**: Nhập từ khóa → "Tìm kiếm"
6. **Làm mới**: Click "Làm mới" để hiển thị tất cả

### 3. Validation rules
- **Username**: Tối thiểu 3 ký tự, không trùng lặp
- **Password**: Tối thiểu 4 ký tự
- **Họ tên**: Bắt buộc nhập
- **Email**: Format hợp lệ (nếu có)
- **Role**: ADMIN hoặc USER

## 🔒 Bảo mật

### Phân quyền
- **ADMIN**: Toàn quyền quản lý user và thí sinh
- **USER**: Chỉ xem và quản lý thí sinh

### Bảo vệ dữ liệu
- Không cho phép xóa admin cuối cùng
- Không cho phép xóa tài khoản đang đăng nhập
- Ẩn password trong table hiển thị
- Validation đầy đủ trước khi lưu

## 🛠️ Công nghệ sử dụng

- **Java Swing**: GUI framework
- **Look and Feel**: Nimbus (fallback to System)
- **File I/O**: UTF-8 encoding cho tiếng Việt
- **Design Pattern**: MVC, Singleton
- **Event Handling**: ActionListener, MouseListener

## 📁 Cấu trúc thư mục

```
DTDBv1/
├── src/main/java/
│   └── com/mycompany/quanlydoituongdacbiet/
│       ├── model/           # Data models
│       ├── view/            # UI components
│       ├── controller/      # Business logic controllers
│       ├── service/         # Business services
│       └── QuanLyDoiTuong/  # Main application
├── data/                    # Data storage
│   └── users.txt           # User data file
└── README.md               # This file
```

## 🎯 Tính năng nổi bật

✅ **Giao diện đẹp**: Thiết kế theo chuẩn ThiSinhManagementView  
✅ **Màu sắc sinh động**: Tông chủ đạo #0071F0  
✅ **Layout chuẩn**: Không bị ép chật, spacing hoàn hảo  
✅ **Backend hoàn chỉnh**: MVC pattern với Service layer  
✅ **File-based storage**: Dữ liệu persistent, UTF-8 support  
✅ **Validation đầy đủ**: Kiểm tra input và business rules  
✅ **Phân quyền rõ ràng**: ADMIN/USER với UI restrictions  
✅ **Error handling**: Xử lý lỗi và hiển thị thông báo  
✅ **Professional UX**: Hover effects, status updates  

## 🔄 Cập nhật so với phiên bản cũ

### Đã xóa
- ❌ LoginManagementViewOld.java
- ❌ UserManagementViewOld.java  
- ❌ TestNewViews.java

### Đã thay thế
- ✅ LoginManagementView.java (mới)
- ✅ UserManagementView.java (mới)
- ✅ MainApplication.java (entry point chính thức)

### Đã thêm
- ✅ UserService.java (business logic)
- ✅ UserController.java (MVC controller)
- ✅ LoginController.java (login logic)
- ✅ Enhanced User.java (email support)

---

**Phát triển bởi**: Augment Agent  
**Phiên bản**: 2.0  
**Ngày cập nhật**: 2025-06-28
