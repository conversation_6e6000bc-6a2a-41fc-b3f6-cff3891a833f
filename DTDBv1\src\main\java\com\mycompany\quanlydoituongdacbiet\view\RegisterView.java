package com.mycompany.quanlydoituongdacbiet.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;

/**
 * <PERSON>iao diện đăng ký tài kho<PERSON>n - <PERSON><PERSON><PERSON><PERSON> kế theo chuẩn LoginView
 */
public class RegisterView extends J<PERSON>rame {
    
    // Màu sắc chủ đạo
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240); // #0071F0
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69);
    private static final Color DANGER_COLOR = new Color(220, 53, 69);
    private static final Color WHITE_COLOR = Color.WHITE;
    private static final Color LIGHT_GRAY = new Color(248, 249, 250);
    
    // Components
    private JPanel mainPanel;
    private JPanel registerPanel;
    
    // Form fields
    private JTextField txtUsername;
    private JPasswordField txtPassword;
    private JPasswordField txtConfirmPassword;
    private JTextField txtFullName;
    private JTextField txtEmail;
    
    // Buttons
    private JButton btnRegister;
    private JButton btnCancel;
    private JButton btnBackToLogin;
    
    // Status
    private JLabel lblStatus;
    
    public RegisterView() {
        initComponents();
        setupLayout();
        setupFrame();
    }
    
    /**
     * Khởi tạo components
     */
    private void initComponents() {
        // Main panel
        mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBackground(LIGHT_GRAY);
        
        // Register panel
        registerPanel = new JPanel();
        registerPanel.setLayout(new BoxLayout(registerPanel, BoxLayout.Y_AXIS));
        registerPanel.setBackground(WHITE_COLOR);
        registerPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(PRIMARY_COLOR, 2),
            BorderFactory.createEmptyBorder(40, 40, 40, 40)
        ));
        
        // Form fields
        txtUsername = new JTextField();
        txtPassword = new JPasswordField();
        txtConfirmPassword = new JPasswordField();
        txtFullName = new JTextField();
        txtEmail = new JTextField();
        
        // Style form fields
        styleTextField(txtUsername);
        styleTextField(txtPassword);
        styleTextField(txtConfirmPassword);
        styleTextField(txtFullName);
        styleTextField(txtEmail);
        
        // Buttons
        btnRegister = new JButton("Đăng ký");
        btnCancel = new JButton("Hủy");
        btnBackToLogin = new JButton("Quay lại Đăng nhập");
        
        styleButton(btnRegister, PRIMARY_COLOR);
        styleButton(btnCancel, DANGER_COLOR);
        styleButton(btnBackToLogin, SUCCESS_COLOR);
        
        // Status label
        lblStatus = new JLabel("Vui lòng điền thông tin đăng ký");
        lblStatus.setFont(new Font("Arial", Font.PLAIN, 14));
        lblStatus.setForeground(PRIMARY_COLOR);
        lblStatus.setAlignmentX(Component.CENTER_ALIGNMENT);
    }
    
    /**
     * Style text field
     */
    private void styleTextField(JComponent field) {
        field.setPreferredSize(new Dimension(300, 40));
        field.setMaximumSize(new Dimension(300, 40));
        field.setFont(new Font("Arial", Font.PLAIN, 16));
        field.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(PRIMARY_COLOR, 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));
    }
    
    /**
     * Style button
     */
    private void styleButton(JButton button, Color color) {
        button.setPreferredSize(new Dimension(140, 45));
        button.setMaximumSize(new Dimension(140, 45));
        button.setFont(new Font("Arial", Font.BOLD, 16));
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));
        button.setFocusPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setAlignmentX(Component.CENTER_ALIGNMENT);
    }
    
    /**
     * Setup layout
     */
    private void setupLayout() {
        // Title
        JLabel titleLabel = new JLabel("ĐĂNG KÝ TÀI KHOẢN");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(PRIMARY_COLOR);
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        // Form fields with labels
        registerPanel.add(titleLabel);
        registerPanel.add(Box.createVerticalStrut(30));
        
        addFormField("Tên đăng nhập:", txtUsername);
        addFormField("Mật khẩu:", txtPassword);
        addFormField("Xác nhận mật khẩu:", txtConfirmPassword);
        addFormField("Họ và tên:", txtFullName);
        addFormField("Email:", txtEmail);
        
        registerPanel.add(Box.createVerticalStrut(20));
        
        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 0));
        buttonPanel.setBackground(WHITE_COLOR);
        buttonPanel.add(btnRegister);
        buttonPanel.add(btnCancel);
        
        registerPanel.add(buttonPanel);
        registerPanel.add(Box.createVerticalStrut(15));
        registerPanel.add(btnBackToLogin);
        registerPanel.add(Box.createVerticalStrut(20));
        registerPanel.add(lblStatus);
        
        // Add to main panel
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.CENTER;
        
        mainPanel.add(registerPanel, gbc);
    }
    
    /**
     * Add form field with label
     */
    private void addFormField(String labelText, JComponent field) {
        JLabel label = new JLabel(labelText);
        label.setFont(new Font("Arial", Font.BOLD, 16));
        label.setForeground(Color.DARK_GRAY);
        label.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        registerPanel.add(label);
        registerPanel.add(Box.createVerticalStrut(8));
        registerPanel.add(field);
        registerPanel.add(Box.createVerticalStrut(15));
    }
    
    /**
     * Setup frame
     */
    private void setupFrame() {
        setTitle("Đăng ký - Hệ thống Quản lý Điểm thi Đại học");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(600, 700);
        setLocationRelativeTo(null);
        setResizable(false);
        
        add(mainPanel);
        
        // Set Look and Feel
        try {
            UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                SwingUtilities.updateComponentTreeUI(this);
            } catch (Exception ex) {
                // Use default look and feel
            }
        }
    }
    
    // Getter methods for controller
    public String getUsername() { return txtUsername.getText().trim(); }
    public String getPassword() { return new String(txtPassword.getPassword()); }
    public String getConfirmPassword() { return new String(txtConfirmPassword.getPassword()); }
    public String getFullName() { return txtFullName.getText().trim(); }
    public String getEmail() { return txtEmail.getText().trim(); }
    
    // Clear form
    public void clearForm() {
        txtUsername.setText("");
        txtPassword.setText("");
        txtConfirmPassword.setText("");
        txtFullName.setText("");
        txtEmail.setText("");
        txtUsername.requestFocus();
    }
    
    // Status methods
    public void updateStatus(String message) {
        lblStatus.setText(message);
        lblStatus.setForeground(PRIMARY_COLOR);
    }
    
    public void showError(String message) {
        lblStatus.setText("Lỗi: " + message);
        lblStatus.setForeground(DANGER_COLOR);
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }
    
    public void showSuccess(String message) {
        lblStatus.setText(message);
        lblStatus.setForeground(SUCCESS_COLOR);
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }
    
    // ActionListener methods
    public void addRegisterListener(ActionListener listener) { btnRegister.addActionListener(listener); }
    public void addCancelListener(ActionListener listener) { btnCancel.addActionListener(listener); }
    public void addBackToLoginListener(ActionListener listener) { btnBackToLogin.addActionListener(listener); }
}
