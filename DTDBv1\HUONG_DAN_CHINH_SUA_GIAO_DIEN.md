# HƯỚNG DẪN CHỈNH SỬA GIAO DIỆN
## Hệ thống Quản lý Điểm thi Đại học

### 📋 MỤC LỤC
1. [C<PERSON>u trúc dự án](#cấu-trúc-dự-án)
2. [<PERSON><PERSON>u sắc và Theme](#màu-sắc-và-theme)
3. [Typography và Font](#typography-và-font)
4. [Layout và Spacing](#layout-và-spacing)
5. [Component Styling](#component-styling)
6. [Các file cần chỉnh sửa](#các-file-cần-chỉnh-sửa)
7. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## 🏗️ CẤU TRÚC DỰ ÁN

```
DTDBv1/src/main/java/com/mycompany/quanlydoituongdacbiet/
├── view/                    # Các file giao diện
│   ├── LoginManagementView.java
│   ├── ThiSinhManagementView.java
│   ├── MonThiManagementView.java
│   ├── KhoiThiManagementView.java
│   ├── DiemThiManagementView.java
│   └── RegisterView.java
├── controller/              # Các file điều khiển
├── model/                   # Các file model
├── service/                 # Các file service
└── QuanLyDoiTuong/
    └── MainApplication.java # File chạy chính
```

---

## 🎨 MÀU SẮC VÀ THEME

### Màu chính được sử dụng:
```java
// Trong mỗi file View, các màu được định nghĩa như sau:
private static final Color PRIMARY_COLOR = new Color(0, 113, 240);    // #0071F0 - Xanh chính
private static final Color SUCCESS_COLOR = new Color(40, 167, 69);     // #28A745 - Xanh lá
private static final Color DANGER_COLOR = new Color(220, 53, 69);      // #DC3545 - Đỏ
private static final Color WARNING_COLOR = new Color(255, 193, 7);     // #FFC107 - Vàng

// Màu background
private static final Color LIGHT_BACKGROUND = new Color(248, 249, 250); // #F8F9FA
private static final Color WHITE_BACKGROUND = Color.WHITE;               // #FFFFFF

// Màu border
private static final Color BORDER_COLOR = new Color(222, 226, 230);     // #DEE2E6
private static final Color INPUT_BORDER = new Color(206, 212, 218);     // #CED4DA
```

### Cách thay đổi màu sắc:
1. **Thay đổi màu chính của ứng dụng:**
   ```java
   // Tìm dòng này trong file View
   private static final Color PRIMARY_COLOR = new Color(0, 113, 240);
   
   // Thay đổi thành màu mới (ví dụ: màu tím)
   private static final Color PRIMARY_COLOR = new Color(102, 51, 153);
   ```

2. **Thay đổi màu background:**
   ```java
   // Tìm dòng này trong setupLayout()
   getContentPane().setBackground(new Color(248, 249, 250));
   
   // Thay đổi thành màu khác
   getContentPane().setBackground(new Color(240, 240, 240));
   ```

---

## 📝 TYPOGRAPHY VÀ FONT

### Font được sử dụng:
```java
// Title lớn (24px, Bold)
new Font("Arial", Font.BOLD, 24)

// Title trung (16px, Bold) 
new Font("Arial", Font.BOLD, 16)

// Label (14px, Bold)
new Font("Arial", Font.BOLD, 14)

// Text thường (14px, Plain)
new Font("Arial", Font.PLAIN, 14)

// Text nhỏ (12px)
new Font("Arial", Font.PLAIN, 12)
```

### Cách thay đổi font:
1. **Thay đổi font family:**
   ```java
   // Từ Arial sang Times New Roman
   titleLabel.setFont(new Font("Times New Roman", Font.BOLD, 24));
   ```

2. **Thay đổi kích thước:**
   ```java
   // Tăng kích thước title từ 24 lên 28
   titleLabel.setFont(new Font("Arial", Font.BOLD, 28));
   ```

---

## 📐 LAYOUT VÀ SPACING

### Padding và Margin chuẩn:
```java
// Main padding (20px)
mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

// Component spacing (12px)
gbc.insets = new Insets(12, 10, 12, 10);

// Button spacing (10px)
new FlowLayout(FlowLayout.CENTER, 10, 20)

// Title spacing (15px bottom)
titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));
```

### Cách thay đổi spacing:
1. **Tăng/giảm padding chính:**
   ```java
   // Từ 20px thành 30px
   mainPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
   ```

2. **Thay đổi spacing giữa components:**
   ```java
   // Từ 12px thành 15px
   gbc.insets = new Insets(15, 12, 15, 12);
   ```

---

## 🎛️ COMPONENT STYLING

### Kích thước chuẩn:
```java
// Text fields chính
txtField.setPreferredSize(new Dimension(250, 35));

// Text fields tìm kiếm  
txtSearch.setPreferredSize(new Dimension(200, 35));

// Buttons
button.setPreferredSize(new Dimension(100, 35));

// ComboBox
comboBox.setPreferredSize(new Dimension(250, 35));
```

### Border styling:
```java
// Border cho text fields
txtField.setBorder(BorderFactory.createCompoundBorder(
    BorderFactory.createLineBorder(new Color(206, 212, 218), 1),
    BorderFactory.createEmptyBorder(8, 12, 8, 12)
));

// Border cho panels
panel.setBorder(BorderFactory.createCompoundBorder(
    BorderFactory.createLineBorder(new Color(222, 226, 230), 1),
    BorderFactory.createEmptyBorder(20, 20, 20, 20)
));
```

### Button styling:
```java
// Button chính (Primary)
button.setBackground(PRIMARY_COLOR);
button.setForeground(Color.WHITE);
button.setFont(new Font("Arial", Font.BOLD, 12));
button.setPreferredSize(new Dimension(100, 35));

// Button nguy hiểm (Danger)
button.setBackground(DANGER_COLOR);
button.setForeground(Color.WHITE);

// Button thành công (Success)
button.setBackground(SUCCESS_COLOR);
button.setForeground(Color.WHITE);
```

---

## 📁 CÁC FILE CẦN CHỈNH SỬA

### 1. **LoginManagementView.java** - Giao diện chính sau đăng nhập
**Vị trí:** `DTDBv1/src/main/java/com/mycompany/quanlydoituongdacbiet/view/LoginManagementView.java`

**Các phương thức quan trọng:**
- `setupLayout()` - Layout tổng thể
- `createHeaderPanel()` - Header với title
- `createButtonPanel()` - Panel chứa các nút menu
- `styleComponents()` - Styling cho components

### 2. **ThiSinhManagementView.java** - Quản lý thí sinh  
**Vị trí:** `DTDBv1/src/main/java/com/mycompany/quanlydoituongdacbiet/view/ThiSinhManagementView.java`

**Các phương thức quan trọng:**
- `setupLayout()` - Layout chính với split pane
- `createFormPanel()` - Form nhập liệu bên trái
- `createTablePanel()` - Bảng dữ liệu bên phải
- `styleComponents()` - Styling components

### 3. **MonThiManagementView.java** - Quản lý môn thi
**Vị trí:** `DTDBv1/src/main/java/com/mycompany/quanlydoituongdacbiet/view/MonThiManagementView.java`

### 4. **KhoiThiManagementView.java** - Quản lý khối thi
**Vị trí:** `DTDBv1/src/main/java/com/mycompany/quanlydoituongdacbiet/view/KhoiThiManagementView.java`

**Đặc biệt:** Có `createMonThiSelectionPanel()` - Panel chọn môn thi với dual JList

### 5. **DiemThiManagementView.java** - Quản lý điểm thi
**Vị trí:** `DTDBv1/src/main/java/com/mycompany/quanlydoituongdacbiet/view/DiemThiManagementView.java`

**Đặc biệt:** Có `createStatisticsPanel()` - Panel thống kê điểm

### 6. **RegisterView.java** - Giao diện đăng ký
**Vị trí:** `DTDBv1/src/main/java/com/mycompany/quanlydoituongdacbiet/view/RegisterView.java`

---

## 💡 VÍ DỤ THỰC TẾ

### Ví dụ 1: Thay đổi màu chính từ xanh sang đỏ

1. **Mở file:** `MonThiManagementView.java`
2. **Tìm dòng:**
   ```java
   private static final Color PRIMARY_COLOR = new Color(0, 113, 240);
   ```
3. **Thay thành:**
   ```java
   private static final Color PRIMARY_COLOR = new Color(220, 53, 69);
   ```
4. **Compile lại:** `javac -cp . com/mycompany/quanlydoituongdacbiet/view/MonThiManagementView.java`

### Ví dụ 2: Tăng kích thước text fields

1. **Mở file:** `ThiSinhManagementView.java`
2. **Tìm phương thức:** `styleComponents()`
3. **Tìm dòng:**
   ```java
   txtSoBaoDanh.setPreferredSize(new Dimension(250, 35));
   ```
4. **Thay thành:**
   ```java
   txtSoBaoDanh.setPreferredSize(new Dimension(300, 40));
   ```

### Ví dụ 3: Thay đổi font chữ

1. **Mở file:** `LoginManagementView.java`
2. **Tìm phương thức:** `createHeaderPanel()`
3. **Tìm dòng:**
   ```java
   titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
   ```
4. **Thay thành:**
   ```java
   titleLabel.setFont(new Font("Times New Roman", Font.BOLD, 28));
   ```

### Ví dụ 4: Thay đổi spacing

1. **Mở file:** `DiemThiManagementView.java`
2. **Tìm phương thức:** `setupLayout()`
3. **Tìm dòng:**
   ```java
   mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
   ```
4. **Thay thành:**
   ```java
   mainPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
   ```

---

## 🔧 COMPILE VÀ CHẠY

### Sau khi chỉnh sửa:

1. **Compile file đã sửa:**
   ```bash
   cd DTDBv1/src/main/java
   javac -cp . com/mycompany/quanlydoituongdacbiet/view/TenFileView.java
   ```

2. **Chạy ứng dụng:**
   ```bash
   java com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong.MainApplication
   ```

### Lưu ý quan trọng:
- Luôn backup file trước khi chỉnh sửa
- Test kỹ sau mỗi thay đổi
- Compile thành công trước khi chạy
- Giữ tính nhất quán về màu sắc và spacing

---

## 📞 HỖ TRỢ

Nếu gặp lỗi khi chỉnh sửa:
1. Kiểm tra syntax Java
2. Đảm bảo import đầy đủ
3. Compile lại file
4. Restart ứng dụng

**Chúc bạn thành công trong việc tùy chỉnh giao diện!** 🎉

---

## 🎯 CÁC THAY ĐỔI NÂNG CAO

### 1. Thêm hiệu ứng hover cho buttons
```java
// Trong styleComponents()
button.addMouseListener(new MouseAdapter() {
    @Override
    public void mouseEntered(MouseEvent e) {
        button.setBackground(button.getBackground().darker());
    }

    @Override
    public void mouseExited(MouseEvent e) {
        button.setBackground(PRIMARY_COLOR);
    }
});
```

### 2. Thay đổi Look and Feel
```java
// Trong MainApplication.java hoặc constructor của View
try {
    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
    // Hoặc
    UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");
} catch (Exception e) {
    e.printStackTrace();
}
```

### 3. Tùy chỉnh Table styling
```java
// Trong setupTable()
table.setGridColor(new Color(230, 230, 230));
table.setShowGrid(true);
table.setIntercellSpacing(new Dimension(1, 1));
table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

// Tùy chỉnh header
JTableHeader header = table.getTableHeader();
header.setPreferredSize(new Dimension(0, 40));
header.setReorderingAllowed(false);
```

### 4. Thêm icon cho buttons
```java
// Cần có file icon trong resources
ImageIcon icon = new ImageIcon("path/to/icon.png");
button.setIcon(icon);
button.setHorizontalTextPosition(SwingConstants.RIGHT);
```

### 5. Tạo custom border
```java
// Border bo góc
Border roundedBorder = new AbstractBorder() {
    @Override
    public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.setColor(new Color(206, 212, 218));
        g2.drawRoundRect(x, y, width - 1, height - 1, 10, 10);
        g2.dispose();
    }
};
```

---

## 🔍 TROUBLESHOOTING

### Lỗi thường gặp và cách khắc phục:

1. **Lỗi compile: "cannot find symbol"**
   ```
   Nguyên nhân: Thiếu import hoặc tên biến sai
   Khắc phục: Kiểm tra import và tên biến
   ```

2. **Giao diện không hiển thị đúng**
   ```
   Nguyên nhân: Layout manager không phù hợp
   Khắc phục: Kiểm tra BorderLayout, GridBagLayout
   ```

3. **Màu sắc không thay đổi**
   ```
   Nguyên nhân: Chưa compile lại hoặc cache
   Khắc phục: Compile lại và restart ứng dụng
   ```

4. **Font không hiển thị**
   ```
   Nguyên nhân: Font không có trong hệ thống
   Khắc phục: Dùng font mặc định như Arial, Times New Roman
   ```

---

## 📚 TÀI LIỆU THAM KHẢO

### Java Swing Components:
- **JPanel**: Container chứa các components khác
- **JLabel**: Hiển thị text hoặc icon
- **JTextField**: Input text
- **JButton**: Nút bấm
- **JTable**: Bảng dữ liệu
- **JComboBox**: Dropdown list
- **JScrollPane**: Scroll container

### Layout Managers:
- **BorderLayout**: North, South, East, West, Center
- **GridBagLayout**: Flexible grid layout
- **FlowLayout**: Sắp xếp theo dòng
- **GridLayout**: Grid cố định

### Color Codes phổ biến:
```java
// Màu Material Design
new Color(33, 150, 243)   // Blue
new Color(76, 175, 80)    // Green
new Color(255, 152, 0)    // Orange
new Color(156, 39, 176)   // Purple
new Color(96, 125, 139)   // Blue Grey
```

---

## 🎨 TEMPLATE MẪU

### Template cho một View mới:
```java
public class NewManagementView extends JFrame {
    // Colors
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240);
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69);
    private static final Color DANGER_COLOR = new Color(220, 53, 69);

    // Components
    private JPanel mainPanel;
    private JLabel titleLabel;

    public NewManagementView() {
        initComponents();
        setupLayout();
        styleComponents();
        setupEvents();
        setupWindow();
    }

    private void initComponents() {
        // Khởi tạo components
    }

    private void setupLayout() {
        setLayout(new BorderLayout());
        getContentPane().setBackground(new Color(248, 249, 250));

        // Main panel với padding
        mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(248, 249, 250));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        add(mainPanel, BorderLayout.CENTER);
    }

    private void styleComponents() {
        // Styling cho components
    }

    private void setupEvents() {
        // Event handlers
    }

    private void setupWindow() {
        setTitle("Tiêu đề");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setResizable(true);
    }
}
```

**File hướng dẫn đã hoàn thành!** 📖✨
