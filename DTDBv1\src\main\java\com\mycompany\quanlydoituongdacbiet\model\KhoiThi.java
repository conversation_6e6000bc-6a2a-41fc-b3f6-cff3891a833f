package com.mycompany.quanlydoituongdacbiet.model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Class đại diện cho khối thi
 */
public class KhoiThi {
    private String maKhoi;
    private String tenKhoi;
    private List<String> danhSachMon;
    private String moTa;
    
    // Constructor mặc định
    public KhoiThi() {
        danhSachMon = new ArrayList<>();
    }
    
    // Constructor đầy đủ
    public KhoiThi(String maKhoi, String tenKhoi, List<String> danhSachMon, String moTa) {
        this.maKhoi = maKhoi;
        this.tenKhoi = tenKhoi;
        this.danhSachMon = danhSachMon != null ? danhSachMon : new ArrayList<>();
        this.moTa = moTa;
    }
    
    /**
     * Chuyển đổi từ string trong file thành object
     */
    public static KhoiThi fromString(String line) {
        try {
            String[] parts = line.split("\\|");
            if (parts.length >= 4) {
                KhoiThi khoiThi = new KhoiThi();
                khoiThi.maKhoi = parts[0];
                khoiThi.tenKhoi = parts[1];
                // Parse danh sách môn (ngăn cách bởi dấu phẩy)
                if (!parts[2].trim().isEmpty()) {
                    khoiThi.danhSachMon = Arrays.asList(parts[2].split(","));
                }
                khoiThi.moTa = parts[3];
                return khoiThi;
            }
        } catch (Exception e) {
            System.err.println("Lỗi parse KhoiThi: " + line);
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Chuyển đổi object thành string để lưu vào file
     */
    @Override
    public String toString() {
        String monString = danhSachMon != null ? String.join(",", danhSachMon) : "";
        return maKhoi + "|" + tenKhoi + "|" + monString + "|" + moTa;
    }
    
    // Getters and Setters
    public String getMaKhoi() { return maKhoi; }
    public void setMaKhoi(String maKhoi) { this.maKhoi = maKhoi; }
    
    public String getTenKhoi() { return tenKhoi; }
    public void setTenKhoi(String tenKhoi) { this.tenKhoi = tenKhoi; }
    
    public List<String> getDanhSachMon() { return danhSachMon; }
    public void setDanhSachMon(List<String> danhSachMon) { 
        this.danhSachMon = danhSachMon != null ? danhSachMon : new ArrayList<>(); 
    }
    
    public String getMoTa() { return moTa; }
    public void setMoTa(String moTa) { this.moTa = moTa; }
    
    /**
     * Thêm môn vào khối
     */
    public void addMon(String maMon) {
        if (danhSachMon == null) {
            danhSachMon = new ArrayList<>();
        }
        if (!danhSachMon.contains(maMon)) {
            danhSachMon.add(maMon);
        }
    }
    
    /**
     * Xóa môn khỏi khối
     */
    public void removeMon(String maMon) {
        if (danhSachMon != null) {
            danhSachMon.remove(maMon);
        }
    }
}