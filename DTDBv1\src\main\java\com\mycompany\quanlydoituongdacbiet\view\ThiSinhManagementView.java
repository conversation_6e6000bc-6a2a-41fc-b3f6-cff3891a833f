package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import com.mycompany.quanlydoituongdacbiet.model.KhoiThi;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Màn hình quản lý thí sinh
 */
public class ThiSinhManagementView extends JFrame {
    
    // Components cho form nhập liệu
    private JTextField txtSoBaoDanh, txtHoTen, txtDiaChi, txtSoDienThoai, txtEmail;
    private JComboBox<String> cmbGioiTinh, cmbKhoiThi;
    private JSpinner spnNgaySinh;
    
    // Components cho bảng dữ liệu
    private JTable tableThiSinh;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    
    // Components cho tìm kiếm
    private JTextField txtTimKiem;
    private JButton btnTimKiem, btnLamMoi;
    
    // Components cho thao tác
    private JButton btnThem, btnSua, btnXoa, btnLuu, btnHuy;
    
    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingSoBaoDanh = null;
    
    public ThiSinhManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupEvents();
        setupWindow();
        resetForm();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components
        txtSoBaoDanh = new JTextField(15);
        txtHoTen = new JTextField(25);
        txtDiaChi = new JTextField(30);
        txtSoDienThoai = new JTextField(15);
        txtEmail = new JTextField(25);
        
        // ComboBox
        cmbGioiTinh = new JComboBox<>(new String[]{"Nam", "Nữ"});
        cmbKhoiThi = new JComboBox<>();
        
        // Date spinner
        spnNgaySinh = new JSpinner(new SpinnerDateModel());
        JSpinner.DateEditor dateEditor = new JSpinner.DateEditor(spnNgaySinh, "dd/MM/yyyy");
        spnNgaySinh.setEditor(dateEditor);
        
        // Search components
        txtTimKiem = new JTextField(20);
        btnTimKiem = new JButton("Tìm kiếm");
        btnLamMoi = new JButton("Làm mới");
        
        // Action buttons
        btnThem = new JButton("Thêm thí sinh");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        btnLuu = new JButton("Lưu");
        btnHuy = new JButton("Hủy");
        
        // Status
        lblStatus = new JLabel("Sẵn sàng");
        
        // Table
        String[] columnNames = {"Số báo danh", "Họ tên", "Giới tính", "Ngày sinh", 
                               "Địa chỉ", "Số điện thoại", "Email", "Khối thi"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho edit trực tiếp trên table
            }
        };
        tableThiSinh = new JTable(tableModel);
        scrollPane = new JScrollPane(tableThiSinh);
    }
    
    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Top panel - Form nhập liệu
        JPanel topPanel = createFormPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // Center panel - Table và tìm kiếm
        JPanel centerPanel = createTablePanel();
        add(centerPanel, BorderLayout.CENTER);
        
        // Bottom panel - Status và buttons
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Tạo panel form nhập liệu
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Thông tin thí sinh"));
        
        // Form fields
        JPanel formPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Row 1
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(new JLabel("Số báo danh:"), gbc);
        gbc.gridx = 1;
        formPanel.add(txtSoBaoDanh, gbc);
        
        gbc.gridx = 2;
        formPanel.add(new JLabel("Họ tên:"), gbc);
        gbc.gridx = 3;
        formPanel.add(txtHoTen, gbc);
        
        // Row 2
        gbc.gridx = 0; gbc.gridy = 1;
        formPanel.add(new JLabel("Giới tính:"), gbc);
        gbc.gridx = 1;
        formPanel.add(cmbGioiTinh, gbc);
        
        gbc.gridx = 2;
        formPanel.add(new JLabel("Ngày sinh:"), gbc);
        gbc.gridx = 3;
        formPanel.add(spnNgaySinh, gbc);
        
        // Row 3
        gbc.gridx = 0; gbc.gridy = 2;
        formPanel.add(new JLabel("Địa chỉ:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(txtDiaChi, gbc);
        
        // Row 4
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 1; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Số điện thoại:"), gbc);
        gbc.gridx = 1;
        formPanel.add(txtSoDienThoai, gbc);
        
        gbc.gridx = 2;
        formPanel.add(new JLabel("Email:"), gbc);
        gbc.gridx = 3;
        formPanel.add(txtEmail, gbc);
        
        // Row 5
        gbc.gridx = 0; gbc.gridy = 4;
        formPanel.add(new JLabel("Khối thi:"), gbc);
        gbc.gridx = 1;
        formPanel.add(cmbKhoiThi, gbc);
        
        panel.add(formPanel, BorderLayout.CENTER);
        
        // Action buttons
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(btnThem);
        buttonPanel.add(btnSua);
        buttonPanel.add(btnXoa);
        buttonPanel.add(btnLuu);
        buttonPanel.add(btnHuy);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * Tạo panel table và tìm kiếm
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Danh sách thí sinh"));
        
        // Search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.add(new JLabel("Tìm kiếm:"));
        searchPanel.add(txtTimKiem);
        searchPanel.add(btnTimKiem);
        searchPanel.add(btnLamMoi);
        
        panel.add(searchPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * Tạo panel bottom
     */
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createLoweredBevelBorder());
        panel.add(lblStatus, BorderLayout.WEST);
        return panel;
    }
    
    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableThiSinh.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        tableThiSinh.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedThiSinh();
            }
        });
        
        // Set column widths
        tableThiSinh.getColumnModel().getColumn(0).setPreferredWidth(100); // Số báo danh
        tableThiSinh.getColumnModel().getColumn(1).setPreferredWidth(200); // Họ tên
        tableThiSinh.getColumnModel().getColumn(2).setPreferredWidth(80);  // Giới tính
        tableThiSinh.getColumnModel().getColumn(3).setPreferredWidth(100); // Ngày sinh
        tableThiSinh.getColumnModel().getColumn(4).setPreferredWidth(200); // Địa chỉ
        tableThiSinh.getColumnModel().getColumn(5).setPreferredWidth(120); // SĐT
        tableThiSinh.getColumnModel().getColumn(6).setPreferredWidth(200); // Email
        tableThiSinh.getColumnModel().getColumn(7).setPreferredWidth(80);  // Khối thi
    }
    
    /**
     * Thiết lập events
     */
    private void setupEvents() {
        // Button events sẽ được set từ controller
    }
    
    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản lý Thí sinh");
        setSize(1000, 700);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
    }
    
    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        txtSoBaoDanh.setText("");
        txtHoTen.setText("");
        txtDiaChi.setText("");
        txtSoDienThoai.setText("");
        txtEmail.setText("");
        cmbGioiTinh.setSelectedIndex(0);
        spnNgaySinh.setValue(new Date());
        cmbKhoiThi.setSelectedIndex(-1);
        
        txtSoBaoDanh.setEnabled(true);
        isEditing = false;
        editingSoBaoDanh = null;
        
        btnThem.setEnabled(true);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(false);
        btnHuy.setEnabled(false);
        
        tableThiSinh.clearSelection();
        updateStatus("Sẵn sàng");
    }
    
    /**
     * Load thí sinh được chọn từ table
     */
    private void loadSelectedThiSinh() {
        int selectedRow = tableThiSinh.getSelectedRow();
        if (selectedRow >= 0) {
            txtSoBaoDanh.setText(tableModel.getValueAt(selectedRow, 0).toString());
            txtHoTen.setText(tableModel.getValueAt(selectedRow, 1).toString());
            cmbGioiTinh.setSelectedItem(tableModel.getValueAt(selectedRow, 2));
            
            // Parse ngày sinh
            try {
                String ngaySinhStr = tableModel.getValueAt(selectedRow, 3).toString();
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                Date ngaySinh = sdf.parse(ngaySinhStr);
                spnNgaySinh.setValue(ngaySinh);
            } catch (Exception e) {
                spnNgaySinh.setValue(new Date());
            }
            
            txtDiaChi.setText(tableModel.getValueAt(selectedRow, 4).toString());
            txtSoDienThoai.setText(tableModel.getValueAt(selectedRow, 5).toString());
            txtEmail.setText(tableModel.getValueAt(selectedRow, 6).toString());
            cmbKhoiThi.setSelectedItem(tableModel.getValueAt(selectedRow, 7));
            
            btnSua.setEnabled(true);
            btnXoa.setEnabled(true);
            btnThem.setEnabled(false);
        } else {
            resetForm();
        }
    }
    
    /**
     * Cập nhật trạng thái
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }
    
    /**
     * Load danh sách khối thi vào combobox
     */
    public void loadKhoiThiData(List<KhoiThi> khoiThis) {
        cmbKhoiThi.removeAllItems();
        for (KhoiThi khoi : khoiThis) {
            cmbKhoiThi.addItem(khoi.getMaKhoi() + " - " + khoi.getTenKhoi());
        }
    }
    
    /**
     * Load danh sách thí sinh vào table
     */
    public void loadThiSinhData(List<ThiSinh> thiSinhs) {
        tableModel.setRowCount(0);
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        
        for (ThiSinh ts : thiSinhs) {
            Object[] row = {
                ts.getSoBaoDanh(),
                ts.getHoTen(),
                ts.getGioiTinh(),
                sdf.format(ts.getNgaySinh()),
                ts.getDiaChi(),
                ts.getSoDienThoai(),
                ts.getEmail(),
                ts.getMaKhoi()
            };
            tableModel.addRow(row);
        }
        updateStatus("Đã load " + thiSinhs.size() + " thí sinh");
    }
    
    /**
     * Lấy thông tin thí sinh từ form
     */
    public ThiSinh getThiSinhFromForm() {
        ThiSinh thiSinh = new ThiSinh();
        thiSinh.setSoBaoDanh(txtSoBaoDanh.getText().trim());
        thiSinh.setHoTen(txtHoTen.getText().trim());
        thiSinh.setGioiTinh(cmbGioiTinh.getSelectedItem().toString());
        thiSinh.setNgaySinh((Date) spnNgaySinh.getValue());
        thiSinh.setDiaChi(txtDiaChi.getText().trim());
        thiSinh.setSoDienThoai(txtSoDienThoai.getText().trim());
        thiSinh.setEmail(txtEmail.getText().trim());
        
        String khoiSelection = (String) cmbKhoiThi.getSelectedItem();
        if (khoiSelection != null && !khoiSelection.isEmpty()) {
            thiSinh.setMaKhoi(khoiSelection.split(" - ")[0]);
        }
        
        return thiSinh;
    }
    
    /**
     * Kiểm tra validation form
     */
    public boolean validateForm() {
        if (txtSoBaoDanh.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập số báo danh!");
            txtSoBaoDanh.requestFocus();
            return false;
        }
        
        if (txtHoTen.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập họ tên!");
            txtHoTen.requestFocus();
            return false;
        }
        
        if (cmbKhoiThi.getSelectedIndex() < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn khối thi!");
            cmbKhoiThi.requestFocus();
            return false;
        }
        
        return true;
    }
    
    /**
     * Bắt đầu chế độ editing
     */
    public void startEditing() {
        isEditing = true;
        editingSoBaoDanh = txtSoBaoDanh.getText();
        txtSoBaoDanh.setEnabled(false);
        
        btnThem.setEnabled(false);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(true);
        btnHuy.setEnabled(true);
        
        updateStatus("Đang chỉnh sửa thí sinh: " + editingSoBaoDanh);
    }
    
    /**
     * Bắt đầu chế độ thêm mới
     */
    public void startAdding() {
        resetForm();
        isEditing = false;
        
        btnThem.setEnabled(false);
        btnSua.setEnabled(false);
        btnXoa.setEnabled(false);
        btnLuu.setEnabled(true);
        btnHuy.setEnabled(true);
        
        txtSoBaoDanh.requestFocus();
        updateStatus("Đang thêm thí sinh mới");
    }
    
    // ==================== GETTER/SETTER FOR EVENTS ====================
    
    public void setThemActionListener(ActionListener listener) {
        btnThem.addActionListener(listener);
    }
    
    public void setSuaActionListener(ActionListener listener) {
        btnSua.addActionListener(listener);
    }
    
    public void setXoaActionListener(ActionListener listener) {
        btnXoa.addActionListener(listener);
    }
    
    public void setLuuActionListener(ActionListener listener) {
        btnLuu.addActionListener(listener);
    }
    
    public void setHuyActionListener(ActionListener listener) {
        btnHuy.addActionListener(listener);
    }
    
    public void setTimKiemActionListener(ActionListener listener) {
        btnTimKiem.addActionListener(listener);
    }
    
    public void setLamMoiActionListener(ActionListener listener) {
        btnLamMoi.addActionListener(listener);
    }
    
    public String getSearchText() {
        return txtTimKiem.getText().trim();
    }
    
    public boolean isEditing() {
        return isEditing;
    }
    
    public String getEditingSoBaoDanh() {
        return editingSoBaoDanh;
    }

    // Methods for controller integration
    public void addAddListener(ActionListener listener) {
        btnThem.addActionListener(listener);
    }

    public void addEditListener(ActionListener listener) {
        btnSua.addActionListener(listener);
    }

    public void addDeleteListener(ActionListener listener) {
        btnXoa.addActionListener(listener);
    }

    public void addSaveListener(ActionListener listener) {
        btnLuu.addActionListener(listener);
    }

    public void addCancelListener(ActionListener listener) {
        btnHuy.addActionListener(listener);
    }

    public void addSearchListener(ActionListener listener) {
        btnTimKiem.addActionListener(listener);
    }

    public void addRefreshListener(ActionListener listener) {
        btnLamMoi.addActionListener(listener);
    }

    public int getSelectedRow() {
        return tableThiSinh.getSelectedRow();
    }

    public String getSelectedSoBaoDanh() {
        int selectedRow = getSelectedRow();
        if (selectedRow >= 0) {
            return (String) tableModel.getValueAt(selectedRow, 0);
        }
        return null;
    }

    public void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    public void showSuccess(String message) {
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
        updateStatus(message);
    }

    public void clearSearchText() {
        txtTimKiem.setText("");
    }

    // Overload method for String array
    public void loadKhoiThiData(String[] khoiThis) {
        cmbKhoiThi.removeAllItems();
        for (String khoi : khoiThis) {
            cmbKhoiThi.addItem(khoi);
        }
    }
}