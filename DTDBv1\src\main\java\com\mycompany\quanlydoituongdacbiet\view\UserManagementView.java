package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.User;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * <PERSON><PERSON>o diện quản lý người dùng - Thiết kế theo chuẩn ThiSinhManagementView
 */
public class UserManagementView extends JFrame {
    
    // Màu sắc chủ đạo
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240); // #0071F0
    private static final Color SECONDARY_COLOR = new Color(108, 117, 125); // #6c757d
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69); // #28a745
    private static final Color DANGER_COLOR = new Color(220, 53, 69); // #dc3545
    private static final Color LIGHT_COLOR = new Color(248, 249, 250); // #f8f9fa
    private static final Color WHITE_COLOR = Color.WHITE;
    
    // Components cho form nhập liệu
    private JTextField txtUsername, txtFullName, txtEmail;
    private JPasswordField txtPassword, txtConfirmPassword;
    private JComboBox<String> cmbRole;
    
    // Components cho bảng dữ liệu
    private JTable tableUsers;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    
    // Components cho tìm kiếm
    private JTextField txtSearch;
    private JButton btnSearch, btnRefresh;
    
    // Components cho thao tác
    private JButton btnAdd, btnEdit, btnDelete, btnSave, btnCancel, btnLogout;
    
    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingUsername = null;
    
    public UserManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupEvents();
        setupWindow();
        resetForm();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components
        txtUsername = new JTextField(20);
        txtFullName = new JTextField(25);
        txtEmail = new JTextField(25);
        txtPassword = new JPasswordField(20);
        txtConfirmPassword = new JPasswordField(20);
        
        // ComboBox
        cmbRole = new JComboBox<>(new String[]{"USER", "ADMIN"});
        
        // Search components
        txtSearch = new JTextField(20);
        btnSearch = new JButton("Tìm kiếm");
        btnRefresh = new JButton("Làm mới");
        
        // Action buttons
        btnAdd = new JButton("Thêm người dùng");
        btnEdit = new JButton("Sửa");
        btnDelete = new JButton("Xóa");
        btnSave = new JButton("Lưu");
        btnCancel = new JButton("Hủy");
        btnLogout = new JButton("Đăng xuất");
        
        // Styling buttons
        styleButton(btnAdd, PRIMARY_COLOR);
        styleButton(btnEdit, SECONDARY_COLOR);
        styleButton(btnDelete, DANGER_COLOR);
        styleButton(btnSave, SUCCESS_COLOR);
        styleButton(btnCancel, SECONDARY_COLOR);
        styleButton(btnLogout, DANGER_COLOR);
        styleButton(btnSearch, PRIMARY_COLOR);
        styleButton(btnRefresh, SECONDARY_COLOR);
        
        // Status
        lblStatus = new JLabel("Sẵn sàng");
        lblStatus.setFont(new Font("Arial", Font.PLAIN, 14));
        
        // Table
        String[] columnNames = {"Tên đăng nhập", "Họ tên", "Email", "Vai trò", "Trạng thái"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        tableUsers = new JTable(tableModel);
        scrollPane = new JScrollPane(tableUsers);
    }
    
    /**
     * Styling cho buttons
     */
    private void styleButton(JButton button, Color bgColor) {
        button.setBackground(bgColor);
        button.setForeground(WHITE_COLOR);
        button.setFont(new Font("Arial", Font.BOLD, 14));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(140, 35));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
    }
    
    /**
     * Thiết lập layout theo chuẩn ThiSinhManagementView
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        getContentPane().setBackground(LIGHT_COLOR);
        
        // Top panel - Form nhập liệu
        JPanel topPanel = createFormPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // Center panel - Table và tìm kiếm
        JPanel centerPanel = createTablePanel();
        add(centerPanel, BorderLayout.CENTER);
        
        // Bottom panel - Status và logout
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Tạo panel form nhập liệu
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(WHITE_COLOR);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(PRIMARY_COLOR, 2),
                "Thông tin người dùng",
                0, 0, new Font("Arial", Font.BOLD, 16), PRIMARY_COLOR
            ),
            BorderFactory.createEmptyBorder(10, 10, 10, 10)
        ));
        
        // Form fields
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBackground(WHITE_COLOR);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Row 1
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(createLabel("Tên đăng nhập:"), gbc);
        gbc.gridx = 1;
        formPanel.add(txtUsername, gbc);
        
        gbc.gridx = 2;
        formPanel.add(createLabel("Họ tên:"), gbc);
        gbc.gridx = 3;
        formPanel.add(txtFullName, gbc);
        
        // Row 2
        gbc.gridx = 0; gbc.gridy = 1;
        formPanel.add(createLabel("Email:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(txtEmail, gbc);
        
        // Row 3
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 1; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(createLabel("Mật khẩu:"), gbc);
        gbc.gridx = 1;
        formPanel.add(txtPassword, gbc);
        
        gbc.gridx = 2;
        formPanel.add(createLabel("Xác nhận MK:"), gbc);
        gbc.gridx = 3;
        formPanel.add(txtConfirmPassword, gbc);
        
        // Row 4
        gbc.gridx = 0; gbc.gridy = 3;
        formPanel.add(createLabel("Vai trò:"), gbc);
        gbc.gridx = 1;
        formPanel.add(cmbRole, gbc);
        
        panel.add(formPanel, BorderLayout.CENTER);
        
        // Action buttons
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 10));
        buttonPanel.setBackground(WHITE_COLOR);
        buttonPanel.add(btnAdd);
        buttonPanel.add(btnEdit);
        buttonPanel.add(btnDelete);
        buttonPanel.add(btnSave);
        buttonPanel.add(btnCancel);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * Tạo label với style
     */
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(new Font("Arial", Font.BOLD, 14));
        label.setForeground(new Color(33, 37, 41));
        return label;
    }
    
    /**
     * Tạo panel table và tìm kiếm
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(WHITE_COLOR);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(PRIMARY_COLOR, 2),
                "Danh sách người dùng",
                0, 0, new Font("Arial", Font.BOLD, 16), PRIMARY_COLOR
            ),
            BorderFactory.createEmptyBorder(10, 10, 10, 10)
        ));
        
        // Search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 10));
        searchPanel.setBackground(WHITE_COLOR);
        searchPanel.add(createLabel("Tìm kiếm:"));
        searchPanel.add(txtSearch);
        searchPanel.add(btnSearch);
        searchPanel.add(btnRefresh);
        
        panel.add(searchPanel, BorderLayout.NORTH);
        
        // Table với styling
        tableUsers.setFont(new Font("Arial", Font.PLAIN, 13));
        tableUsers.setRowHeight(25);
        tableUsers.getTableHeader().setFont(new Font("Arial", Font.BOLD, 14));
        tableUsers.getTableHeader().setBackground(PRIMARY_COLOR);
        tableUsers.getTableHeader().setForeground(WHITE_COLOR);
        tableUsers.setSelectionBackground(new Color(0, 113, 240, 50));
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * Tạo panel bottom
     */
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(LIGHT_COLOR);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLoweredBevelBorder(),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        
        panel.add(lblStatus, BorderLayout.WEST);
        
        // Logout button ở góc phải
        JPanel logoutPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        logoutPanel.setBackground(LIGHT_COLOR);
        logoutPanel.add(btnLogout);
        panel.add(logoutPanel, BorderLayout.EAST);
        
        return panel;
    }
    
    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableUsers.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        tableUsers.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedUser();
            }
        });
        
        // Set column widths
        tableUsers.getColumnModel().getColumn(0).setPreferredWidth(150); // Username
        tableUsers.getColumnModel().getColumn(1).setPreferredWidth(200); // Full name
        tableUsers.getColumnModel().getColumn(2).setPreferredWidth(250); // Email
        tableUsers.getColumnModel().getColumn(3).setPreferredWidth(100); // Role
        tableUsers.getColumnModel().getColumn(4).setPreferredWidth(100); // Status
    }
    
    /**
     * Thiết lập events
     */
    private void setupEvents() {
        // Button events sẽ được set từ controller
    }
    
    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản lý Người dùng - Hệ thống Quản lý Điểm thi Đại học");
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        
        // Icon và styling
        try {
            setIconImage(Toolkit.getDefaultToolkit().getImage("icon.png"));
        } catch (Exception e) {
            // Ignore if icon not found
        }
    }

    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        txtUsername.setText("");
        txtFullName.setText("");
        txtEmail.setText("");
        txtPassword.setText("");
        txtConfirmPassword.setText("");
        cmbRole.setSelectedIndex(0);

        txtUsername.setEnabled(true);
        isEditing = false;
        editingUsername = null;

        btnAdd.setEnabled(true);
        btnEdit.setEnabled(false);
        btnDelete.setEnabled(false);
        btnSave.setEnabled(false);
        btnCancel.setEnabled(false);

        tableUsers.clearSelection();
        updateStatus("Sẵn sàng");
    }

    /**
     * Load user được chọn từ table
     */
    private void loadSelectedUser() {
        int selectedRow = tableUsers.getSelectedRow();
        if (selectedRow >= 0) {
            txtUsername.setText(tableModel.getValueAt(selectedRow, 0).toString());
            txtFullName.setText(tableModel.getValueAt(selectedRow, 1).toString());
            txtEmail.setText(tableModel.getValueAt(selectedRow, 2).toString());
            cmbRole.setSelectedItem(tableModel.getValueAt(selectedRow, 3));

            txtPassword.setText("");
            txtConfirmPassword.setText("");

            btnEdit.setEnabled(true);
            btnDelete.setEnabled(true);
            btnAdd.setEnabled(false);
        }
    }

    /**
     * Cập nhật status
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
        lblStatus.setForeground(PRIMARY_COLOR);
    }

    /**
     * Hiển thị lỗi
     */
    public void showError(String message) {
        lblStatus.setText("Lỗi: " + message);
        lblStatus.setForeground(DANGER_COLOR);
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    /**
     * Hiển thị thành công
     */
    public void showSuccess(String message) {
        lblStatus.setText(message);
        lblStatus.setForeground(SUCCESS_COLOR);
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }

    // Getter methods
    public JTextField getTxtUsername() { return txtUsername; }
    public JTextField getTxtFullName() { return txtFullName; }
    public JTextField getTxtEmail() { return txtEmail; }
    public JPasswordField getTxtPassword() { return txtPassword; }
    public JPasswordField getTxtConfirmPassword() { return txtConfirmPassword; }
    public JComboBox<String> getCmbRole() { return cmbRole; }
    public JTextField getTxtSearch() { return txtSearch; }
    public DefaultTableModel getTableModel() { return tableModel; }
    public JTable getTableUsers() { return tableUsers; }
    public boolean isEditing() { return isEditing; }
    public String getEditingUsername() { return editingUsername; }

    // Setter methods
    public void setEditing(boolean editing) { this.isEditing = editing; }
    public void setEditingUsername(String username) { this.editingUsername = username; }

    // ActionListener methods
    public void addAddListener(ActionListener listener) { btnAdd.addActionListener(listener); }
    public void addEditListener(ActionListener listener) { btnEdit.addActionListener(listener); }
    public void addDeleteListener(ActionListener listener) { btnDelete.addActionListener(listener); }
    public void addSaveListener(ActionListener listener) { btnSave.addActionListener(listener); }
    public void addCancelListener(ActionListener listener) { btnCancel.addActionListener(listener); }
    public void addSearchListener(ActionListener listener) { btnSearch.addActionListener(listener); }
    public void addRefreshListener(ActionListener listener) { btnRefresh.addActionListener(listener); }
    public void addLogoutListener(ActionListener listener) { btnLogout.addActionListener(listener); }

    /**
     * Load danh sách users vào table
     */
    public void loadUsers(List<User> users) {
        tableModel.setRowCount(0);
        for (User user : users) {
            Object[] row = {
                user.getUsername(),
                user.getFullName(),
                user.getEmail() != null ? user.getEmail() : "",
                user.getRole(),
                "●●●●●●●●" // Ẩn password
            };
            tableModel.addRow(row);
        }
        updateStatus("Đã tải " + users.size() + " người dùng");
    }

    // ===== METHODS FOR CONTROLLER =====

    /**
     * Add event listeners
     */
    public void addAddListener(ActionListener listener) {
        btnAdd.addActionListener(listener);
    }

    public void addEditListener(ActionListener listener) {
        btnEdit.addActionListener(listener);
    }

    public void addDeleteListener(ActionListener listener) {
        btnDelete.addActionListener(listener);
    }

    public void addSaveListener(ActionListener listener) {
        btnSave.addActionListener(listener);
    }

    public void addCancelListener(ActionListener listener) {
        btnCancel.addActionListener(listener);
    }

    public void addSearchListener(ActionListener listener) {
        btnSearch.addActionListener(listener);
    }

    public void addRefreshListener(ActionListener listener) {
        btnRefresh.addActionListener(listener);
    }

    public void addLogoutListener(ActionListener listener) {
        btnLogout.addActionListener(listener);
    }

    /**
     * Getters for form components
     */
    public JTextField getTxtUsername() { return txtUsername; }
    public JTextField getTxtFullName() { return txtFullName; }
    public JTextField getTxtEmail() { return txtEmail; }
    public JPasswordField getTxtPassword() { return txtPassword; }
    public JPasswordField getTxtConfirmPassword() { return txtConfirmPassword; }
    public JComboBox<String> getCmbRole() { return cmbRole; }
    public JTextField getTxtSearch() { return txtSearch; }
    public JTable getTableUsers() { return tableUsers; }
    public DefaultTableModel getTableModel() { return tableModel; }

    /**
     * Editing state management
     */
    public boolean isEditing() { return isEditing; }
    public void setEditing(boolean editing) { this.isEditing = editing; }
    public String getEditingUsername() { return editingUsername; }
    public void setEditingUsername(String username) { this.editingUsername = username; }

    /**
     * Show messages
     */
    public void showSuccess(String message) {
        JOptionPane.showMessageDialog(this, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }

    public void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    public void showWarning(String message) {
        JOptionPane.showMessageDialog(this, message, "Cảnh báo", JOptionPane.WARNING_MESSAGE);
    }

    /**
     * Update status
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }
}