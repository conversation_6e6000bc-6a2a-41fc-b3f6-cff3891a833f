package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.User;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Giao diện quản lý người dùng (Admin only)
 */
public class UserManagementView extends JFrame {
    
    // Components cho form nhập liệu
    private JTextField txtUsername, txtFullName;
    private JPasswordField txtPassword, txtConfirmPassword;
    private JComboBox<String> cmbRole;
    
    // Components cho bảng dữ liệu
    private JTable tableUsers;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;
    
    // Components cho tìm kiếm
    private JTextField txtSearch;
    private JButton btnSearch, btnRefresh;
    
    // Components cho thao tác
    private JButton btnAdd, btnEdit, btnDelete, btnSave, btnCancel;
    
    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingUsername = null;
    
    public UserManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupWindow();
        resetForm();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components
        txtUsername = new JTextField(20);
        txtFullName = new JTextField(25);
        txtPassword = new JPasswordField(20);
        txtConfirmPassword = new JPasswordField(20);
        
        // ComboBox cho role
        cmbRole = new JComboBox<>(new String[]{"USER", "ADMIN"});
        
        // Search components
        txtSearch = new JTextField(20);
        btnSearch = new JButton("Tìm kiếm");
        btnRefresh = new JButton("Làm mới");
        
        // Action buttons
        btnAdd = new JButton("Thêm người dùng");
        btnEdit = new JButton("Sửa");
        btnDelete = new JButton("Xóa");
        btnSave = new JButton("Lưu");
        btnCancel = new JButton("Hủy");
        
        // Style buttons
        styleButton(btnAdd, new Color(40, 167, 69));
        styleButton(btnEdit, new Color(255, 193, 7));
        styleButton(btnDelete, new Color(220, 53, 69));
        styleButton(btnSave, new Color(0, 123, 255));
        styleButton(btnCancel, new Color(108, 117, 125));
        styleButton(btnSearch, new Color(23, 162, 184));
        styleButton(btnRefresh, new Color(108, 117, 125));
        
        // Status
        lblStatus = new JLabel("Sẵn sàng");
        
        // Table
        String[] columnNames = {"Tên đăng nhập", "Họ tên", "Vai trò"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho edit trực tiếp trên table
            }
        };
        tableUsers = new JTable(tableModel);
        scrollPane = new JScrollPane(tableUsers);
    }
    
    /**
     * Style button với màu sắc
     */
    private void styleButton(JButton button, Color color) {
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setFont(new Font("Arial", Font.BOLD, 12));
        button.setPreferredSize(new Dimension(120, 35));
    }
    
    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Top panel - Form nhập liệu
        JPanel topPanel = createFormPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // Center panel - Table và tìm kiếm
        JPanel centerPanel = createTablePanel();
        add(centerPanel, BorderLayout.CENTER);
        
        // Bottom panel - Status
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Tạo panel form nhập liệu
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Thông tin người dùng"));
        
        // Form fields
        JPanel formPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Row 1
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(new JLabel("Tên đăng nhập:"), gbc);
        gbc.gridx = 1;
        formPanel.add(txtUsername, gbc);
        
        gbc.gridx = 2;
        formPanel.add(new JLabel("Họ tên:"), gbc);
        gbc.gridx = 3;
        formPanel.add(txtFullName, gbc);
        
        // Row 2
        gbc.gridx = 0; gbc.gridy = 1;
        formPanel.add(new JLabel("Mật khẩu:"), gbc);
        gbc.gridx = 1;
        formPanel.add(txtPassword, gbc);
        
        gbc.gridx = 2;
        formPanel.add(new JLabel("Xác nhận MK:"), gbc);
        gbc.gridx = 3;
        formPanel.add(txtConfirmPassword, gbc);
        
        // Row 3
        gbc.gridx = 0; gbc.gridy = 2;
        formPanel.add(new JLabel("Vai trò:"), gbc);
        gbc.gridx = 1;
        formPanel.add(cmbRole, gbc);
        
        panel.add(formPanel, BorderLayout.CENTER);
        
        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(btnAdd);
        buttonPanel.add(btnEdit);
        buttonPanel.add(btnDelete);
        buttonPanel.add(btnSave);
        buttonPanel.add(btnCancel);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * Tạo panel table và tìm kiếm
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Danh sách người dùng"));
        
        // Search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.add(new JLabel("Tìm kiếm:"));
        searchPanel.add(txtSearch);
        searchPanel.add(btnSearch);
        searchPanel.add(btnRefresh);
        
        panel.add(searchPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * Tạo panel bottom
     */
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createLoweredBevelBorder());
        panel.add(lblStatus, BorderLayout.WEST);
        return panel;
    }
    
    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableUsers.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        tableUsers.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedUserToForm();
            }
        });
        
        // Set column widths
        tableUsers.getColumnModel().getColumn(0).setPreferredWidth(150); // Username
        tableUsers.getColumnModel().getColumn(1).setPreferredWidth(250); // Full name
        tableUsers.getColumnModel().getColumn(2).setPreferredWidth(100); // Role
    }
    
    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản lý Người dùng");
        setSize(800, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
    }
    
    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        txtUsername.setText("");
        txtFullName.setText("");
        txtPassword.setText("");
        txtConfirmPassword.setText("");
        cmbRole.setSelectedIndex(0);
        
        txtUsername.setEnabled(true);
        isEditing = false;
        editingUsername = null;
        
        btnAdd.setEnabled(true);
        btnEdit.setEnabled(false);
        btnDelete.setEnabled(false);
        btnSave.setEnabled(false);
        btnCancel.setEnabled(false);
        
        tableUsers.clearSelection();
        updateStatus("Sẵn sàng");
    }
    
    /**
     * Load user được chọn vào form
     */
    private void loadSelectedUserToForm() {
        int selectedRow = tableUsers.getSelectedRow();
        if (selectedRow >= 0) {
            txtUsername.setText(tableModel.getValueAt(selectedRow, 0).toString());
            txtFullName.setText(tableModel.getValueAt(selectedRow, 1).toString());
            cmbRole.setSelectedItem(tableModel.getValueAt(selectedRow, 2));
            
            // Clear password fields khi load user
            txtPassword.setText("");
            txtConfirmPassword.setText("");
            
            btnEdit.setEnabled(true);
            btnDelete.setEnabled(true);
            btnAdd.setEnabled(false);
        } else {
            resetForm();
        }
    }
    
    /**
     * Cập nhật trạng thái
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }
    
    /**
     * Load danh sách users vào table
     */
    public void loadUserData(List<User> users) {
        tableModel.setRowCount(0);
        for (User user : users) {
            Object[] row = {
                user.getUsername(),
                user.getFullName(),
                user.getRole()
            };
            tableModel.addRow(row);
        }
    }
    
    /**
     * Lấy thông tin user từ form
     */
    public User getUserFromForm() {
        User user = new User();
        user.setUsername(txtUsername.getText().trim());
        user.setFullName(txtFullName.getText().trim());
        user.setRole(cmbRole.getSelectedItem().toString());
        
        String password = new String(txtPassword.getPassword());
        if (!password.isEmpty()) {
            user.setPassword(password);
        }
        
        return user;
    }
    
    /**
     * Validate form input
     */
    public String validateForm() {
        String username = txtUsername.getText().trim();
        String fullName = txtFullName.getText().trim();
        String password = new String(txtPassword.getPassword());
        String confirmPassword = new String(txtConfirmPassword.getPassword());
        
        if (username.isEmpty()) {
            return "Tên đăng nhập không được để trống!";
        }
        
        if (username.length() < 3) {
            return "Tên đăng nhập phải có ít nhất 3 ký tự!";
        }
        
        if (fullName.isEmpty()) {
            return "Họ tên không được để trống!";
        }
        
        // Chỉ validate password khi thêm mới hoặc khi có nhập password
        if (!isEditing || !password.isEmpty()) {
            if (password.isEmpty()) {
                return "Mật khẩu không được để trống!";
            }
            
            if (password.length() < 3) {
                return "Mật khẩu phải có ít nhất 3 ký tự!";
            }
            
            if (!password.equals(confirmPassword)) {
                return "Mật khẩu xác nhận không khớp!";
            }
        }
        
        return null; // No error
    }
    
    // Getters
    public String getSearchText() {
        return txtSearch.getText().trim();
    }
    
    public boolean isEditing() {
        return isEditing;
    }
    
    public String getEditingUsername() {
        return editingUsername;
    }
    
    /**
     * Bắt đầu chế độ editing
     */
    public void startEditing() {
        isEditing = true;
        editingUsername = txtUsername.getText();
        txtUsername.setEnabled(false);
        
        btnAdd.setEnabled(false);
        btnEdit.setEnabled(false);
        btnDelete.setEnabled(false);
        btnSave.setEnabled(true);
        btnCancel.setEnabled(true);
        
        updateStatus("Đang chỉnh sửa người dùng: " + editingUsername);
    }
    
    /**
     * Bắt đầu chế độ thêm mới
     */
    public void startAdding() {
        resetForm();
        isEditing = false;
        
        btnAdd.setEnabled(false);
        btnEdit.setEnabled(false);
        btnDelete.setEnabled(false);
        btnSave.setEnabled(true);
        btnCancel.setEnabled(true);
        
        txtUsername.requestFocus();
        updateStatus("Đang thêm người dùng mới");
    }
    
    // Event listener setters
    public void setAddActionListener(ActionListener listener) {
        btnAdd.addActionListener(listener);
    }
    
    public void setEditActionListener(ActionListener listener) {
        btnEdit.addActionListener(listener);
    }
    
    public void setDeleteActionListener(ActionListener listener) {
        btnDelete.addActionListener(listener);
    }
    
    public void setSaveActionListener(ActionListener listener) {
        btnSave.addActionListener(listener);
    }
    
    public void setCancelActionListener(ActionListener listener) {
        btnCancel.addActionListener(listener);
    }
    
    public void setSearchActionListener(ActionListener listener) {
        btnSearch.addActionListener(listener);
        txtSearch.addActionListener(listener);
    }
    
    public void setRefreshActionListener(ActionListener listener) {
        btnRefresh.addActionListener(listener);
    }
}
