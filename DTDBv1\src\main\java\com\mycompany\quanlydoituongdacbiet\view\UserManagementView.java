package com.mycompany.quanlydoituongdacbiet.view;

import com.mycompany.quanlydoituongdacbiet.model.User;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * <PERSON><PERSON>o diện quản lý người dùng - Thiết kế theo chuẩn ThiSinhManagementView
 */
public class UserManagementView extends JFrame {

    // Màu sắc chủ đạo
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240); // #0071F0
    private static final Color SECONDARY_COLOR = new Color(108, 117, 125); // #6c757d
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69); // #28a745
    private static final Color DANGER_COLOR = new Color(220, 53, 69); // #dc3545
    private static final Color LIGHT_COLOR = new Color(248, 249, 250); // #f8f9fa
    private static final Color WHITE_COLOR = Color.WHITE;

    // Components cho form nhập liệu
    private JTextField txtUsername, txtFullName, txtEmail;
    private JPasswordField txtPassword, txtConfirmPassword;
    private JComboBox<String> cmbRole;

    // Components cho bảng dữ liệu
    private JTable tableUsers;
    private DefaultTableModel tableModel;
    private JScrollPane scrollPane;

    // Components cho tìm kiếm
    private JTextField txtSearch;
    private JButton btnSearch, btnRefresh;

    // Components cho thao tác
    private JButton btnAdd, btnEdit, btnDelete, btnSave, btnCancel, btnLogout;

    // Status
    private JLabel lblStatus;
    private boolean isEditing = false;
    private String editingUsername = null;
    
    public UserManagementView() {
        initComponents();
        setupLayout();
        setupTable();
        setupWindow();
        resetForm();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Form input components - làm to hết lên
        txtUsername = new JTextField(30);
        txtUsername.setPreferredSize(new Dimension(280, 40));
        txtUsername.setFont(new Font("Arial", Font.PLAIN, 16));

        txtFullName = new JTextField(35);
        txtFullName.setPreferredSize(new Dimension(350, 40));
        txtFullName.setFont(new Font("Arial", Font.PLAIN, 16));

        txtPassword = new JPasswordField(30);
        txtPassword.setPreferredSize(new Dimension(280, 40));
        txtPassword.setFont(new Font("Arial", Font.PLAIN, 16));

        txtConfirmPassword = new JPasswordField(30);
        txtConfirmPassword.setPreferredSize(new Dimension(280, 40));
        txtConfirmPassword.setFont(new Font("Arial", Font.PLAIN, 16));

        // ComboBox cho role
        cmbRole = new JComboBox<>(new String[]{"USER", "ADMIN"});
        cmbRole.setPreferredSize(new Dimension(150, 40));
        cmbRole.setFont(new Font("Arial", Font.PLAIN, 16));

        // Search components
        txtSearch = new JTextField(35);
        txtSearch.setPreferredSize(new Dimension(350, 40));
        txtSearch.setFont(new Font("Arial", Font.PLAIN, 16));
        btnSearch = new JButton("Tìm kiếm");
        btnRefresh = new JButton("Làm mới");
        
        // Action buttons
        btnAdd = new JButton("Thêm người dùng");
        btnEdit = new JButton("Sửa");
        btnDelete = new JButton("Xóa");
        btnSave = new JButton("Lưu");
        btnCancel = new JButton("Hủy");
        
        // Style buttons - làm to hết lên
        styleButton(btnAdd, new Color(40, 167, 69));
        styleButton(btnEdit, new Color(255, 193, 7));
        styleButton(btnDelete, new Color(220, 53, 69));
        styleButton(btnSave, new Color(0, 123, 255));
        styleButton(btnCancel, new Color(108, 117, 125));
        styleButton(btnSearch, new Color(23, 162, 184));
        styleButton(btnRefresh, new Color(108, 117, 125));
        
        // Status
        lblStatus = new JLabel("Sẵn sàng");
        
        // Table
        String[] columnNames = {"Tên đăng nhập", "Họ tên", "Vai trò"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho edit trực tiếp trên table
            }
        };
        tableUsers = new JTable(tableModel);
        scrollPane = new JScrollPane(tableUsers);
    }
    
    /**
     * Style button với màu sắc - làm to hết lên
     */
    private void styleButton(JButton button, Color color) {
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setFont(new Font("Arial", Font.BOLD, 16));  // Font to hơn
        button.setPreferredSize(new Dimension(160, 45));   // Kích thước to hơn
    }
    
    /**
     * Thiết lập layout - sửa lại để đẹp hơn
     */
    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));  // Thêm gap

        // Tạo main panel với margin xung quanh
        JPanel mainPanel = new JPanel(new BorderLayout(15, 15));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Top panel - Form nhập liệu
        JPanel topPanel = createFormPanel();
        mainPanel.add(topPanel, BorderLayout.NORTH);

        // Center panel - Table và tìm kiếm
        JPanel centerPanel = createTablePanel();
        mainPanel.add(centerPanel, BorderLayout.CENTER);

        // Bottom panel - Status
        JPanel bottomPanel = createBottomPanel();
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);

        add(mainPanel, BorderLayout.CENTER);
    }
    
    /**
     * Tạo panel form nhập liệu - thiết kế lại đẹp hơn
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "Thông tin người dùng",
                0, 0, new Font("Arial", Font.BOLD, 18)),
            BorderFactory.createEmptyBorder(15, 15, 15, 15)));

        // Form fields - layout 2 cột đẹp hơn
        JPanel formPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(12, 15, 12, 15);
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // Cột 1 - Labels và inputs
        gbc.gridx = 0; gbc.gridy = 0; gbc.weightx = 0.0;
        JLabel lblUsername = new JLabel("Tên đăng nhập:");
        lblUsername.setFont(new Font("Arial", Font.BOLD, 16));
        lblUsername.setPreferredSize(new Dimension(150, 30));
        formPanel.add(lblUsername, gbc);

        gbc.gridx = 1; gbc.weightx = 0.3;
        formPanel.add(txtUsername, gbc);

        // Spacer
        gbc.gridx = 2; gbc.weightx = 0.1;
        formPanel.add(Box.createHorizontalStrut(30), gbc);

        gbc.gridx = 3; gbc.weightx = 0.0;
        JLabel lblFullName = new JLabel("Họ tên:");
        lblFullName.setFont(new Font("Arial", Font.BOLD, 16));
        lblFullName.setPreferredSize(new Dimension(100, 30));
        formPanel.add(lblFullName, gbc);

        gbc.gridx = 4; gbc.weightx = 0.6;
        formPanel.add(txtFullName, gbc);

        // Row 2
        gbc.gridx = 0; gbc.gridy = 1; gbc.weightx = 0.0;
        JLabel lblPassword = new JLabel("Mật khẩu:");
        lblPassword.setFont(new Font("Arial", Font.BOLD, 16));
        formPanel.add(lblPassword, gbc);

        gbc.gridx = 1; gbc.weightx = 0.3;
        formPanel.add(txtPassword, gbc);

        // Spacer
        gbc.gridx = 2; gbc.weightx = 0.1;
        formPanel.add(Box.createHorizontalStrut(30), gbc);

        gbc.gridx = 3; gbc.weightx = 0.0;
        JLabel lblConfirmPassword = new JLabel("Xác nhận MK:");
        lblConfirmPassword.setFont(new Font("Arial", Font.BOLD, 16));
        formPanel.add(lblConfirmPassword, gbc);

        gbc.gridx = 4; gbc.weightx = 0.6;
        formPanel.add(txtConfirmPassword, gbc);

        // Row 3
        gbc.gridx = 0; gbc.gridy = 2; gbc.weightx = 0.0;
        JLabel lblRole = new JLabel("Vai trò:");
        lblRole.setFont(new Font("Arial", Font.BOLD, 16));
        formPanel.add(lblRole, gbc);

        gbc.gridx = 1; gbc.weightx = 0.3;
        formPanel.add(cmbRole, gbc);

        panel.add(formPanel, BorderLayout.CENTER);

        // Button panel - thiết kế đẹp hơn
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 25, 20));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));

        buttonPanel.add(btnAdd);
        buttonPanel.add(btnEdit);
        buttonPanel.add(btnDelete);
        buttonPanel.add(btnSave);
        buttonPanel.add(btnCancel);

        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }
    
    /**
     * Tạo panel table và tìm kiếm - thiết kế đẹp hơn
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "Danh sách người dùng",
                0, 0, new Font("Arial", Font.BOLD, 18)),
            BorderFactory.createEmptyBorder(10, 15, 15, 15)));

        // Search panel - thiết kế đẹp hơn
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 15, 10));
        searchPanel.setBorder(BorderFactory.createEmptyBorder(5, 0, 10, 0));

        JLabel lblSearch = new JLabel("Tìm kiếm:");
        lblSearch.setFont(new Font("Arial", Font.BOLD, 16));
        searchPanel.add(lblSearch);
        searchPanel.add(Box.createHorizontalStrut(10));
        searchPanel.add(txtSearch);
        searchPanel.add(Box.createHorizontalStrut(15));
        searchPanel.add(btnSearch);
        searchPanel.add(Box.createHorizontalStrut(10));
        searchPanel.add(btnRefresh);

        panel.add(searchPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }
    
    /**
     * Tạo panel bottom
     */
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createLoweredBevelBorder());
        panel.setPreferredSize(new Dimension(0, 40));  // Status bar cao hơn

        lblStatus.setFont(new Font("Arial", Font.PLAIN, 16));  // Font to hơn
        lblStatus.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));  // Padding to hơn
        panel.add(lblStatus, BorderLayout.WEST);

        return panel;
    }
    
    /**
     * Thiết lập table
     */
    private void setupTable() {
        tableUsers.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        tableUsers.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedUserToForm();
            }
        });

        // Set column widths cho table rộng hơn
        tableUsers.getColumnModel().getColumn(0).setPreferredWidth(250); // Username
        tableUsers.getColumnModel().getColumn(1).setPreferredWidth(500); // Full name
        tableUsers.getColumnModel().getColumn(2).setPreferredWidth(200); // Role

        // Set row height cho dễ nhìn hơn - làm to hết lên
        tableUsers.setRowHeight(35);  // Row cao hơn

        // Set font cho table - to hơn
        tableUsers.setFont(new Font("Arial", Font.PLAIN, 16));

        // Set font cho table header - to hơn
        tableUsers.getTableHeader().setFont(new Font("Arial", Font.BOLD, 16));
        tableUsers.getTableHeader().setPreferredSize(new Dimension(0, 40));

        // Set preferred size cho scroll pane - to hơn và đẹp hơn
        scrollPane.setPreferredSize(new Dimension(1000, 400));
        scrollPane.setBorder(BorderFactory.createLoweredBevelBorder());
    }
    
    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Quản lý Người dùng");
        setSize(1200, 800);  // Tăng kích thước cửa sổ
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
    }
    
    /**
     * Reset form về trạng thái ban đầu
     */
    public void resetForm() {
        txtUsername.setText("");
        txtFullName.setText("");
        txtPassword.setText("");
        txtConfirmPassword.setText("");
        cmbRole.setSelectedIndex(0);
        
        txtUsername.setEnabled(true);
        isEditing = false;
        editingUsername = null;
        
        btnAdd.setEnabled(true);
        btnEdit.setEnabled(false);
        btnDelete.setEnabled(false);
        btnSave.setEnabled(false);
        btnCancel.setEnabled(false);
        
        tableUsers.clearSelection();
        updateStatus("Sẵn sàng");
    }
    
    /**
     * Load user được chọn vào form
     */
    private void loadSelectedUserToForm() {
        int selectedRow = tableUsers.getSelectedRow();
        if (selectedRow >= 0) {
            txtUsername.setText(tableModel.getValueAt(selectedRow, 0).toString());
            txtFullName.setText(tableModel.getValueAt(selectedRow, 1).toString());
            cmbRole.setSelectedItem(tableModel.getValueAt(selectedRow, 2));
            
            // Clear password fields khi load user
            txtPassword.setText("");
            txtConfirmPassword.setText("");
            
            btnEdit.setEnabled(true);
            btnDelete.setEnabled(true);
            btnAdd.setEnabled(false);
        } else {
            resetForm();
        }
    }
    
    /**
     * Cập nhật trạng thái
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
    }
    
    /**
     * Load danh sách users vào table
     */
    public void loadUserData(List<User> users) {
        tableModel.setRowCount(0);
        for (User user : users) {
            Object[] row = {
                user.getUsername(),
                user.getFullName(),
                user.getRole()
            };
            tableModel.addRow(row);
        }
    }
    
    /**
     * Lấy thông tin user từ form
     */
    public User getUserFromForm() {
        User user = new User();
        user.setUsername(txtUsername.getText().trim());
        user.setFullName(txtFullName.getText().trim());
        user.setRole(cmbRole.getSelectedItem().toString());
        
        String password = new String(txtPassword.getPassword());
        if (!password.isEmpty()) {
            user.setPassword(password);
        }
        
        return user;
    }
    
    /**
     * Validate form input
     */
    public String validateForm() {
        String username = txtUsername.getText().trim();
        String fullName = txtFullName.getText().trim();
        String password = new String(txtPassword.getPassword());
        String confirmPassword = new String(txtConfirmPassword.getPassword());
        
        if (username.isEmpty()) {
            return "Tên đăng nhập không được để trống!";
        }
        
        if (username.length() < 3) {
            return "Tên đăng nhập phải có ít nhất 3 ký tự!";
        }
        
        if (fullName.isEmpty()) {
            return "Họ tên không được để trống!";
        }
        
        // Chỉ validate password khi thêm mới hoặc khi có nhập password
        if (!isEditing || !password.isEmpty()) {
            if (password.isEmpty()) {
                return "Mật khẩu không được để trống!";
            }
            
            if (password.length() < 3) {
                return "Mật khẩu phải có ít nhất 3 ký tự!";
            }
            
            if (!password.equals(confirmPassword)) {
                return "Mật khẩu xác nhận không khớp!";
            }
        }
        
        return null; // No error
    }
    
    // Getters
    public String getSearchText() {
        return txtSearch.getText().trim();
    }
    
    public boolean isEditing() {
        return isEditing;
    }
    
    public String getEditingUsername() {
        return editingUsername;
    }
    
    /**
     * Bắt đầu chế độ editing
     */
    public void startEditing() {
        isEditing = true;
        editingUsername = txtUsername.getText();
        txtUsername.setEnabled(false);
        
        btnAdd.setEnabled(false);
        btnEdit.setEnabled(false);
        btnDelete.setEnabled(false);
        btnSave.setEnabled(true);
        btnCancel.setEnabled(true);
        
        updateStatus("Đang chỉnh sửa người dùng: " + editingUsername);
    }
    
    /**
     * Bắt đầu chế độ thêm mới
     */
    public void startAdding() {
        resetForm();
        isEditing = false;
        
        btnAdd.setEnabled(false);
        btnEdit.setEnabled(false);
        btnDelete.setEnabled(false);
        btnSave.setEnabled(true);
        btnCancel.setEnabled(true);
        
        txtUsername.requestFocus();
        updateStatus("Đang thêm người dùng mới");
    }
    
    // Event listener setters
    public void setAddActionListener(ActionListener listener) {
        btnAdd.addActionListener(listener);
    }
    
    public void setEditActionListener(ActionListener listener) {
        btnEdit.addActionListener(listener);
    }
    
    public void setDeleteActionListener(ActionListener listener) {
        btnDelete.addActionListener(listener);
    }
    
    public void setSaveActionListener(ActionListener listener) {
        btnSave.addActionListener(listener);
    }
    
    public void setCancelActionListener(ActionListener listener) {
        btnCancel.addActionListener(listener);
    }
    
    public void setSearchActionListener(ActionListener listener) {
        btnSearch.addActionListener(listener);
        txtSearch.addActionListener(listener);
    }
    
    public void setRefreshActionListener(ActionListener listener) {
        btnRefresh.addActionListener(listener);
    }
}
