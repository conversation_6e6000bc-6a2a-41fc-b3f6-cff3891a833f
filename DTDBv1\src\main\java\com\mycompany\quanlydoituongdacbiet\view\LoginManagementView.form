<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="com.mycompany.quanlydoituongdacbiet.view.LoginManagementView">
  <grid id="27dc6" binding="mainPanel" layout-manager="BorderLayout" hgap="0" vgap="0">
    <constraints>
      <xy x="20" y="20" width="800" height="600"/>
    </constraints>
    <properties>
      <background color="-1"/>
      <preferredSize width="800" height="600"/>
    </properties>
    <border type="none"/>
    <children>
      <grid id="a8b7c" binding="loginPanel" layout-manager="GridBagLayout">
        <constraints border-constraint="Center"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="none"/>
        <children>
          <component id="d4e5f" class="javax.swing.JLabel" binding="titleLabel">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="2" vsize-policy="0" hsize-policy="0" anchor="0" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="24" style="1"/>
              <foreground color="-16744192"/>
              <horizontalAlignment value="0"/>
              <text value="HỆ THỐNG QUẢN LÝ ĐIỂM THI ĐẠI HỌC"/>
            </properties>
          </component>
          <component id="g6h7i" class="javax.swing.JLabel">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Tên đăng nhập:"/>
            </properties>
          </component>
          <component id="j8k9l" class="javax.swing.JTextField" binding="txtUsername">
            <constraints>
              <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="200" height="35"/>
              </gridbag>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="m0n1o" class="javax.swing.JLabel">
            <constraints>
              <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Mật khẩu:"/>
            </properties>
          </component>
          <component id="p2q3r" class="javax.swing.JPasswordField" binding="txtPassword">
            <constraints>
              <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="200" height="35"/>
              </gridbag>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <grid id="s4t5u" binding="buttonPanel" layout-manager="FlowLayout" hgap="10" vgap="5" flow-align="1">
            <constraints>
              <grid row="3" column="0" row-span="1" col-span="2" vsize-policy="3" hsize-policy="3" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <background color="-1"/>
            </properties>
            <border type="none"/>
            <children>
              <component id="v6w7x" class="javax.swing.JButton" binding="btnLogin">
                <constraints/>
                <properties>
                  <background color="-16744192"/>
                  <font name="Arial" size="14" style="1"/>
                  <foreground color="-1"/>
                  <preferredSize width="120" height="40"/>
                  <text value="Đăng nhập"/>
                </properties>
              </component>
              <component id="y8z9a" class="javax.swing.JButton" binding="btnRegister">
                <constraints/>
                <properties>
                  <background color="-13408513"/>
                  <font name="Arial" size="14" style="1"/>
                  <foreground color="-1"/>
                  <preferredSize width="120" height="40"/>
                  <text value="Đăng ký"/>
                </properties>
              </component>
              <component id="b0c1d" class="javax.swing.JButton" binding="btnExit">
                <constraints/>
                <properties>
                  <background color="-3407617"/>
                  <font name="Arial" size="14" style="1"/>
                  <foreground color="-1"/>
                  <preferredSize width="120" height="40"/>
                  <text value="Thoát"/>
                </properties>
              </component>
            </children>
          </grid>
        </children>
      </grid>
      <grid id="e2f3g" binding="menuPanel" layout-manager="BorderLayout" hgap="0" vgap="0">
        <constraints border-constraint="Center"/>
        <properties>
          <background color="-1"/>
          <visible value="false"/>
        </properties>
        <border type="none"/>
        <children>
          <component id="h4i5j" class="javax.swing.JLabel" binding="welcomeLabel">
            <constraints border-constraint="North"/>
            <properties>
              <font name="Arial" size="20" style="1"/>
              <foreground color="-16744192"/>
              <horizontalAlignment value="0"/>
              <text value="CHÀO MỪNG ĐẾN HỆ THỐNG QUẢN LÝ"/>
            </properties>
          </component>
          <grid id="k6l7m" binding="menuButtonPanel" layout-manager="GridLayout" hgap="15" vgap="15">
            <constraints border-constraint="Center"/>
            <properties>
              <background color="-1"/>
            </properties>
            <border type="none"/>
            <children/>
          </grid>
        </children>
      </grid>
      <component id="n8o9p" class="javax.swing.JLabel" binding="lblStatus">
        <constraints border-constraint="South"/>
        <properties>
          <font name="Arial" size="12" style="2"/>
          <foreground color="-16744192"/>
          <text value="Sẵn sàng"/>
        </properties>
      </component>
    </children>
  </grid>
</form>
