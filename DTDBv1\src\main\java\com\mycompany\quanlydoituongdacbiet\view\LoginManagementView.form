<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="title" type="java.lang.String" value="Hệ Thống Quản Lý"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="mainPanel" alignment="0" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="mainPanel" alignment="0" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="mainPanel">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="ff" red="ff" type="rgb"/>
        </Property>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[1000, 700]"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Component id="titleLabel" alignment="0" max="32767" attributes="0"/>
              <Component id="menuPanel" alignment="0" max="32767" attributes="0"/>
              <Component id="userInfoPanel" alignment="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <Component id="titleLabel" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="userInfoPanel" min="-2" max="-2" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="menuPanel" min="-2" max="-2" attributes="0"/>
                  <EmptySpace pref="400" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="titleLabel">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Arial" size="24" style="1"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="f0" green="71" red="0" type="rgb"/>
            </Property>
            <Property name="horizontalAlignment" type="int" value="0"/>
            <Property name="text" type="java.lang.String" value="HỆ THỐNG QUẢN LÝ ĐỐI TƯỢNG ĐẶC BIỆT"/>
          </Properties>
        </Component>
        <Container class="javax.swing.JPanel" name="userInfoPanel">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
          </Properties>

          <Layout>
            <DimensionLayout dim="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                      <Component id="lblWelcome" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="32767" attributes="0"/>
                      <Component id="btnLogout" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
            <DimensionLayout dim="1">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="10" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="lblWelcome" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="btnLogout" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace min="-2" pref="10" max="-2" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
          </Layout>
          <SubComponents>
            <Component class="javax.swing.JLabel" name="lblWelcome">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="14" style="0"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Chào mừng: Admin"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JButton" name="btnLogout">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="0" green="80" red="80" type="rgb"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="14" style="1"/>
                </Property>
                <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Đăng xuất"/>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[120, 35]"/>
                </Property>
              </Properties>
            </Component>
          </SubComponents>
        </Container>
        <Container class="javax.swing.JPanel" name="menuPanel">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
          </Properties>

          <Layout>
            <DimensionLayout dim="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="100" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="btnThiSinh" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="btnMonThi" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="btnKhoiThi" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="btnDiemThi" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="btnTraCuu" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="btnUserManagement" alignment="0" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace min="-2" pref="100" max="-2" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
            <DimensionLayout dim="1">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                      <Component id="btnThiSinh" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="15" max="-2" attributes="0"/>
                      <Component id="btnMonThi" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="15" max="-2" attributes="0"/>
                      <Component id="btnKhoiThi" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="15" max="-2" attributes="0"/>
                      <Component id="btnDiemThi" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="15" max="-2" attributes="0"/>
                      <Component id="btnTraCuu" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="15" max="-2" attributes="0"/>
                      <Component id="btnUserManagement" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
          </Layout>
          <SubComponents>
            <Component class="javax.swing.JButton" name="btnThiSinh">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="f0" green="71" red="0" type="rgb"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="1"/>
                </Property>
                <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Quản lý thí sinh"/>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[250, 50]"/>
                </Property>
              </Properties>
            </Component>
            <Component class="javax.swing.JButton" name="btnMonThi">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="f0" green="71" red="0" type="rgb"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="1"/>
                </Property>
                <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Quản lý môn thi"/>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[250, 50]"/>
                </Property>
              </Properties>
            </Component>
            <Component class="javax.swing.JButton" name="btnKhoiThi">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="f0" green="71" red="0" type="rgb"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="1"/>
                </Property>
                <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Quản lý khối thi"/>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[250, 50]"/>
                </Property>
              </Properties>
            </Component>
            <Component class="javax.swing.JButton" name="btnDiemThi">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="f0" green="71" red="0" type="rgb"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="1"/>
                </Property>
                <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Quản lý điểm thi"/>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[250, 50]"/>
                </Property>
              </Properties>
            </Component>
            <Component class="javax.swing.JButton" name="btnTraCuu">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="f0" green="71" red="0" type="rgb"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="1"/>
                </Property>
                <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Tra cứu điểm thi"/>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[250, 50]"/>
                </Property>
              </Properties>
            </Component>
            <Component class="javax.swing.JButton" name="btnUserManagement">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="f0" green="71" red="0" type="rgb"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Arial" size="16" style="1"/>
                </Property>
                <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
                <Property name="text" type="java.lang.String" value="Quản lý người dùng"/>
                <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
                  <Dimension value="[250, 50]"/>
                </Property>
              </Properties>
            </Component>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
