package com.mycompany.quanlydoituongdacbiet.dao;

import com.mycompany.quanlydoituongdacbiet.model.User;
import com.mycompany.quanlydoituongdacbiet.utils.FileManager;

import java.util.ArrayList;
import java.util.List;

/**
 * DAO class để quản lý dữ liệu User
 */
public class UserDAO {
    private static final String USERS_FILE = "data/users.txt";
    
    /**
     * Lấy tất cả users từ file
     */
    public List<User> getAllUsers() {
        List<User> users = new ArrayList<>();
        List<String> lines = FileManager.readAllLines(USERS_FILE);
        
        for (String line : lines) {
            User user = User.fromFileString(line);
            if (user != null) {
                users.add(user);
            }
        }
        
        return users;
    }
    
    /**
     * Tìm user theo username
     */
    public User findByUsername(String username) {
        List<User> users = getAllUsers();
        for (User user : users) {
            if (user.getUsername().equals(username)) {
                return user;
            }
        }
        return null;
    }
    
    /**
     * <PERSON>ác thực user với username và password
     */
    public User authenticate(String username, String password) {
        User user = findByUsername(username);
        if (user != null && user.getPassword().equals(password)) {
            return user;
        }
        return null;
    }
    
    /**
     * Thêm user mới
     */
    public boolean addUser(User user) {
        // Kiểm tra username đã tồn tại chưa
        if (findByUsername(user.getUsername()) != null) {
            return false; // Username đã tồn tại
        }
        
        FileManager.appendLine(USERS_FILE, user.toFileString());
        return true;
    }
    
    /**
     * Cập nhật thông tin user
     */
    public boolean updateUser(User user) {
        User existingUser = findByUsername(user.getUsername());
        if (existingUser == null) {
            return false; // User không tồn tại
        }
        
        FileManager.updateLine(USERS_FILE, user.getUsername(), 0, user.toFileString());
        return true;
    }
    
    /**
     * Xóa user
     */
    public boolean deleteUser(String username) {
        User existingUser = findByUsername(username);
        if (existingUser == null) {
            return false; // User không tồn tại
        }
        
        FileManager.deleteLine(USERS_FILE, username, 0);
        return true;
    }
    
    /**
     * Đổi mật khẩu
     */
    public boolean changePassword(String username, String oldPassword, String newPassword) {
        User user = authenticate(username, oldPassword);
        if (user == null) {
            return false; // Mật khẩu cũ không đúng
        }
        
        user.setPassword(newPassword);
        return updateUser(user);
    }
    
    /**
     * Kiểm tra xem có user nào trong hệ thống chưa
     */
    public boolean hasUsers() {
        return !getAllUsers().isEmpty();
    }
    
    /**
     * Tạo admin mặc định nếu chưa có user nào
     */
    public void createDefaultAdmin() {
        if (!hasUsers()) {
            User defaultAdmin = new User("admin", "admin123", "Administrator", "ADMIN");
            addUser(defaultAdmin);
        }
    }
}
