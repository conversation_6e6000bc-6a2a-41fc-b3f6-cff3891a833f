package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import com.mycompany.quanlydoituongdacbiet.service.MonThiService;
import com.mycompany.quanlydoituongdacbiet.view.MonThiManagementView;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import javax.swing.SwingUtilities;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;

/**
 * Controller cho quản lý môn thi
 */
public class MonThiController {
    private MonThiManagementView view;
    private MonThiService service;
    
    public MonThiController(MonThiManagementView view) {
        this.view = view;
        this.service = MonThiService.getInstance();
        
        initEventListeners();
        loadData();
    }
    
    /**
     * Khởi tạo event listeners
     */
    private void initEventListeners() {
        // Button listeners
        view.addThemListener(new ThemActionListener());
        view.addSuaListener(new SuaActionListener());
        view.addXoaListener(new XoaActionListener());
        view.addLuuListener(new LuuActionListener());
        view.addHuyListener(new HuyActionListener());
        view.addTimKiemListener(new TimKiemActionListener());
        view.addLamMoiListener(new LamMoiActionListener());
        
        // Search field listener
        view.getTxtTimKiem().getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) {
                performSearch();
            }
            
            @Override
            public void removeUpdate(DocumentEvent e) {
                performSearch();
            }
            
            @Override
            public void changedUpdate(DocumentEvent e) {
                performSearch();
            }
        });
    }
    
    /**
     * Load dữ liệu ban đầu
     */
    private void loadData() {
        try {
            List<MonThi> monThis = service.getAllMonThi();
            view.updateTable(monThis);
            view.updateStatus("Đã tải " + monThis.size() + " môn thi");
        } catch (Exception e) {
            view.showError("Lỗi khi tải dữ liệu: " + e.getMessage());
        }
    }
    
    /**
     * Thực hiện tìm kiếm
     */
    private void performSearch() {
        SwingUtilities.invokeLater(() -> {
            try {
                String keyword = view.getTxtTimKiem().getText().trim();
                List<MonThi> result;
                
                if (keyword.isEmpty()) {
                    result = service.getAllMonThi();
                } else {
                    result = service.searchMonThiByName(keyword);
                }
                
                view.updateTable(result);
                view.updateStatus("Tìm thấy " + result.size() + " môn thi");
            } catch (Exception e) {
                view.showError("Lỗi khi tìm kiếm: " + e.getMessage());
            }
        });
    }
    
    /**
     * Action listener cho nút Thêm
     */
    private class ThemActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.setAddMode();
        }
    }
    
    /**
     * Action listener cho nút Sửa
     */
    private class SuaActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (view.getTableMonThi().getSelectedRow() >= 0) {
                view.setEditMode();
            } else {
                view.showError("Vui lòng chọn môn thi cần sửa");
            }
        }
    }
    
    /**
     * Action listener cho nút Xóa
     */
    private class XoaActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int selectedRow = view.getTableMonThi().getSelectedRow();
            if (selectedRow >= 0) {
                String maMon = (String) view.getTableModel().getValueAt(selectedRow, 0);
                
                if (view.confirmDelete(maMon)) {
                    try {
                        if (service.deleteMonThi(maMon)) {
                            view.showSuccess("Xóa môn thi thành công");
                            loadData();
                            view.resetForm();
                        } else {
                            view.showError("Không thể xóa môn thi");
                        }
                    } catch (Exception ex) {
                        view.showError("Lỗi khi xóa: " + ex.getMessage());
                    }
                }
            } else {
                view.showError("Vui lòng chọn môn thi cần xóa");
            }
        }
    }
    
    /**
     * Action listener cho nút Lưu
     */
    private class LuuActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                MonThi monThi = view.getFormData();
                
                // Validate dữ liệu
                String validationError = service.validateMonThi(monThi);
                if (validationError != null) {
                    view.showError(validationError);
                    return;
                }
                
                boolean success;
                if (view.isEditing()) {
                    // Cập nhật
                    success = service.updateMonThi(monThi);
                    if (success) {
                        view.showSuccess("Cập nhật môn thi thành công");
                    } else {
                        view.showError("Không thể cập nhật môn thi");
                    }
                } else {
                    // Thêm mới
                    success = service.addMonThi(monThi);
                    if (success) {
                        view.showSuccess("Thêm môn thi thành công");
                    } else {
                        view.showError("Không thể thêm môn thi. Mã môn có thể đã tồn tại");
                    }
                }
                
                if (success) {
                    loadData();
                    view.resetForm();
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi khi lưu: " + ex.getMessage());
            }
        }
    }
    
    /**
     * Action listener cho nút Hủy
     */
    private class HuyActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.resetForm();
        }
    }
    
    /**
     * Action listener cho nút Tìm kiếm
     */
    private class TimKiemActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            performSearch();
        }
    }
    
    /**
     * Action listener cho nút Làm mới
     */
    private class LamMoiActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.getTxtTimKiem().setText("");
            loadData();
            view.resetForm();
        }
    }
    
    /**
     * Hiển thị view
     */
    public void showView() {
        view.setVisible(true);
    }
}
