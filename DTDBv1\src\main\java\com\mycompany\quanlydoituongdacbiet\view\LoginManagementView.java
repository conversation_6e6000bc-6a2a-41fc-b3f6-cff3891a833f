package com.mycompany.quanlydoituongdacbiet.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;

/**
 * <PERSON><PERSON><PERSON> diện đăng nhập - <PERSON><PERSON><PERSON><PERSON> kế theo chuẩn ThiSinhManagementView
 */
public class LoginManagementView extends J<PERSON>rame {
    
    // <PERSON><PERSON>u sắc chủ đạo
    private static final Color PRIMARY_COLOR = new Color(0, 113, 240); // #0071F0
    private static final Color SECONDARY_COLOR = new Color(108, 117, 125); // #6c757d
    private static final Color SUCCESS_COLOR = new Color(40, 167, 69); // #28a745
    private static final Color DANGER_COLOR = new Color(220, 53, 69); // #dc3545
    private static final Color LIGHT_COLOR = new Color(248, 249, 250); // #f8f9fa
    private static final Color WHITE_COLOR = Color.WHITE;
    
    // Components
    private JTextField txtUsername;
    private JPasswordField txtPassword;
    private JButton btnLogin, btnRegister, btnAbout, btnExit;
    private JButton btnThiSinhManagement, btnMonThiManagement, btnKhoiThiManagement, btnDiemThiManagement, btnLogout;
    private JLabel lblStatus;
    private JPanel mainPanel, loginPanel;
    
    public LoginManagementView() {
        initComponents();
        setupLayout();
        setupWindow();
        setupEvents();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Input components
        txtUsername = new JTextField(20);
        txtUsername.setFont(new Font("Arial", Font.PLAIN, 16));
        txtUsername.setPreferredSize(new Dimension(300, 40));
        
        txtPassword = new JPasswordField(20);
        txtPassword.setFont(new Font("Arial", Font.PLAIN, 16));
        txtPassword.setPreferredSize(new Dimension(300, 40));
        
        // Buttons với styling
        btnLogin = new JButton("Đăng nhập");
        btnRegister = new JButton("Đăng ký");
        btnAbout = new JButton("Thông tin");
        btnExit = new JButton("Thoát");

        styleButton(btnLogin, PRIMARY_COLOR);
        styleButton(btnRegister, SUCCESS_COLOR);
        styleButton(btnAbout, SECONDARY_COLOR);
        styleButton(btnExit, DANGER_COLOR);
        
        // Status
        lblStatus = new JLabel(" ");
        lblStatus.setFont(new Font("Arial", Font.PLAIN, 14));
        lblStatus.setForeground(DANGER_COLOR);
    }
    
    /**
     * Styling cho buttons
     */
    private void styleButton(JButton button, Color bgColor) {
        button.setBackground(bgColor);
        button.setForeground(WHITE_COLOR);
        button.setFont(new Font("Arial", Font.BOLD, 16));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(140, 45));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // Hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(bgColor.darker());
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(bgColor);
            }
        });
    }
    
    /**
     * Thiết lập layout theo chuẩn ThiSinhManagementView
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        getContentPane().setBackground(LIGHT_COLOR);
        
        // Header panel
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);
        
        // Main panel - Login form
        mainPanel = createMainPanel();
        add(mainPanel, BorderLayout.CENTER);
        
        // Status panel
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Tạo header panel
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(PRIMARY_COLOR);
        panel.setPreferredSize(new Dimension(0, 100));
        
        JLabel titleLabel = new JLabel("HỆ THỐNG QUẢN LÝ ĐIỂM THI ĐẠI HỌC");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(WHITE_COLOR);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        panel.add(titleLabel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * Tạo main panel với login form
     */
    private JPanel createMainPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBackground(LIGHT_COLOR);
        
        // Login form panel
        loginPanel = createLoginFormPanel();
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.CENTER;
        
        panel.add(loginPanel, gbc);
        
        return panel;
    }
    
    /**
     * Tạo login form panel
     */
    private JPanel createLoginFormPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(WHITE_COLOR);
        panel.setPreferredSize(new Dimension(600, 400));
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(PRIMARY_COLOR, 3),
            BorderFactory.createEmptyBorder(40, 50, 40, 50)
        ));
        
        // Title
        JLabel titleLabel = new JLabel("ĐĂNG NHẬP HỆ THỐNG");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 20));
        titleLabel.setForeground(PRIMARY_COLOR);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 30, 0));
        
        panel.add(titleLabel, BorderLayout.NORTH);
        
        // Form fields
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBackground(WHITE_COLOR);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(15, 15, 15, 15);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Username row
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(createLabel("Tên đăng nhập:"), gbc);
        gbc.gridx = 1;
        formPanel.add(txtUsername, gbc);
        
        // Password row
        gbc.gridx = 0; gbc.gridy = 1;
        formPanel.add(createLabel("Mật khẩu:"), gbc);
        gbc.gridx = 1;
        formPanel.add(txtPassword, gbc);
        
        panel.add(formPanel, BorderLayout.CENTER);
        
        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 20));
        buttonPanel.setBackground(WHITE_COLOR);
        buttonPanel.add(btnLogin);
        buttonPanel.add(btnRegister);
        buttonPanel.add(btnAbout);
        buttonPanel.add(btnExit);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * Tạo label với style
     */
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(new Font("Arial", Font.BOLD, 16));
        label.setForeground(new Color(33, 37, 41));
        label.setPreferredSize(new Dimension(150, 30));
        label.setHorizontalAlignment(SwingConstants.RIGHT);
        return label;
    }
    
    /**
     * Tạo status panel
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(LIGHT_COLOR);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLoweredBevelBorder(),
            BorderFactory.createEmptyBorder(8, 15, 8, 15)
        ));
        panel.setPreferredSize(new Dimension(0, 40));
        
        panel.add(lblStatus, BorderLayout.WEST);
        
        return panel;
    }
    
    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Đăng nhập - Hệ thống Quản lý Điểm thi Đại học");
        setSize(900, 700);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setResizable(false);
        
        // Icon
        try {
            setIconImage(Toolkit.getDefaultToolkit().getImage("icon.png"));
        } catch (Exception e) {
            // Ignore if icon not found
        }
    }
    
    /**
     * Thiết lập events
     */
    private void setupEvents() {
        // Enter key cho login
        txtPassword.addActionListener(e -> btnLogin.doClick());
        
        // Focus management
        txtUsername.addActionListener(e -> txtPassword.requestFocus());
    }
    
    /**
     * Hiển thị main menu và ẩn login form
     */
    public void showMainMenu() {
        loginPanel.setVisible(false);
        
        // Tạo main menu panel
        JPanel menuPanel = createMainMenuPanel();
        
        mainPanel.removeAll();
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.CENTER;
        mainPanel.add(menuPanel, gbc);
        
        mainPanel.revalidate();
        mainPanel.repaint();
        
        updateStatus("Đăng nhập thành công!");
    }
    
    /**
     * Tạo main menu panel
     */
    private JPanel createMainMenuPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(WHITE_COLOR);
        panel.setPreferredSize(new Dimension(600, 400));
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(PRIMARY_COLOR, 3),
            BorderFactory.createEmptyBorder(40, 50, 40, 50)
        ));
        
        // Welcome message
        JLabel welcomeLabel = new JLabel("CHÀO MỪNG ĐẾN VỚI HỆ THỐNG");
        welcomeLabel.setFont(new Font("Arial", Font.BOLD, 20));
        welcomeLabel.setForeground(PRIMARY_COLOR);
        welcomeLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        panel.add(welcomeLabel, BorderLayout.NORTH);
        
        // Menu buttons - Sử dụng GridLayout 3x2 để có 5 buttons + logout
        JPanel menuButtonPanel = new JPanel(new GridLayout(3, 2, 15, 15));
        menuButtonPanel.setBackground(WHITE_COLOR);
        menuButtonPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        btnThiSinhManagement = new JButton("Quản lý Thí sinh");
        btnMonThiManagement = new JButton("Quản lý Môn thi");
        btnKhoiThiManagement = new JButton("Quản lý Khối thi");
        btnDiemThiManagement = new JButton("Quản lý Điểm thi");
        btnLogout = new JButton("Đăng xuất");
        JButton btnEmpty = new JButton(""); // Empty button for layout

        styleMenuButton(btnThiSinhManagement, SUCCESS_COLOR);
        styleMenuButton(btnMonThiManagement, PRIMARY_COLOR);
        styleMenuButton(btnKhoiThiManagement, new Color(255, 193, 7)); // Warning color
        styleMenuButton(btnDiemThiManagement, new Color(23, 162, 184)); // Info color
        styleMenuButton(btnLogout, DANGER_COLOR);

        // Make empty button invisible
        btnEmpty.setVisible(false);

        menuButtonPanel.add(btnThiSinhManagement);
        menuButtonPanel.add(btnMonThiManagement);
        menuButtonPanel.add(btnKhoiThiManagement);
        menuButtonPanel.add(btnDiemThiManagement);
        menuButtonPanel.add(btnLogout);
        menuButtonPanel.add(btnEmpty);
        
        panel.add(menuButtonPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * Style cho menu buttons
     */
    private void styleMenuButton(JButton button, Color bgColor) {
        button.setBackground(bgColor);
        button.setForeground(WHITE_COLOR);
        button.setFont(new Font("Arial", Font.BOLD, 18));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(400, 60));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // Hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(bgColor.darker());
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(bgColor);
            }
        });
    }

    /**
     * Hiển thị lại login form
     */
    public void showLoginForm() {
        mainPanel.removeAll();

        // Đảm bảo loginPanel hiển thị
        loginPanel.setVisible(true);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.CENTER;

        mainPanel.add(loginPanel, gbc);
        mainPanel.revalidate();
        mainPanel.repaint();

        // Clear form
        txtUsername.setText("");
        txtPassword.setText("");
        txtUsername.requestFocus();
        updateStatus("Đã đăng xuất");
    }

    /**
     * Cập nhật status
     */
    public void updateStatus(String message) {
        lblStatus.setText(message);
        lblStatus.setForeground(PRIMARY_COLOR);
    }

    /**
     * Hiển thị lỗi
     */
    public void showError(String message) {
        lblStatus.setText("Lỗi: " + message);
        lblStatus.setForeground(DANGER_COLOR);
    }

    /**
     * Hiển thị thành công
     */
    public void showSuccess(String message) {
        lblStatus.setText(message);
        lblStatus.setForeground(SUCCESS_COLOR);
    }

    /**
     * Clear form
     */
    public void clearForm() {
        txtUsername.setText("");
        txtPassword.setText("");
        lblStatus.setText(" ");
        txtUsername.requestFocus();
    }

    // Getter methods
    public JTextField getTxtUsername() { return txtUsername; }
    public JPasswordField getTxtPassword() { return txtPassword; }
    public JLabel getLblStatus() { return lblStatus; }

    // ActionListener methods
    public void addLoginListener(ActionListener listener) { btnLogin.addActionListener(listener); }
    public void addRegisterListener(ActionListener listener) { btnRegister.addActionListener(listener); }
    public void addAboutListener(ActionListener listener) { btnAbout.addActionListener(listener); }
    public void addExitListener(ActionListener listener) { btnExit.addActionListener(listener); }

    // Management menu ActionListener methods
    public void addThiSinhManagementListener(ActionListener listener) {
        if (btnThiSinhManagement != null) btnThiSinhManagement.addActionListener(listener);
    }
    public void addMonThiManagementListener(ActionListener listener) {
        if (btnMonThiManagement != null) btnMonThiManagement.addActionListener(listener);
    }
    public void addKhoiThiManagementListener(ActionListener listener) {
        if (btnKhoiThiManagement != null) btnKhoiThiManagement.addActionListener(listener);
    }
    public void addDiemThiManagementListener(ActionListener listener) {
        if (btnDiemThiManagement != null) btnDiemThiManagement.addActionListener(listener);
    }
    public void addLogoutListener(ActionListener listener) {
        if (btnLogout != null) btnLogout.addActionListener(listener);
    }

    /**
     * Lấy username
     */
    public String getUsername() {
        return txtUsername.getText().trim();
    }

    /**
     * Lấy password
     */
    public String getPassword() {
        return new String(txtPassword.getPassword());
    }

    /**
     * Validate input
     */
    public boolean validateInput() {
        if (getUsername().isEmpty()) {
            showError("Vui lòng nhập tên đăng nhập");
            txtUsername.requestFocus();
            return false;
        }

        if (getPassword().isEmpty()) {
            showError("Vui lòng nhập mật khẩu");
            txtPassword.requestFocus();
            return false;
        }

        return true;
    }
}
