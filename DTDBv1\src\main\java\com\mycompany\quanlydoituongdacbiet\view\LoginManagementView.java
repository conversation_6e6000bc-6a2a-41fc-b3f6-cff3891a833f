package com.mycompany.quanlydoituongdacbiet.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;

/**
 * Giao diện đăng nhập với Swing GUI
 */
public class LoginManagementView extends JFrame {
    
    // Components cho đăng nhập
    private JTextField txtUsername;
    private JPasswordField txtPassword;
    private JButton btnLogin, btnExit, btnAbout;
    private JLabel lblStatus;
    
    // Components cho màn hình chính (sau khi đăng nhập)
    private JPanel mainPanel;
    private JLabel lblWelcome;
    private JButton btnThiSinh, btnMonThi, btnKhoiThi, btnDiemThi;
    private JButton btnUserManagement, btnChangePassword, btnLogout;
    
    private boolean isLoggedIn = false;
    private String currentUserRole = "";
    
    public LoginManagementView() {
        initComponents();
        setupLayout();
        setupWindow();
        showLoginPanel();
    }
    
    /**
     * Khởi tạo các components
     */
    private void initComponents() {
        // Login components - tăng kích thước
        txtUsername = new JTextField(25);
        txtUsername.setPreferredSize(new Dimension(250, 35));
        txtUsername.setFont(new Font("Arial", Font.PLAIN, 14));

        txtPassword = new JPasswordField(25);
        txtPassword.setPreferredSize(new Dimension(250, 35));
        txtPassword.setFont(new Font("Arial", Font.PLAIN, 14));

        btnLogin = new JButton("Đăng nhập");
        btnExit = new JButton("Thoát");
        btnAbout = new JButton("Thông tin");
        lblStatus = new JLabel(" ");
        
        // Main panel components
        lblWelcome = new JLabel("Chào mừng bạn đến với hệ thống!");
        btnThiSinh = new JButton("Quản lý Thí sinh");
        btnMonThi = new JButton("Quản lý Môn thi");
        btnKhoiThi = new JButton("Quản lý Khối thi");
        btnDiemThi = new JButton("Quản lý Điểm thi");
        btnUserManagement = new JButton("Quản lý Người dùng");
        btnChangePassword = new JButton("Đổi mật khẩu");
        btnLogout = new JButton("Đăng xuất");
        
        // Style buttons
        styleButton(btnLogin, new Color(0, 123, 255));
        styleButton(btnExit, new Color(220, 53, 69));
        styleButton(btnAbout, new Color(108, 117, 125));
        
        styleButton(btnThiSinh, new Color(40, 167, 69));
        styleButton(btnMonThi, new Color(40, 167, 69));
        styleButton(btnKhoiThi, new Color(40, 167, 69));
        styleButton(btnDiemThi, new Color(40, 167, 69));
        styleButton(btnUserManagement, new Color(255, 193, 7));
        styleButton(btnChangePassword, new Color(23, 162, 184));
        styleButton(btnLogout, new Color(220, 53, 69));
    }
    
    /**
     * Style button với màu sắc
     */
    private void styleButton(JButton button, Color color) {
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setFont(new Font("Arial", Font.BOLD, 12));
        button.setPreferredSize(new Dimension(150, 35));
    }
    
    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new CardLayout());
        
        // Tạo login panel
        JPanel loginPanel = createLoginPanel();
        add(loginPanel, "LOGIN");
        
        // Tạo main panel
        mainPanel = createMainPanel();
        add(mainPanel, "MAIN");
    }
    
    /**
     * Tạo panel đăng nhập
     */
    private JPanel createLoginPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(new Color(248, 249, 250));
        
        // Header
        JPanel headerPanel = new JPanel();
        headerPanel.setBackground(new Color(0, 123, 255));
        headerPanel.setPreferredSize(new Dimension(0, 80));
        
        JLabel titleLabel = new JLabel("HỆ THỐNG QUẢN LÝ ĐỐI TƯỢNG ĐẶC BIỆT");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        headerPanel.add(titleLabel);
        
        // Center - Login form
        JPanel centerPanel = new JPanel(new GridBagLayout());
        centerPanel.setBackground(new Color(248, 249, 250));
        
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedBevelBorder(),
            BorderFactory.createEmptyBorder(40, 40, 40, 40)
        ));
        formPanel.setBackground(Color.WHITE);
        formPanel.setPreferredSize(new Dimension(450, 300));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(15, 15, 15, 15);
        
        // Title
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        JLabel loginTitle = new JLabel("ĐĂNG NHẬP HỆ THỐNG");
        loginTitle.setFont(new Font("Arial", Font.BOLD, 16));
        loginTitle.setHorizontalAlignment(SwingConstants.CENTER);
        formPanel.add(loginTitle, gbc);
        
        // Username
        gbc.gridwidth = 1; gbc.gridy = 1; gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel lblUsername = new JLabel("Tên đăng nhập:");
        lblUsername.setFont(new Font("Arial", Font.BOLD, 14));
        formPanel.add(lblUsername, gbc);
        gbc.gridx = 1; gbc.anchor = GridBagConstraints.WEST;
        formPanel.add(txtUsername, gbc);

        // Password
        gbc.gridy = 2; gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel lblPassword = new JLabel("Mật khẩu:");
        lblPassword.setFont(new Font("Arial", Font.BOLD, 14));
        formPanel.add(lblPassword, gbc);
        gbc.gridx = 1; gbc.anchor = GridBagConstraints.WEST;
        formPanel.add(txtPassword, gbc);
        
        // Buttons
        gbc.gridy = 3; gbc.gridx = 0; gbc.gridwidth = 2;
        gbc.anchor = GridBagConstraints.CENTER;
        gbc.insets = new Insets(25, 15, 15, 15);
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 0));
        buttonPanel.setBackground(Color.WHITE);

        // Tăng kích thước buttons
        btnLogin.setPreferredSize(new Dimension(120, 40));
        btnAbout.setPreferredSize(new Dimension(120, 40));
        btnExit.setPreferredSize(new Dimension(120, 40));

        buttonPanel.add(btnLogin);
        buttonPanel.add(btnAbout);
        buttonPanel.add(btnExit);
        formPanel.add(buttonPanel, gbc);
        
        centerPanel.add(formPanel);
        
        // Status panel
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.setBackground(new Color(248, 249, 250));
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        lblStatus.setForeground(Color.RED);
        statusPanel.add(lblStatus);
        
        panel.add(headerPanel, BorderLayout.NORTH);
        panel.add(centerPanel, BorderLayout.CENTER);
        panel.add(statusPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * Tạo panel chính (sau khi đăng nhập)
     */
    private JPanel createMainPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(new Color(248, 249, 250));
        
        // Header
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(40, 167, 69));
        headerPanel.setPreferredSize(new Dimension(0, 60));
        
        lblWelcome.setFont(new Font("Arial", Font.BOLD, 16));
        lblWelcome.setForeground(Color.WHITE);
        lblWelcome.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        headerPanel.add(lblWelcome, BorderLayout.WEST);
        
        // Menu buttons
        JPanel menuPanel = new JPanel(new GridBagLayout());
        menuPanel.setBackground(new Color(248, 249, 250));
        menuPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        
        // Row 1
        gbc.gridx = 0; gbc.gridy = 0;
        menuPanel.add(btnThiSinh, gbc);
        gbc.gridx = 1;
        menuPanel.add(btnMonThi, gbc);
        
        // Row 2
        gbc.gridx = 0; gbc.gridy = 1;
        menuPanel.add(btnKhoiThi, gbc);
        gbc.gridx = 1;
        menuPanel.add(btnDiemThi, gbc);
        
        // Row 3 - Admin only
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        menuPanel.add(btnUserManagement, gbc);
        
        // Row 4
        gbc.gridwidth = 1; gbc.gridy = 3;
        gbc.gridx = 0;
        menuPanel.add(btnChangePassword, gbc);
        gbc.gridx = 1;
        menuPanel.add(btnLogout, gbc);
        
        panel.add(headerPanel, BorderLayout.NORTH);
        panel.add(menuPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * Thiết lập window
     */
    private void setupWindow() {
        setTitle("Hệ thống Quản lý Đối tượng Đặc biệt");
        setSize(600, 500);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setResizable(false);
    }
    
    /**
     * Hiển thị panel đăng nhập
     */
    public void showLoginPanel() {
        CardLayout cl = (CardLayout) getContentPane().getLayout();
        cl.show(getContentPane(), "LOGIN");
        isLoggedIn = false;
        txtUsername.setText("");
        txtPassword.setText("");
        lblStatus.setText(" ");
        txtUsername.requestFocus();
    }
    
    /**
     * Hiển thị panel chính
     */
    public void showMainPanel(String username, String fullName, String role) {
        CardLayout cl = (CardLayout) getContentPane().getLayout();
        cl.show(getContentPane(), "MAIN");
        isLoggedIn = true;
        currentUserRole = role;
        
        lblWelcome.setText("Chào mừng " + fullName + " (" + role + ")");
        
        // Ẩn/hiện button quản lý user dựa trên role
        btnUserManagement.setVisible("ADMIN".equals(role));
        
        setSize(600, 500);
        setLocationRelativeTo(null);
    }
    
    // Getters
    public String getUsername() {
        return txtUsername.getText().trim();
    }
    
    public String getPassword() {
        return new String(txtPassword.getPassword());
    }
    
    public boolean isLoggedIn() {
        return isLoggedIn;
    }
    
    public String getCurrentUserRole() {
        return currentUserRole;
    }
    
    // Status methods
    public void setStatus(String message, boolean isError) {
        lblStatus.setText(message);
        lblStatus.setForeground(isError ? Color.RED : new Color(40, 167, 69));
    }
    
    public void clearStatus() {
        lblStatus.setText(" ");
    }
    
    // Event listener setters
    public void setLoginActionListener(ActionListener listener) {
        btnLogin.addActionListener(listener);
        // Enter key support
        txtPassword.addActionListener(listener);
    }
    
    public void setExitActionListener(ActionListener listener) {
        btnExit.addActionListener(listener);
    }
    
    public void setAboutActionListener(ActionListener listener) {
        btnAbout.addActionListener(listener);
    }
    
    public void setLogoutActionListener(ActionListener listener) {
        btnLogout.addActionListener(listener);
    }
    
    public void setThiSinhActionListener(ActionListener listener) {
        btnThiSinh.addActionListener(listener);
    }
    
    public void setMonThiActionListener(ActionListener listener) {
        btnMonThi.addActionListener(listener);
    }
    
    public void setKhoiThiActionListener(ActionListener listener) {
        btnKhoiThi.addActionListener(listener);
    }
    
    public void setDiemThiActionListener(ActionListener listener) {
        btnDiemThi.addActionListener(listener);
    }
    
    public void setUserManagementActionListener(ActionListener listener) {
        btnUserManagement.addActionListener(listener);
    }
    
    public void setChangePasswordActionListener(ActionListener listener) {
        btnChangePassword.addActionListener(listener);
    }
}
