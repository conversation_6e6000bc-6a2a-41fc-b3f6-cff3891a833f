package com.mycompany.quanlydoituongdacbiet.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;

/**
 * Giao diện đăng nhập với Swing GUI
 */
public class LoginManagementView extends JFrame {
    
    // Components cho đăng nhập
    private JTextField txtUsername;
    private JPasswordField txtPassword;
    private JButton btnLogin, btnExit, btnAbout;
    private JLabel lblStatus;
    
    // Components cho màn hình chính (sau khi đăng nhập)
    private JPanel mainPanel;
    private JLabel lblWelcome;
    private JButton btnThiSinh, btnMonThi, btnKhoiThi, btnDiemThi;
    private JButton btnUserManagement, btnChangePassword, btnLogout;
    
    private boolean isLoggedIn = false;
    private String currentUserRole = "";
    
    public LoginManagementView() {
        initComponents();
        setupLayout();
        setupWindow();
        showLoginPanel();
    }
    
    /**
     * Khởi tạo các components - làm to đẹp hết lên
     */
    private void initComponents() {
        // Login components - làm to hết lên
        txtUsername = new JTextField(30);
        txtUsername.setPreferredSize(new Dimension(350, 45));
        txtUsername.setFont(new Font("Arial", Font.PLAIN, 16));

        txtPassword = new JPasswordField(30);
        txtPassword.setPreferredSize(new Dimension(350, 45));
        txtPassword.setFont(new Font("Arial", Font.PLAIN, 16));

        btnLogin = new JButton("Đăng nhập");
        btnExit = new JButton("Thoát");
        btnAbout = new JButton("Thông tin");
        lblStatus = new JLabel(" ");
        
        // Main panel components - làm to hết lên
        lblWelcome = new JLabel("Chào mừng bạn đến với hệ thống!");
        lblWelcome.setFont(new Font("Arial", Font.BOLD, 20));
        lblWelcome.setHorizontalAlignment(SwingConstants.CENTER);

        btnThiSinh = new JButton("Quản lý Thí sinh");
        btnMonThi = new JButton("Quản lý Môn thi");
        btnKhoiThi = new JButton("Quản lý Khối thi");
        btnDiemThi = new JButton("Quản lý Điểm thi");
        btnUserManagement = new JButton("Quản lý Người dùng");
        btnChangePassword = new JButton("Đổi mật khẩu");
        btnLogout = new JButton("Đăng xuất");
        
        // Style buttons
        styleButton(btnLogin, new Color(0, 123, 255));
        styleButton(btnExit, new Color(220, 53, 69));
        styleButton(btnAbout, new Color(108, 117, 125));
        
        styleButton(btnThiSinh, new Color(40, 167, 69));
        styleButton(btnMonThi, new Color(40, 167, 69));
        styleButton(btnKhoiThi, new Color(40, 167, 69));
        styleButton(btnDiemThi, new Color(40, 167, 69));
        styleButton(btnUserManagement, new Color(255, 193, 7));
        styleButton(btnChangePassword, new Color(23, 162, 184));
        styleButton(btnLogout, new Color(220, 53, 69));
    }
    
    /**
     * Style button với màu sắc - làm to hết lên
     */
    private void styleButton(JButton button, Color color) {
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setFont(new Font("Arial", Font.BOLD, 16));  // Font to hơn
        button.setPreferredSize(new Dimension(200, 50));   // Kích thước to hơn
    }
    
    /**
     * Thiết lập layout
     */
    private void setupLayout() {
        setLayout(new CardLayout());
        
        // Tạo login panel
        JPanel loginPanel = createLoginPanel();
        add(loginPanel, "LOGIN");
        
        // Tạo main panel
        mainPanel = createMainPanel();
        add(mainPanel, "MAIN");
    }
    
    /**
     * Tạo panel đăng nhập - thiết kế đẹp hơn
     */
    private JPanel createLoginPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // Header - làm to hết lên
        JPanel headerPanel = new JPanel();
        headerPanel.setBackground(new Color(0, 123, 255));
        headerPanel.setPreferredSize(new Dimension(0, 100));  // Cao hơn

        JLabel titleLabel = new JLabel("HỆ THỐNG QUẢN LÝ ĐIỂM THI ĐẠI HỌC");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));  // Font to hơn
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        headerPanel.add(titleLabel);
        
        // Center - Login form với layout đẹp hơn
        JPanel centerPanel = new JPanel(new GridBagLayout());
        centerPanel.setBackground(new Color(248, 249, 250));
        centerPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createCompoundBorder(
                BorderFactory.createRaisedBevelBorder(),
                BorderFactory.createLineBorder(new Color(0, 123, 255), 2)
            ),
            BorderFactory.createEmptyBorder(60, 60, 60, 60)  // Padding to hơn
        ));
        formPanel.setBackground(Color.WHITE);
        formPanel.setPreferredSize(new Dimension(650, 450));  // Kích thước to hơn

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(20, 20, 20, 20);  // Spacing to hơn

        // Title
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        JLabel loginTitle = new JLabel("ĐĂNG NHẬP HỆ THỐNG");
        loginTitle.setFont(new Font("Arial", Font.BOLD, 20));  // Font to hơn
        loginTitle.setHorizontalAlignment(SwingConstants.CENTER);
        formPanel.add(loginTitle, gbc);
        
        // Username - làm to hết lên
        gbc.gridwidth = 1; gbc.gridy = 1; gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel lblUsername = new JLabel("Tên đăng nhập:");
        lblUsername.setFont(new Font("Arial", Font.BOLD, 18));  // Font to hơn
        formPanel.add(lblUsername, gbc);
        gbc.gridx = 1; gbc.anchor = GridBagConstraints.WEST;
        formPanel.add(txtUsername, gbc);

        // Password - làm to hết lên
        gbc.gridy = 2; gbc.gridx = 0;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel lblPassword = new JLabel("Mật khẩu:");
        lblPassword.setFont(new Font("Arial", Font.BOLD, 18));  // Font to hơn
        formPanel.add(lblPassword, gbc);
        gbc.gridx = 1; gbc.anchor = GridBagConstraints.WEST;
        formPanel.add(txtPassword, gbc);
        
        // Buttons - làm to hết lên
        gbc.gridy = 3; gbc.gridx = 0; gbc.gridwidth = 2;
        gbc.anchor = GridBagConstraints.CENTER;
        gbc.insets = new Insets(30, 20, 20, 20);  // Spacing to hơn
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));  // Gap to hơn
        buttonPanel.setBackground(Color.WHITE);

        // Tăng kích thước buttons to hơn nữa
        btnLogin.setPreferredSize(new Dimension(150, 50));
        btnAbout.setPreferredSize(new Dimension(150, 50));
        btnExit.setPreferredSize(new Dimension(150, 50));

        buttonPanel.add(btnLogin);
        buttonPanel.add(btnAbout);
        buttonPanel.add(btnExit);
        formPanel.add(buttonPanel, gbc);
        
        centerPanel.add(formPanel);
        
        // Status panel - làm to hết lên
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.setBackground(new Color(248, 249, 250));
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        statusPanel.setPreferredSize(new Dimension(0, 40));  // Cao hơn
        lblStatus.setForeground(Color.RED);
        lblStatus.setFont(new Font("Arial", Font.PLAIN, 16));  // Font to hơn
        statusPanel.add(lblStatus);
        
        panel.add(headerPanel, BorderLayout.NORTH);
        panel.add(centerPanel, BorderLayout.CENTER);
        panel.add(statusPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * Tạo panel chính (sau khi đăng nhập) - thiết kế đẹp hơn
     */
    private JPanel createMainPanel() {
        JPanel panel = new JPanel(new BorderLayout(15, 15));
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // Header - làm to hết lên
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(40, 167, 69));
        headerPanel.setPreferredSize(new Dimension(0, 80));  // Cao hơn

        lblWelcome.setFont(new Font("Arial", Font.BOLD, 20));  // Font to hơn
        lblWelcome.setForeground(Color.WHITE);
        lblWelcome.setBorder(BorderFactory.createEmptyBorder(25, 30, 25, 30));  // Padding to hơn
        headerPanel.add(lblWelcome, BorderLayout.WEST);
        
        // Menu buttons - thiết kế đẹp và căn giữa
        JPanel menuContainer = new JPanel(new GridBagLayout());
        menuContainer.setBackground(new Color(248, 249, 250));

        JPanel menuPanel = new JPanel(new GridBagLayout());
        menuPanel.setBackground(Color.WHITE);
        menuPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createCompoundBorder(
                BorderFactory.createRaisedBevelBorder(),
                BorderFactory.createLineBorder(new Color(40, 167, 69), 2)
            ),
            BorderFactory.createEmptyBorder(40, 40, 40, 40)
        ));
        menuPanel.setPreferredSize(new Dimension(700, 500));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(15, 15, 15, 15);  // Spacing đều
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;

        // Title cho menu
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        JLabel menuTitle = new JLabel("MENU CHỨC NĂNG");
        menuTitle.setFont(new Font("Arial", Font.BOLD, 20));
        menuTitle.setHorizontalAlignment(SwingConstants.CENTER);
        menuTitle.setForeground(new Color(40, 167, 69));
        gbc.insets = new Insets(10, 15, 25, 15);
        menuPanel.add(menuTitle, gbc);

        // Reset insets và gridwidth
        gbc.insets = new Insets(15, 15, 15, 15);
        gbc.gridwidth = 1;

        // Row 1
        gbc.gridx = 0; gbc.gridy = 1;
        menuPanel.add(btnThiSinh, gbc);
        gbc.gridx = 1;
        menuPanel.add(btnMonThi, gbc);
        
        // Row 2
        gbc.gridx = 0; gbc.gridy = 2;
        menuPanel.add(btnKhoiThi, gbc);
        gbc.gridx = 1;
        menuPanel.add(btnDiemThi, gbc);

        // Row 3 - Admin only
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 2;
        menuPanel.add(btnUserManagement, gbc);

        // Row 4
        gbc.gridwidth = 1; gbc.gridy = 4;
        gbc.gridx = 0;
        menuPanel.add(btnChangePassword, gbc);
        gbc.gridx = 1;
        menuPanel.add(btnLogout, gbc);
        
        // Căn giữa menu panel
        menuContainer.add(menuPanel);

        panel.add(headerPanel, BorderLayout.NORTH);
        panel.add(menuContainer, BorderLayout.CENTER);

        return panel;
    }
    
    /**
     * Thiết lập window - làm to hết lên
     */
    private void setupWindow() {
        setTitle("Hệ thống Quản lý Đối tượng Đặc biệt");
        setSize(900, 700);  // Kích thước to hơn nhiều
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setResizable(false);
    }
    
    /**
     * Hiển thị panel đăng nhập
     */
    public void showLoginPanel() {
        CardLayout cl = (CardLayout) getContentPane().getLayout();
        cl.show(getContentPane(), "LOGIN");
        isLoggedIn = false;
        txtUsername.setText("");
        txtPassword.setText("");
        lblStatus.setText(" ");
        txtUsername.requestFocus();
    }
    
    /**
     * Hiển thị panel chính
     */
    public void showMainPanel(String username, String fullName, String role) {
        CardLayout cl = (CardLayout) getContentPane().getLayout();
        cl.show(getContentPane(), "MAIN");
        isLoggedIn = true;
        currentUserRole = role;
        
        lblWelcome.setText("Chào mừng " + fullName + " (" + role + ")");
        
        // Ẩn/hiện button quản lý user dựa trên role
        btnUserManagement.setVisible("ADMIN".equals(role));
        
        setSize(600, 500);
        setLocationRelativeTo(null);
    }
    
    // Getters
    public String getUsername() {
        return txtUsername.getText().trim();
    }
    
    public String getPassword() {
        return new String(txtPassword.getPassword());
    }
    
    public boolean isLoggedIn() {
        return isLoggedIn;
    }
    
    public String getCurrentUserRole() {
        return currentUserRole;
    }
    
    // Status methods
    public void setStatus(String message, boolean isError) {
        lblStatus.setText(message);
        lblStatus.setForeground(isError ? Color.RED : new Color(40, 167, 69));
    }
    
    public void clearStatus() {
        lblStatus.setText(" ");
    }
    
    // Event listener setters
    public void setLoginActionListener(ActionListener listener) {
        btnLogin.addActionListener(listener);
        // Enter key support
        txtPassword.addActionListener(listener);
    }
    
    public void setExitActionListener(ActionListener listener) {
        btnExit.addActionListener(listener);
    }
    
    public void setAboutActionListener(ActionListener listener) {
        btnAbout.addActionListener(listener);
    }
    
    public void setLogoutActionListener(ActionListener listener) {
        btnLogout.addActionListener(listener);
    }
    
    public void setThiSinhActionListener(ActionListener listener) {
        btnThiSinh.addActionListener(listener);
    }
    
    public void setMonThiActionListener(ActionListener listener) {
        btnMonThi.addActionListener(listener);
    }
    
    public void setKhoiThiActionListener(ActionListener listener) {
        btnKhoiThi.addActionListener(listener);
    }
    
    public void setDiemThiActionListener(ActionListener listener) {
        btnDiemThi.addActionListener(listener);
    }
    
    public void setUserManagementActionListener(ActionListener listener) {
        btnUserManagement.addActionListener(listener);
    }
    
    public void setChangePasswordActionListener(ActionListener listener) {
        btnChangePassword.addActionListener(listener);
    }
}
