# 🧪 HƯỚNG DẪN KIỂM THỬ HỆ THỐNG

## 🚀 Khởi động ứng dụng

### 1. Compile
```bash
cd DTDBv1/src/main/java
javac -cp . com/mycompany/quanlydoituongdacbiet/QuanLyDoiTuong/MainApplication.java
```

### 2. Run
```bash
java -cp . com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong.MainApplication
```

## 🔐 Test đăng nhập

### ✅ Test Case 1: Đăng nhập thành công
1. **Input**: 
   - Username: `admin`
   - Password: `admin`
2. **Expected**: 
   - Hiển thị main menu với các nút chức năng
   - Status: "Đăng nhập thành công"

### ❌ Test Case 2: Đăng nhập thất bại
1. **Input**: 
   - Username: `wrong`
   - Password: `wrong`
2. **Expected**: 
   - Hiển thị lỗi "Tên đăng nhập hoặc mật khẩu không đúng"
   - Vẫn ở màn hình login

### ❌ Test Case 3: Input rỗng
1. **Input**: 
   - Username: (để trống)
   - Password: (để trống)
2. **Expected**: 
   - Hiển thị lỗi "Vui lòng nhập tên đăng nhập"

## 👥 Test quản lý người dùng (Chỉ ADMIN)

### ✅ Test Case 4: Mở User Management
1. **Từ main menu** → Click "Quản lý Người dùng"
2. **Expected**: 
   - Mở cửa sổ User Management
   - Hiển thị danh sách user hiện tại
   - Form thêm user ở phía trên

### ✅ Test Case 5: Thêm user mới
1. **Input**:
   - Username: `testuser`
   - Họ tên: `Test User`
   - Email: `<EMAIL>`
   - Password: `1234`
   - Confirm Password: `1234`
   - Role: `USER`
2. **Action**: Click "Thêm" → "Lưu"
3. **Expected**: 
   - User mới xuất hiện trong table
   - Form được clear
   - Status: "Thêm người dùng thành công"

### ✅ Test Case 6: Sửa user
1. **Action**: 
   - Chọn user trong table
   - Click "Sửa"
   - Thay đổi họ tên thành "Updated Name"
   - Click "Lưu"
2. **Expected**: 
   - Thông tin user được cập nhật trong table
   - Status: "Cập nhật người dùng thành công"

### ✅ Test Case 7: Xóa user
1. **Action**: 
   - Chọn user (không phải admin)
   - Click "Xóa"
   - Xác nhận xóa
2. **Expected**: 
   - User bị xóa khỏi table
   - Status: "Xóa người dùng thành công"

### ❌ Test Case 8: Xóa admin cuối cùng
1. **Action**: 
   - Chọn admin user
   - Click "Xóa"
2. **Expected**: 
   - Hiển thị lỗi "Không thể xóa admin cuối cùng"

### ✅ Test Case 9: Tìm kiếm user
1. **Input**: 
   - Nhập "admin" vào ô tìm kiếm
   - Click "Tìm kiếm"
2. **Expected**: 
   - Chỉ hiển thị user có chứa "admin"

### ✅ Test Case 10: Làm mới danh sách
1. **Action**: Click "Làm mới"
2. **Expected**: 
   - Hiển thị lại tất cả user
   - Form được clear

## 🔄 Test navigation

### ✅ Test Case 11: Đăng xuất từ User Management
1. **Action**: Click "Đăng xuất" trong User Management
2. **Expected**: 
   - Quay về màn hình login
   - Form login được clear

### ✅ Test Case 12: Đăng xuất từ Main Menu
1. **Action**: Click "Đăng xuất" trong Main Menu
2. **Expected**: 
   - Quay về màn hình login
   - Status: "Đã đăng xuất"

## 🎓 Test quản lý thí sinh

### ✅ Test Case 13: Mở ThiSinh Management
1. **Action**: Từ main menu → Click "Quản lý Thí sinh"
2. **Expected**: 
   - Mở cửa sổ ThiSinhManagementView
   - Hiển thị giao diện quản lý thí sinh

## 📊 Test validation

### ❌ Test Case 14: Username trùng lặp
1. **Action**: 
   - Thêm user với username đã tồn tại
2. **Expected**: 
   - Hiển thị lỗi "Username đã tồn tại"

### ❌ Test Case 15: Password không khớp
1. **Action**: 
   - Password: `1234`
   - Confirm Password: `5678`
2. **Expected**: 
   - Hiển thị lỗi "Mật khẩu xác nhận không khớp"

### ❌ Test Case 16: Email không hợp lệ
1. **Action**: 
   - Email: `invalid-email`
2. **Expected**: 
   - Hiển thị lỗi "Email không hợp lệ"

### ❌ Test Case 17: Trường bắt buộc để trống
1. **Action**: 
   - Để trống Username hoặc Họ tên
2. **Expected**: 
   - Hiển thị lỗi tương ứng

## 💾 Test data persistence

### ✅ Test Case 18: Khởi động lại ứng dụng
1. **Action**: 
   - Thêm user mới
   - Thoát ứng dụng
   - Khởi động lại
2. **Expected**: 
   - User mới vẫn tồn tại
   - Có thể đăng nhập với user mới

## 🎨 Test UI/UX

### ✅ Test Case 19: Responsive design
1. **Action**: Thay đổi kích thước cửa sổ
2. **Expected**: 
   - Layout tự động điều chỉnh
   - Không bị lỗi hiển thị

### ✅ Test Case 20: Color scheme
1. **Check**: 
   - Màu chủ đạo #0071F0 được áp dụng
   - Buttons có hover effect
   - Status messages có màu phù hợp

## 🔒 Test phân quyền

### ✅ Test Case 21: User role restrictions
1. **Action**: 
   - Đăng nhập với user role USER
   - Kiểm tra main menu
2. **Expected**: 
   - Không có nút "Quản lý Người dùng"
   - Chỉ có "Quản lý Thí sinh"

## 📝 Checklist tổng thể

- [ ] Ứng dụng khởi động thành công
- [ ] Đăng nhập admin/admin hoạt động
- [ ] Main menu hiển thị đúng
- [ ] User Management mở được
- [ ] Thêm user mới thành công
- [ ] Sửa user thành công
- [ ] Xóa user thành công
- [ ] Tìm kiếm hoạt động
- [ ] Validation hoạt động đúng
- [ ] Đăng xuất hoạt động
- [ ] Data được lưu persistent
- [ ] UI đẹp và responsive
- [ ] Phân quyền hoạt động đúng

## 🐛 Báo cáo lỗi

Nếu phát hiện lỗi, ghi chú:
1. **Test case nào bị lỗi**
2. **Input đã sử dụng**
3. **Kết quả thực tế**
4. **Kết quả mong đợi**
5. **Error message (nếu có)**

---

**Lưu ý**: Đảm bảo thư mục `data/` được tạo tự động và file `users.txt` chứa dữ liệu đúng format.
