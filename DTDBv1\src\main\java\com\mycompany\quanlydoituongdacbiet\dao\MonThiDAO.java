package com.mycompany.quanlydoituongdacbiet.dao;

import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import com.mycompany.quanlydoituongdacbiet.utils.FileManager;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO để quản lý dữ liệu môn thi
 */
public class MonThiDAO {
    
    /**
     * Lấy tất cả môn thi
     */
    public List<MonThi> getAllMonThi() {
        List<MonThi> monThis = new ArrayList<>();
        List<String> lines = FileManager.readAllLines(FileManager.MONTHIS_FILE);
        
        for (String line : lines) {
            MonThi monThi = MonThi.fromString(line);
            if (monThi != null) {
                monThis.add(monThi);
            }
        }
        
        return monThis;
    }
    
    /**
     * Thêm môn thi mới
     */
    public boolean addMonThi(MonThi monThi) {
        try {
            // Ki<PERSON>m tra trùng mã môn
            if (getMonThiByMaMon(monThi.getMaMon()) != null) {
                return false; // Đã tồn tại
            }
            
            FileManager.appendLine(FileManager.MONTHIS_FILE, monThi.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Cập nhật thông tin môn thi
     */
    public boolean updateMonThi(MonThi monThi) {
        try {
            FileManager.updateLine(FileManager.MONTHIS_FILE, 
                                 monThi.getMaMon(), 0, monThi.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Xóa môn thi
     */
    public boolean deleteMonThi(String maMon) {
        try {
            FileManager.deleteLine(FileManager.MONTHIS_FILE, maMon, 0);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Tìm môn thi theo mã môn
     */
    public MonThi getMonThiByMaMon(String maMon) {
        List<String> lines = FileManager.searchLines(FileManager.MONTHIS_FILE, maMon, 0);
        if (!lines.isEmpty()) {
            return MonThi.fromString(lines.get(0));
        }
        return null;
    }
    
    /**
     * Tìm kiếm môn thi theo tên
     */
    public List<MonThi> searchMonThiByName(String tenMon) {
        List<MonThi> result = new ArrayList<>();
        List<String> lines = FileManager.searchLines(FileManager.MONTHIS_FILE, tenMon, 1);
        
        for (String line : lines) {
            MonThi monThi = MonThi.fromString(line);
            if (monThi != null) {
                result.add(monThi);
            }
        }
        
        return result;
    }

    public boolean exists(String maMon) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }
}