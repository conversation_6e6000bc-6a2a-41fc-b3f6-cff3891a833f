package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.model.DiemThi;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import com.mycompany.quanlydoituongdacbiet.service.DiemThiService;
import com.mycompany.quanlydoituongdacbiet.service.ThiSinhService;
import com.mycompany.quanlydoituongdacbiet.service.MonThiService;
import com.mycompany.quanlydoituongdacbiet.view.DiemThiManagementView;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import javax.swing.SwingUtilities;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;

/**
 * Controller cho quản lý điểm thi
 */
public class DiemThiController {
    private DiemThiManagementView view;
    private DiemThiService service;
    private ThiSinhService thiSinhService;
    private MonThiService monThiService;
    
    public DiemThiController(DiemThiManagementView view) {
        this.view = view;
        this.service = DiemThiService.getInstance();
        this.thiSinhService = ThiSinhService.getInstance();
        this.monThiService = MonThiService.getInstance();
        
        initEventListeners();
        loadData();
        loadComboBoxData();
    }
    
    /**
     * Khởi tạo event listeners
     */
    private void initEventListeners() {
        // Button listeners
        view.addThemListener(new ThemActionListener());
        view.addSuaListener(new SuaActionListener());
        view.addXoaListener(new XoaActionListener());
        view.addLuuListener(new LuuActionListener());
        view.addHuyListener(new HuyActionListener());
        view.addTimKiemListener(new TimKiemActionListener());
        view.addLamMoiListener(new LamMoiActionListener());
        
        // Search field listener
        view.getTxtTimKiem().getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) {
                performSearch();
            }
            
            @Override
            public void removeUpdate(DocumentEvent e) {
                performSearch();
            }
            
            @Override
            public void changedUpdate(DocumentEvent e) {
                performSearch();
            }
        });
    }
    
    /**
     * Load dữ liệu ban đầu
     */
    private void loadData() {
        try {
            List<DiemThi> diemThis = service.getAllDiemThi();
            List<ThiSinh> thiSinhs = thiSinhService.getAllThiSinh();
            List<MonThi> monThis = monThiService.getAllMonThi();
            
            view.updateTable(diemThis, thiSinhs, monThis);
            view.updateStatus("Đã tải " + diemThis.size() + " bản ghi điểm thi");
        } catch (Exception e) {
            view.showError("Lỗi khi tải dữ liệu: " + e.getMessage());
        }
    }
    
    /**
     * Load dữ liệu cho combo boxes
     */
    private void loadComboBoxData() {
        try {
            List<ThiSinh> thiSinhs = thiSinhService.getAllThiSinh();
            List<MonThi> monThis = monThiService.getAllMonThi();
            
            view.updateThiSinhList(thiSinhs);
            view.updateMonThiList(monThis);
        } catch (Exception e) {
            view.showError("Lỗi khi tải dữ liệu combo box: " + e.getMessage());
        }
    }
    
    /**
     * Thực hiện tìm kiếm
     */
    private void performSearch() {
        SwingUtilities.invokeLater(() -> {
            try {
                String keyword = view.getTxtTimKiem().getText().trim();
                String searchType = (String) view.getCmbTimKiemTheo().getSelectedItem();
                List<DiemThi> result;
                
                if (keyword.isEmpty()) {
                    result = service.getAllDiemThi();
                } else {
                    switch (searchType) {
                        case "Theo thí sinh":
                            result = service.getDiemThiByThiSinh(keyword);
                            break;
                        case "Theo môn thi":
                            result = service.getDiemThiByMon(keyword);
                            break;
                        default:
                            result = service.searchDiemThi(keyword);
                            break;
                    }
                }
                
                List<ThiSinh> thiSinhs = thiSinhService.getAllThiSinh();
                List<MonThi> monThis = monThiService.getAllMonThi();
                view.updateTable(result, thiSinhs, monThis);
                view.updateStatus("Tìm thấy " + result.size() + " bản ghi");
            } catch (Exception e) {
                view.showError("Lỗi khi tìm kiếm: " + e.getMessage());
            }
        });
    }
    
    /**
     * Action listener cho nút Thêm
     */
    private class ThemActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.setAddMode();
        }
    }
    
    /**
     * Action listener cho nút Sửa
     */
    private class SuaActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (view.getTableDiemThi().getSelectedRow() >= 0) {
                view.setEditMode();
            } else {
                view.showError("Vui lòng chọn bản ghi điểm thi cần sửa");
            }
        }
    }
    
    /**
     * Action listener cho nút Xóa
     */
    private class XoaActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int selectedRow = view.getTableDiemThi().getSelectedRow();
            if (selectedRow >= 0) {
                String soBaoDanh = (String) view.getTableModel().getValueAt(selectedRow, 0);
                String maMon = (String) view.getTableModel().getValueAt(selectedRow, 2);
                
                if (view.confirmDelete(soBaoDanh, maMon)) {
                    try {
                        if (service.deleteDiemThi(soBaoDanh, maMon)) {
                            view.showSuccess("Xóa điểm thi thành công");
                            loadData();
                            view.resetForm();
                        } else {
                            view.showError("Không thể xóa điểm thi");
                        }
                    } catch (Exception ex) {
                        view.showError("Lỗi khi xóa: " + ex.getMessage());
                    }
                }
            } else {
                view.showError("Vui lòng chọn bản ghi điểm thi cần xóa");
            }
        }
    }
    
    /**
     * Action listener cho nút Lưu
     */
    private class LuuActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                DiemThi diemThi = view.getFormData();
                
                // Validate dữ liệu
                String validationError = service.validateDiemThi(diemThi);
                if (validationError != null) {
                    view.showError(validationError);
                    return;
                }
                
                boolean success;
                if (view.isEditing()) {
                    // Cập nhật
                    success = service.updateDiemThi(diemThi);
                    if (success) {
                        view.showSuccess("Cập nhật điểm thi thành công");
                    } else {
                        view.showError("Không thể cập nhật điểm thi");
                    }
                } else {
                    // Thêm mới
                    success = service.addDiemThi(diemThi);
                    if (success) {
                        view.showSuccess("Thêm điểm thi thành công");
                    } else {
                        view.showError("Không thể thêm điểm thi. Điểm có thể đã tồn tại");
                    }
                }
                
                if (success) {
                    loadData();
                    view.resetForm();
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi khi lưu: " + ex.getMessage());
            }
        }
    }
    
    /**
     * Action listener cho nút Hủy
     */
    private class HuyActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.resetForm();
        }
    }
    
    /**
     * Action listener cho nút Tìm kiếm
     */
    private class TimKiemActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            performSearch();
        }
    }
    
    /**
     * Action listener cho nút Làm mới
     */
    private class LamMoiActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.getTxtTimKiem().setText("");
            view.getCmbTimKiemTheo().setSelectedIndex(0);
            loadData();
            loadComboBoxData();
            view.resetForm();
        }
    }
    
    /**
     * Hiển thị view
     */
    public void showView() {
        view.setVisible(true);
    }
}
