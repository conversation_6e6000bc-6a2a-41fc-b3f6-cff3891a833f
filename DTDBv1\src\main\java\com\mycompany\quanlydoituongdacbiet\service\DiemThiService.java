package com.mycompany.quanlydoituongdacbiet.service;

import com.mycompany.quanlydoituongdacbiet.dao.DiemThiDAO;
import com.mycompany.quanlydoituongdacbiet.model.DiemThi;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Service layer cho quản lý điểm thi
 */
public class DiemThiService {
    private static DiemThiService instance;
    private DiemThiDAO diemThiDAO;
    private ThiSinhService thiSinhService;
    private MonThiService monThiService;
    
    private DiemThiService() {
        diemThiDAO = new DiemThiDAO();
        thiSinhService = ThiSinhService.getInstance();
        monThiService = MonThiService.getInstance();
    }
    
    public static DiemThiService getInstance() {
        if (instance == null) {
            instance = new DiemThiService();
        }
        return instance;
    }
    
    /**
     * L<PERSON>y tất cả điểm thi
     */
    public List<DiemThi> getAllDiemThi() {
        return diemThiDAO.getAllDiemThi();
    }
    
    /**
     * Thêm điểm thi mới
     */
    public boolean addDiemThi(DiemThi diemThi) {
        // Validation
        String validationError = validateDiemThi(diemThi);
        if (validationError != null) {
            return false;
        }
        
        return diemThiDAO.addDiemThi(diemThi);
    }
    
    /**
     * Cập nhật điểm thi
     */
    public boolean updateDiemThi(DiemThi diemThi) {
        // Validation
        String validationError = validateDiemThi(diemThi);
        if (validationError != null) {
            return false;
        }
        
        return diemThiDAO.updateDiemThi(diemThi);
    }
    
    /**
     * Xóa điểm thi
     */
    public boolean deleteDiemThi(String soBaoDanh, String maMon) {
        if (soBaoDanh == null || soBaoDanh.trim().isEmpty() ||
            maMon == null || maMon.trim().isEmpty()) {
            return false;
        }
        
        return diemThiDAO.deleteDiemThi(soBaoDanh, maMon);
    }
    
    /**
     * Lấy điểm thi của thí sinh theo môn
     */
    public DiemThi getDiemThi(String soBaoDanh, String maMon) {
        if (soBaoDanh == null || soBaoDanh.trim().isEmpty() ||
            maMon == null || maMon.trim().isEmpty()) {
            return null;
        }
        return diemThiDAO.getDiemThi(soBaoDanh, maMon);
    }
    
    /**
     * Lấy tất cả điểm thi của một thí sinh
     */
    public List<DiemThi> getDiemThiByThiSinh(String soBaoDanh) {
        if (soBaoDanh == null || soBaoDanh.trim().isEmpty()) {
            return null;
        }
        return diemThiDAO.getDiemThiByThiSinh(soBaoDanh);
    }
    
    /**
     * Lấy tất cả điểm thi của một môn
     */
    public List<DiemThi> getDiemThiByMon(String maMon) {
        if (maMon == null || maMon.trim().isEmpty()) {
            return null;
        }
        return diemThiDAO.getDiemThiByMon(maMon);
    }
    
    /**
     * Tìm kiếm điểm thi theo từ khóa
     */
    public List<DiemThi> searchDiemThi(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllDiemThi();
        }
        // Tìm kiếm trong tất cả điểm thi
        List<DiemThi> allDiem = getAllDiemThi();
        List<DiemThi> result = new ArrayList<>();

        for (DiemThi diemThi : allDiem) {
            if (diemThi.getSoBaoDanh().toLowerCase().contains(keyword.toLowerCase()) ||
                diemThi.getMaMon().toLowerCase().contains(keyword.toLowerCase())) {
                result.add(diemThi);
            }
        }
        return result;
    }

    /**
     * Lấy thống kê điểm theo môn
     */
    public Map<String, Double> getThongKeDiemTheoMon() {
        return diemThiDAO.getThongKeDiemTheoMon();
    }

    /**
     * Lấy điểm trung bình của thí sinh
     */
    public double getDiemTrungBinh(String soBaoDanh) {
        return diemThiDAO.getDiemTrungBinh(soBaoDanh);
    }
    
    /**
     * Kiểm tra thí sinh đã có điểm môn này chưa
     */
    public boolean isDiemThiExists(String soBaoDanh, String maMon) {
        return getDiemThi(soBaoDanh, maMon) != null;
    }
    
    /**
     * Nhập điểm hoặc cập nhật nếu đã có
     */
    public boolean nhapDiem(String soBaoDanh, String maMon, double diem, Date ngayThi) {
        DiemThi diemThi = new DiemThi(soBaoDanh, maMon, diem, ngayThi);
        
        if (isDiemThiExists(soBaoDanh, maMon)) {
            return updateDiemThi(diemThi);
        } else {
            return addDiemThi(diemThi);
        }
    }
    
    /**
     * Validate dữ liệu điểm thi
     */
    public String validateDiemThi(DiemThi diemThi) {
        if (diemThi == null) {
            return "Thông tin điểm thi không được để trống";
        }
        
        if (diemThi.getSoBaoDanh() == null || diemThi.getSoBaoDanh().trim().isEmpty()) {
            return "Số báo danh không được để trống";
        }
        
        if (diemThi.getMaMon() == null || diemThi.getMaMon().trim().isEmpty()) {
            return "Mã môn không được để trống";
        }
        
        // Kiểm tra thí sinh có tồn tại không
        if (thiSinhService.findBySoBaoDanh(diemThi.getSoBaoDanh()) == null) {
            return "Thí sinh không tồn tại";
        }
        
        // Kiểm tra môn thi có tồn tại không
        if (!monThiService.isMonThiExists(diemThi.getMaMon())) {
            return "Môn thi không tồn tại";
        }
        
        // Kiểm tra điểm hợp lệ
        if (!diemThi.isValidDiem()) {
            return "Điểm phải từ 0 đến 10";
        }
        
        if (diemThi.getNgayThi() == null) {
            return "Ngày thi không được để trống";
        }
        
        // Kiểm tra ngày thi không được trong tương lai quá xa
        Date now = new Date();
        if (diemThi.getNgayThi().after(now)) {
            return "Ngày thi không được trong tương lai";
        }
        
        return null; // Hợp lệ
    }
}
