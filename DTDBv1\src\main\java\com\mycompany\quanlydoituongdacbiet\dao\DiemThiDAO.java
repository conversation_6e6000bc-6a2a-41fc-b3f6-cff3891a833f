package com.mycompany.quanlydoituongdacbiet.dao;

import com.mycompany.quanlydoituongdacbiet.model.DiemThi;
import com.mycompany.quanlydoituongdacbiet.utils.FileManager;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DAO để quản lý dữ liệu điểm thi
 */
public class DiemThiDAO {
    
    /**
     * Lấy tất cả điểm thi
     */
    public List<DiemThi> getAllDiemThi() {
        List<DiemThi> diemThis = new ArrayList<>();
        List<String> lines = FileManager.readAllLines(FileManager.DIEMTHIS_FILE);
        
        for (String line : lines) {
            DiemThi diemThi = DiemThi.fromString(line);
            if (diemThi != null) {
                diemThis.add(diemThi);
            }
        }
        
        return diemThis;
    }
    
    /**
     * Thêm điểm thi mới
     */
    public boolean addDiemThi(DiemThi diemThi) {
        try {
            // Kiểm tra đã có điểm môn này của thí sinh chưa
            if (getDiemThi(diemThi.getSoBaoDanh(), diemThi.getMaMon()) != null) {
                return false; // Đã tồn tại, cần update thay vì add
            }
            
            FileManager.appendLine(FileManager.DIEMTHIS_FILE, diemThi.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Cập nhật điểm thi
     */
    public boolean updateDiemThi(DiemThi diemThi) {
        try {
            // Tìm và cập nhật dòng có cùng số báo danh và mã môn
            List<String> lines = FileManager.readAllLines(FileManager.DIEMTHIS_FILE);
            List<String> newLines = new ArrayList<>();
            boolean found = false;
            
            for (String line : lines) {
                String[] parts = line.split("\\|");
                if (parts.length >= 2 && parts[0].equals(diemThi.getSoBaoDanh()) 
                    && parts[1].equals(diemThi.getMaMon())) {
                    newLines.add(diemThi.toString());
                    found = true;
                } else {
                    newLines.add(line);
                }
            }
            
            if (found) {
                FileManager.writeAllLines(FileManager.DIEMTHIS_FILE, newLines);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Xóa điểm thi
     */
    public boolean deleteDiemThi(String soBaoDanh, String maMon) {
        try {
            List<String> lines = FileManager.readAllLines(FileManager.DIEMTHIS_FILE);
            List<String> newLines = new ArrayList<>();
            
            for (String line : lines) {
                String[] parts = line.split("\\|");
                if (!(parts.length >= 2 && parts[0].equals(soBaoDanh) && parts[1].equals(maMon))) {
                    newLines.add(line);
                }
            }
            
            FileManager.writeAllLines(FileManager.DIEMTHIS_FILE, newLines);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Lấy điểm thi của một thí sinh trong một môn
     */
    public DiemThi getDiemThi(String soBaoDanh, String maMon) {
        List<String> lines = FileManager.readAllLines(FileManager.DIEMTHIS_FILE);
        
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length >= 2 && parts[0].equals(soBaoDanh) && parts[1].equals(maMon)) {
                return DiemThi.fromString(line);
            }
        }
        
        return null;
    }
    
    /**
     * Lấy tất cả điểm thi của một thí sinh
     */
    public List<DiemThi> getDiemThiByThiSinh(String soBaoDanh) {
        List<DiemThi> result = new ArrayList<>();
        List<String> lines = FileManager.searchLines(FileManager.DIEMTHIS_FILE, soBaoDanh, 0);
        
        for (String line : lines) {
            DiemThi diemThi = DiemThi.fromString(line);
            if (diemThi != null) {
                result.add(diemThi);
            }
        }
        
        return result;
    }
    
    /**
     * Lấy tất cả điểm thi của một môn
     */
    public List<DiemThi> getDiemThiByMon(String maMon) {
        List<DiemThi> result = new ArrayList<>();
        List<String> lines = FileManager.searchLines(FileManager.DIEMTHIS_FILE, maMon, 1);
        
        for (String line : lines) {
            DiemThi diemThi = DiemThi.fromString(line);
            if (diemThi != null) {
                result.add(diemThi);
            }
        }
        
        return result;
    }
    
    /**
     * Tính điểm trung bình của một thí sinh
     */
    public double getDiemTrungBinh(String soBaoDanh) {
        List<DiemThi> diemList = getDiemThiByThiSinh(soBaoDanh);
        if (diemList.isEmpty()) {
            return 0.0;
        }
        
        double tong = 0.0;
        for (DiemThi diem : diemList) {
            tong += diem.getDiem();
        }
        
        return tong / diemList.size();
    }
    
    /**
     * Thống kê điểm theo môn
     */
    public Map<String, Double> getThongKeDiemTheoMon() {
        Map<String, Double> thongKe = new HashMap<>();
        Map<String, List<Double>> diemTheoMon = new HashMap<>();
        
        List<DiemThi> allDiem = getAllDiemThi();
        
        // Nhóm điểm theo môn
        for (DiemThi diem : allDiem) {
            String maMon = diem.getMaMon();
            if (!diemTheoMon.containsKey(maMon)) {
                diemTheoMon.put(maMon, new ArrayList<>());
            }
            diemTheoMon.get(maMon).add(diem.getDiem());
        }
        
        // Tính điểm trung bình cho mỗi môn
        for (Map.Entry<String, List<Double>> entry : diemTheoMon.entrySet()) {
            String maMon = entry.getKey();
            List<Double> danhSachDiem = entry.getValue();
            
            double tong = 0.0;
            for (Double diem : danhSachDiem) {
                tong += diem;
            }
            
            thongKe.put(maMon, tong / danhSachDiem.size());
        }
        
        return thongKe;
    }
    
    /**
     * Xóa tất cả điểm thi của một thí sinh
     */
    public boolean deleteAllDiemThiByThiSinh(String soBaoDanh) {
        try {
            List<String> lines = FileManager.readAllLines(FileManager.DIEMTHIS_FILE);
            List<String> newLines = new ArrayList<>();
            
            for (String line : lines) {
                String[] parts = line.split("\\|");
                if (!(parts.length >= 1 && parts[0].equals(soBaoDanh))) {
                    newLines.add(line);
                }
            }
            
            FileManager.writeAllLines(FileManager.DIEMTHIS_FILE, newLines);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}