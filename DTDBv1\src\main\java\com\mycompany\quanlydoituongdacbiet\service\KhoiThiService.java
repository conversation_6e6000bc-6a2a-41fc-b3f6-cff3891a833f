package com.mycompany.quanlydoituongdacbiet.service;

import com.mycompany.quanlydoituongdacbiet.dao.KhoiThiDAO;
import com.mycompany.quanlydoituongdacbiet.model.KhoiThi;
import java.util.List;

/**
 * Service layer cho quản lý khối thi
 */
public class KhoiThiService {
    private static KhoiThiService instance;
    private KhoiThiDAO khoiThiDAO;
    private MonThiService monThiService;
    
    private KhoiThiService() {
        khoiThiDAO = new KhoiThiDAO();
        monThiService = MonThiService.getInstance();
    }
    
    public static KhoiThiService getInstance() {
        if (instance == null) {
            instance = new KhoiThiService();
        }
        return instance;
    }
    
    /**
     * Lấy tất cả khối thi
     */
    public List<KhoiThi> getAllKhoiThi() {
        return khoiThiDAO.getAllKhoiThi();
    }
    
    /**
     * Thêm khối thi mới
     */
    public boolean addKhoiThi(KhoiThi khoiThi) {
        // Validation
        if (khoiThi == null || 
            khoiThi.getMaKhoi() == null || khoiThi.getMaKhoi().trim().isEmpty() ||
            khoiThi.getTenKhoi() == null || khoiThi.getTenKhoi().trim().isEmpty()) {
            return false;
        }
        
        // Kiểm tra các môn thi trong khối có tồn tại không
        if (khoiThi.getDanhSachMon() != null) {
            for (String maMon : khoiThi.getDanhSachMon()) {
                if (!monThiService.isMonThiExists(maMon)) {
                    return false; // Môn thi không tồn tại
                }
            }
        }
        
        return khoiThiDAO.addKhoiThi(khoiThi);
    }
    
    /**
     * Cập nhật thông tin khối thi
     */
    public boolean updateKhoiThi(KhoiThi khoiThi) {
        // Validation
        if (khoiThi == null || 
            khoiThi.getMaKhoi() == null || khoiThi.getMaKhoi().trim().isEmpty() ||
            khoiThi.getTenKhoi() == null || khoiThi.getTenKhoi().trim().isEmpty()) {
            return false;
        }
        
        // Kiểm tra các môn thi trong khối có tồn tại không
        if (khoiThi.getDanhSachMon() != null) {
            for (String maMon : khoiThi.getDanhSachMon()) {
                if (!monThiService.isMonThiExists(maMon)) {
                    return false; // Môn thi không tồn tại
                }
            }
        }
        
        return khoiThiDAO.updateKhoiThi(khoiThi);
    }
    
    /**
     * Xóa khối thi
     */
    public boolean deleteKhoiThi(String maKhoi) {
        if (maKhoi == null || maKhoi.trim().isEmpty()) {
            return false;
        }
        
        // TODO: Kiểm tra khối thi có đang được sử dụng không
        
        return khoiThiDAO.deleteKhoiThi(maKhoi);
    }
    
    /**
     * Tìm khối thi theo mã khối
     */
    public KhoiThi getKhoiThiByMaKhoi(String maKhoi) {
        if (maKhoi == null || maKhoi.trim().isEmpty()) {
            return null;
        }
        return khoiThiDAO.getKhoiThiByMaKhoi(maKhoi);
    }
    
    /**
     * Tìm kiếm khối thi theo tên
     */
    public List<KhoiThi> searchKhoiThiByName(String tenKhoi) {
        if (tenKhoi == null || tenKhoi.trim().isEmpty()) {
            return getAllKhoiThi();
        }
        return khoiThiDAO.searchKhoiThiByName(tenKhoi);
    }
    
    /**
     * Kiểm tra mã khối có tồn tại không
     */
    public boolean isKhoiThiExists(String maKhoi) {
        return getKhoiThiByMaKhoi(maKhoi) != null;
    }
    
    /**
     * Thêm môn vào khối thi
     */
    public boolean addMonToKhoi(String maKhoi, String maMon) {
        if (!isKhoiThiExists(maKhoi) || !monThiService.isMonThiExists(maMon)) {
            return false;
        }
        
        KhoiThi khoiThi = getKhoiThiByMaKhoi(maKhoi);
        if (khoiThi != null) {
            khoiThi.addMon(maMon);
            return updateKhoiThi(khoiThi);
        }
        return false;
    }
    
    /**
     * Xóa môn khỏi khối thi
     */
    public boolean removeMonFromKhoi(String maKhoi, String maMon) {
        if (!isKhoiThiExists(maKhoi)) {
            return false;
        }
        
        KhoiThi khoiThi = getKhoiThiByMaKhoi(maKhoi);
        if (khoiThi != null) {
            khoiThi.removeMon(maMon);
            return updateKhoiThi(khoiThi);
        }
        return false;
    }
    
    /**
     * Validate dữ liệu khối thi
     */
    public String validateKhoiThi(KhoiThi khoiThi) {
        if (khoiThi == null) {
            return "Thông tin khối thi không được để trống";
        }
        
        if (khoiThi.getMaKhoi() == null || khoiThi.getMaKhoi().trim().isEmpty()) {
            return "Mã khối không được để trống";
        }
        
        if (khoiThi.getTenKhoi() == null || khoiThi.getTenKhoi().trim().isEmpty()) {
            return "Tên khối không được để trống";
        }
        
        // Kiểm tra các môn thi có tồn tại không
        if (khoiThi.getDanhSachMon() != null) {
            for (String maMon : khoiThi.getDanhSachMon()) {
                if (!monThiService.isMonThiExists(maMon)) {
                    return "Môn thi " + maMon + " không tồn tại";
                }
            }
        }
        
        return null; // Hợp lệ
    }
}
