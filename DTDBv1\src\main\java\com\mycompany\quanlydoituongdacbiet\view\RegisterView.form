<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="com.mycompany.quanlydoituongdacbiet.view.RegisterView">
  <grid id="27dc6" binding="mainPanel" layout-manager="BorderLayout" hgap="0" vgap="0">
    <constraints>
      <xy x="20" y="20" width="600" height="500"/>
    </constraints>
    <properties>
      <background color="-1"/>
      <preferredSize width="600" height="500"/>
    </properties>
    <border type="none"/>
    <children>
      <component id="a1b2c" class="javax.swing.JLabel" binding="titleLabel">
        <constraints border-constraint="North"/>
        <properties>
          <font name="Arial" size="24" style="1"/>
          <foreground color="-16744192"/>
          <horizontalAlignment value="0"/>
          <text value="ĐĂNG KÝ TÀI KHOẢN"/>
        </properties>
      </component>
      <grid id="d3e4f" binding="formPanel" layout-manager="GridBagLayout">
        <constraints border-constraint="Center"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="none"/>
        <children>
          <component id="g5h6i" class="javax.swing.JLabel">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Tên đăng nhập:"/>
            </properties>
          </component>
          <component id="j7k8l" class="javax.swing.JTextField" binding="txtUsername">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="250" height="35"/>
              </gridbag>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="m9n0o" class="javax.swing.JLabel">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Mật khẩu:"/>
            </properties>
          </component>
          <component id="p1q2r" class="javax.swing.JPasswordField" binding="txtPassword">
            <constraints>
              <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="250" height="35"/>
              </gridbag>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="s3t4u" class="javax.swing.JLabel">
            <constraints>
              <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Họ tên:"/>
            </properties>
          </component>
          <component id="v5w6x" class="javax.swing.JTextField" binding="txtFullName">
            <constraints>
              <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="250" height="35"/>
              </gridbag>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="y7z8a" class="javax.swing.JLabel">
            <constraints>
              <grid row="3" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Email:"/>
            </properties>
          </component>
          <component id="b9c0d" class="javax.swing.JTextField" binding="txtEmail">
            <constraints>
              <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="250" height="35"/>
              </gridbag>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
        </children>
      </grid>
      <grid id="e1f2g" binding="buttonPanel" layout-manager="FlowLayout" hgap="10" vgap="10" flow-align="1">
        <constraints border-constraint="South"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="none"/>
        <children>
          <component id="h3i4j" class="javax.swing.JButton" binding="btnRegister">
            <constraints/>
            <properties>
              <background color="-13408513"/>
              <font name="Arial" size="14" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="120" height="40"/>
              <text value="Đăng ký"/>
            </properties>
          </component>
          <component id="k5l6m" class="javax.swing.JButton" binding="btnCancel">
            <constraints/>
            <properties>
              <background color="-3407617"/>
              <font name="Arial" size="14" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="120" height="40"/>
              <text value="Hủy"/>
            </properties>
          </component>
          <component id="n7o8p" class="javax.swing.JButton" binding="btnBackToLogin">
            <constraints/>
            <properties>
              <background color="-7829368"/>
              <font name="Arial" size="14" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="150" height="40"/>
              <text value="Về đăng nhập"/>
            </properties>
          </component>
        </children>
      </grid>
    </children>
  </grid>
</form>
