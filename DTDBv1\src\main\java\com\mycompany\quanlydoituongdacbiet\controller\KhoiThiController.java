package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.model.KhoiThi;
import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import com.mycompany.quanlydoituongdacbiet.service.KhoiThiService;
import com.mycompany.quanlydoituongdacbiet.service.MonThiService;
import com.mycompany.quanlydoituongdacbiet.view.KhoiThiManagementView;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import javax.swing.SwingUtilities;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;

/**
 * Controller cho quản lý khối thi
 */
public class KhoiThiController {
    private KhoiThiManagementView view;
    private KhoiThiService service;
    private MonThiService monThiService;
    
    public KhoiThiController(KhoiThiManagementView view) {
        this.view = view;
        this.service = KhoiThiService.getInstance();
        this.monThiService = MonThiService.getInstance();
        
        initEventListeners();
        loadData();
        loadAvailableMonThi();
    }
    
    /**
     * Khởi tạo event listeners
     */
    private void initEventListeners() {
        // Button listeners
        view.addThemListener(new ThemActionListener());
        view.addSuaListener(new SuaActionListener());
        view.addXoaListener(new XoaActionListener());
        view.addLuuListener(new LuuActionListener());
        view.addHuyListener(new HuyActionListener());
        view.addTimKiemListener(new TimKiemActionListener());
        view.addLamMoiListener(new LamMoiActionListener());
        
        // Search field listener
        view.getTxtTimKiem().getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) {
                performSearch();
            }
            
            @Override
            public void removeUpdate(DocumentEvent e) {
                performSearch();
            }
            
            @Override
            public void changedUpdate(DocumentEvent e) {
                performSearch();
            }
        });
    }
    
    /**
     * Load dữ liệu ban đầu
     */
    private void loadData() {
        try {
            List<KhoiThi> khoiThis = service.getAllKhoiThi();
            view.updateTable(khoiThis);
            view.updateStatus("Đã tải " + khoiThis.size() + " khối thi");
        } catch (Exception e) {
            view.showError("Lỗi khi tải dữ liệu: " + e.getMessage());
        }
    }
    
    /**
     * Load danh sách môn thi có sẵn
     */
    private void loadAvailableMonThi() {
        try {
            List<MonThi> monThis = monThiService.getAllMonThi();
            view.updateAvailableMonThi(monThis);
        } catch (Exception e) {
            view.showError("Lỗi khi tải danh sách môn thi: " + e.getMessage());
        }
    }
    
    /**
     * Load môn thi đã chọn cho khối thi
     */
    private void loadSelectedMonThiForKhoi(String maKhoi) {
        try {
            KhoiThi khoiThi = service.getKhoiThiByMaKhoi(maKhoi);
            if (khoiThi != null) {
                view.loadSelectedMonThi(khoiThi.getDanhSachMon());
            }
        } catch (Exception e) {
            view.showError("Lỗi khi tải môn thi của khối: " + e.getMessage());
        }
    }
    
    /**
     * Thực hiện tìm kiếm
     */
    private void performSearch() {
        SwingUtilities.invokeLater(() -> {
            try {
                String keyword = view.getTxtTimKiem().getText().trim();
                List<KhoiThi> result;
                
                if (keyword.isEmpty()) {
                    result = service.getAllKhoiThi();
                } else {
                    result = service.searchKhoiThiByName(keyword);
                }
                
                view.updateTable(result);
                view.updateStatus("Tìm thấy " + result.size() + " khối thi");
            } catch (Exception e) {
                view.showError("Lỗi khi tìm kiếm: " + e.getMessage());
            }
        });
    }
    
    /**
     * Action listener cho nút Thêm
     */
    private class ThemActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.setAddMode();
        }
    }
    
    /**
     * Action listener cho nút Sửa
     */
    private class SuaActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (view.getTableKhoiThi().getSelectedRow() >= 0) {
                view.setEditMode();
                // Load môn thi đã chọn cho khối này
                String maKhoi = view.getEditingMaKhoi();
                if (maKhoi != null) {
                    loadSelectedMonThiForKhoi(maKhoi);
                }
            } else {
                view.showError("Vui lòng chọn khối thi cần sửa");
            }
        }
    }
    
    /**
     * Action listener cho nút Xóa
     */
    private class XoaActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int selectedRow = view.getTableKhoiThi().getSelectedRow();
            if (selectedRow >= 0) {
                String maKhoi = (String) view.getTableModel().getValueAt(selectedRow, 0);
                
                if (view.confirmDelete(maKhoi)) {
                    try {
                        if (service.deleteKhoiThi(maKhoi)) {
                            view.showSuccess("Xóa khối thi thành công");
                            loadData();
                            view.resetForm();

                            // Refresh danh sách khối thi trong ThiSinhController
                            ThiSinhController.refreshKhoiThiDataStatic();
                        } else {
                            view.showError("Không thể xóa khối thi");
                        }
                    } catch (Exception ex) {
                        view.showError("Lỗi khi xóa: " + ex.getMessage());
                    }
                }
            } else {
                view.showError("Vui lòng chọn khối thi cần xóa");
            }
        }
    }
    
    /**
     * Action listener cho nút Lưu
     */
    private class LuuActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                KhoiThi khoiThi = view.getFormData();
                
                // Validate dữ liệu
                String validationError = service.validateKhoiThi(khoiThi);
                if (validationError != null) {
                    view.showError(validationError);
                    return;
                }
                
                boolean success;
                if (view.isEditing()) {
                    // Cập nhật
                    success = service.updateKhoiThi(khoiThi);
                    if (success) {
                        view.showSuccess("Cập nhật khối thi thành công");
                    } else {
                        view.showError("Không thể cập nhật khối thi");
                    }
                } else {
                    // Thêm mới
                    success = service.addKhoiThi(khoiThi);
                    if (success) {
                        view.showSuccess("Thêm khối thi thành công");
                    } else {
                        view.showError("Không thể thêm khối thi. Mã khối có thể đã tồn tại");
                    }
                }
                
                if (success) {
                    loadData();
                    view.resetForm();

                    // Refresh danh sách khối thi trong ThiSinhController
                    ThiSinhController.refreshKhoiThiDataStatic();
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi khi lưu: " + ex.getMessage());
            }
        }
    }
    
    /**
     * Action listener cho nút Hủy
     */
    private class HuyActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.resetForm();
        }
    }
    
    /**
     * Action listener cho nút Tìm kiếm
     */
    private class TimKiemActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            performSearch();
        }
    }
    
    /**
     * Action listener cho nút Làm mới
     */
    private class LamMoiActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.getTxtTimKiem().setText("");
            loadData();
            loadAvailableMonThi();
            view.resetForm();
        }
    }
    
    /**
     * Hiển thị view
     */
    public void showView() {
        view.setVisible(true);
    }
}
