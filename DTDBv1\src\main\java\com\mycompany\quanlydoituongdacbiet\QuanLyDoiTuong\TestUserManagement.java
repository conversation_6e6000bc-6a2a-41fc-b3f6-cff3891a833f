package com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong;

import com.mycompany.quanlydoituongdacbiet.view.UserManagementView;
import com.mycompany.quanlydoituongdacbiet.controller.UserManagementController;
import javax.swing.UIManager;

/**
 * Test User Management System với giao diện Swing
 * Chạy file này để test hệ thống quản lý người dùng
 */
public class TestUserManagement {
    public static void main(String[] args) {
        try {
            // Set Look and Feel
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        javax.swing.SwingUtilities.invokeLater(() -> {
            UserManagementView view = new UserManagementView();
            UserManagementController controller = new UserManagementController(view);
            controller.showView();
        });
    }
}
