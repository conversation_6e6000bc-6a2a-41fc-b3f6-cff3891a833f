# HƯỚNG DẪN SỬ DỤNG NÚT LƯU
## Hệ thống Quản lý Điểm thi Đại học

### 🎯 TỔNG QUAN

Tôi đã sửa lỗi và cải thiện logic của nút **Lưu** trong tất cả các module quản lý. Bây giờ bạn có thể:

✅ **Thêm mới** dữ liệu bằng cách bấm **Thêm** → nhập thông tin → bấm **Lưu**  
✅ **Chỉnh sửa** dữ liệu bằng cách chọn dòng → bấm **Sửa** → sửa thông tin → bấm **Lưu**  
✅ **Hủy bỏ** thao tác bằng cách bấm **Hủy** để quay về trạng thái ban đầu  

---

## 🔧 CÁC THAY ĐỔI ĐÃ THỰC HIỆN

### 1. **MonThiManagementView.java** - Quản lý Môn thi

#### Trước khi sửa:
- Nút **Lưu** bị disable sau khi bấm **Thêm**
- Logic enable/disable không đúng

#### Sau khi sửa:
```java
public void setAddMode() {
    // Clear form data
    txtMaMon.setText("");
    txtTenMon.setText("");
    txtMoTa.setText("");
    spnThoiGianThi.setValue(90);
    
    // Set form state for adding
    txtMaMon.setEditable(true);
    isEditing = false;
    editingMaMon = null;
    
    // Enable/disable buttons for add mode
    btnThem.setEnabled(false);
    btnSua.setEnabled(false);
    btnXoa.setEnabled(false);
    btnLuu.setEnabled(true);    // ✅ QUAN TRỌNG: Enable nút Lưu
    btnHuy.setEnabled(true);
    
    tableMonThi.clearSelection();
    updateStatus("Chế độ thêm mới - Nhập thông tin và bấm Lưu");
}
```

### 2. **KhoiThiManagementView.java** - Quản lý Khối thi

#### Cải thiện:
```java
public void setAddMode() {
    // Clear form data
    txtMaKhoi.setText("");
    txtTenKhoi.setText("");
    txtMoTa.setText("");
    
    // Clear selected subjects - Reset danh sách môn thi
    DefaultListModel<String> availableModel = (DefaultListModel<String>) listAvailableMon.getModel();
    DefaultListModel<String> selectedModel = (DefaultListModel<String>) listSelectedMon.getModel();
    
    // Move all subjects back to available list
    while (selectedModel.getSize() > 0) {
        String subject = selectedModel.remove(0);
        if (!availableModel.contains(subject)) {
            availableModel.addElement(subject);
        }
    }
    
    // Enable buttons correctly
    btnLuu.setEnabled(true);    // ✅ Enable nút Lưu
    btnHuy.setEnabled(true);
}
```

### 3. **DiemThiManagementView.java** - Quản lý Điểm thi

#### Cải thiện:
```java
public void setAddMode() {
    // Clear form data
    cmbSoBaoDanh.setSelectedIndex(-1);
    cmbMaMon.setSelectedIndex(-1);
    spnDiem.setValue(0.0);
    spnNgayThi.setValue(new Date());
    
    // Enable form controls
    cmbSoBaoDanh.setEnabled(true);
    cmbMaMon.setEnabled(true);
    
    // Enable buttons correctly
    btnLuu.setEnabled(true);    // ✅ Enable nút Lưu
    btnHuy.setEnabled(true);
}
```

---

## 📋 HƯỚNG DẪN SỬ DỤNG CHI TIẾT

### 🆕 **THÊM MỚI DỮ LIỆU**

#### Bước 1: Bấm nút **"Thêm"**
- Form sẽ được clear (xóa trắng)
- Nút **Lưu** và **Hủy** sẽ được enable (sáng lên)
- Nút **Thêm**, **Sửa**, **Xóa** sẽ bị disable (mờ đi)
- Status hiển thị: *"Chế độ thêm mới - Nhập thông tin và bấm Lưu"*

#### Bước 2: Nhập thông tin
**Môn thi:**
- Mã môn: VD: `TOAN`, `LY`, `HOA`
- Tên môn: VD: `Toán học`, `Vật lý`, `Hóa học`
- Thời gian thi: VD: `90`, `120`, `150` (phút)
- Mô tả: Thông tin bổ sung

**Khối thi:**
- Mã khối: VD: `A00`, `B00`, `C00`
- Tên khối: VD: `Khối A00`, `Khối B00`
- Chọn môn thi: Dùng nút **→** và **←** để thêm/bớt môn

**Điểm thi:**
- Chọn thí sinh từ ComboBox
- Chọn môn thi từ ComboBox
- Nhập điểm: 0.0 - 10.0
- Chọn ngày thi

#### Bước 3: Bấm nút **"Lưu"**
- Hệ thống sẽ validate dữ liệu
- Nếu hợp lệ: Hiển thị thông báo thành công, refresh bảng dữ liệu
- Nếu lỗi: Hiển thị thông báo lỗi cụ thể

#### Bước 4: Hoàn thành
- Form tự động reset về trạng thái ban đầu
- Nút **Thêm** được enable lại
- Dữ liệu mới xuất hiện trong bảng

---

### ✏️ **CHỈNH SỬA DỮ LIỆU**

#### Bước 1: Chọn dòng cần sửa
- Click vào dòng trong bảng dữ liệu
- Thông tin sẽ tự động load vào form

#### Bước 2: Bấm nút **"Sửa"**
- Form chuyển sang chế độ edit
- Nút **Lưu** và **Hủy** được enable
- Nút **Thêm**, **Sửa**, **Xóa** bị disable
- Status hiển thị: *"Chế độ chỉnh sửa - Sửa thông tin và bấm Lưu"*

#### Bước 3: Sửa thông tin
- **Lưu ý**: Mã không thể sửa (Mã môn, Mã khối)
- Chỉ sửa các thông tin khác như tên, mô tả, thời gian...

#### Bước 4: Bấm nút **"Lưu"**
- Hệ thống validate và cập nhật dữ liệu
- Hiển thị thông báo kết quả

---

### ❌ **HỦY BỎ THAO TÁC**

#### Bấm nút **"Hủy"** bất cứ lúc nào:
- Form reset về trạng thái ban đầu
- Tất cả nút về trạng thái mặc định
- Hủy bỏ mọi thay đổi chưa lưu

---

## 🚨 **XỬ LÝ LỖI VÀ VALIDATION**

### Lỗi thường gặp:

#### 1. **"Mã môn không được để trống"**
- **Nguyên nhân**: Chưa nhập mã môn
- **Khắc phục**: Nhập mã môn hợp lệ

#### 2. **"Không thể thêm môn thi. Mã môn có thể đã tồn tại"**
- **Nguyên nhân**: Mã môn đã có trong hệ thống
- **Khắc phục**: Dùng mã môn khác

#### 3. **"Thời gian thi phải lớn hơn 0"**
- **Nguyên nhân**: Thời gian thi <= 0
- **Khắc phục**: Nhập thời gian > 0 (thường 90-180 phút)

#### 4. **"Vui lòng chọn thí sinh và môn thi"**
- **Nguyên nhân**: Chưa chọn thí sinh hoặc môn thi khi nhập điểm
- **Khắc phục**: Chọn đầy đủ thông tin từ ComboBox

#### 5. **"Điểm phải từ 0.0 đến 10.0"**
- **Nguyên nhân**: Điểm ngoài khoảng cho phép
- **Khắc phục**: Nhập điểm trong khoảng 0.0 - 10.0

---

## 💡 **MẸO SỬ DỤNG**

### 1. **Kiểm tra Status Bar**
- Luôn đọc thông báo ở dưới cùng màn hình
- Status cho biết chế độ hiện tại và hướng dẫn tiếp theo

### 2. **Sử dụng phím tắt**
- **Enter**: Có thể dùng để submit form (tùy component)
- **Escape**: Có thể dùng để hủy (nếu được implement)

### 3. **Kiểm tra dữ liệu trước khi lưu**
- Đảm bảo tất cả field bắt buộc đã được điền
- Kiểm tra format dữ liệu (số, ngày tháng...)

### 4. **Backup dữ liệu**
- Dữ liệu được lưu trong thư mục `data/`
- Nên backup định kỳ các file `.txt`

---

## 🔍 **TROUBLESHOOTING**

### Nếu nút Lưu vẫn không hoạt động:

1. **Kiểm tra console/terminal** có lỗi gì không
2. **Restart ứng dụng** và thử lại
3. **Kiểm tra file dữ liệu** trong thư mục `data/`
4. **Đảm bảo quyền ghi** vào thư mục dự án

### Nếu dữ liệu không hiển thị:

1. **Bấm nút Làm mới** để reload dữ liệu
2. **Kiểm tra file dữ liệu** có tồn tại không
3. **Restart ứng dụng**

---

## ✅ **KIỂM TRA HOẠT ĐỘNG**

### Test case cơ bản:

1. **Mở ứng dụng** → Đăng nhập admin/admin
2. **Vào Quản lý Môn thi**
3. **Bấm Thêm** → Kiểm tra nút Lưu có sáng không
4. **Nhập thông tin** → Bấm Lưu → Kiểm tra có thành công không
5. **Chọn dòng** → Bấm Sửa → Sửa thông tin → Bấm Lưu
6. **Bấm Hủy** → Kiểm tra form có reset không

**Nếu tất cả đều OK → Hệ thống hoạt động bình thường! 🎉**

---

## 📞 **HỖ TRỢ**

Nếu vẫn gặp vấn đề, hãy:
1. Mô tả chi tiết bước đang làm
2. Chụp ảnh màn hình lỗi
3. Copy thông báo lỗi (nếu có)
4. Cho biết module nào đang gặp vấn đề

**Chúc bạn sử dụng thành công! 🚀**
