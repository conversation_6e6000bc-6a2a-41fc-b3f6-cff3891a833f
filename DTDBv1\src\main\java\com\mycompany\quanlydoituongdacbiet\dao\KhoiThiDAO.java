package com.mycompany.quanlydoituongdacbiet.dao;

import com.mycompany.quanlydoituongdacbiet.model.KhoiThi;
import com.mycompany.quanlydoituongdacbiet.utils.FileManager;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO để quản lý dữ liệu khối thi
 */
public class KhoiThiDAO {
    
    /**
     * Lấy tất cả khối thi
     */
    public List<KhoiThi> getAllKhoiThi() {
        List<KhoiThi> khoiThis = new ArrayList<>();
        List<String> lines = FileManager.readAllLines(FileManager.KHOITHIS_FILE);
        
        for (String line : lines) {
            KhoiThi khoiThi = KhoiThi.fromString(line);
            if (khoiThi != null) {
                khoiThis.add(khoiThi);
            }
        }
        
        return khoiThis;
    }
    
    /**
     * Thêm khối thi mới
     */
    public boolean addKhoiThi(KhoiThi khoiThi) {
        try {
            // Kiểm tra trùng mã khối
            if (getKhoiThiByMaKhoi(khoiThi.getMaKhoi()) != null) {
                return false; // Đã tồn tại
            }
            
            FileManager.appendLine(FileManager.KHOITHIS_FILE, khoiThi.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Cập nhật thông tin khối thi
     */
    public boolean updateKhoiThi(KhoiThi khoiThi) {
        try {
            FileManager.updateLine(FileManager.KHOITHIS_FILE, 
                                 khoiThi.getMaKhoi(), 0, khoiThi.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Xóa khối thi
     */
    public boolean deleteKhoiThi(String maKhoi) {
        try {
            FileManager.deleteLine(FileManager.KHOITHIS_FILE, maKhoi, 0);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Tìm khối thi theo mã khối
     */
    public KhoiThi getKhoiThiByMaKhoi(String maKhoi) {
        List<String> lines = FileManager.searchLines(FileManager.KHOITHIS_FILE, maKhoi, 0);
        if (!lines.isEmpty()) {
            return KhoiThi.fromString(lines.get(0));
        }
        return null;
    }
    
    /**
     * Tìm kiếm khối thi theo tên
     */
    public List<KhoiThi> searchKhoiThiByName(String tenKhoi) {
        List<KhoiThi> result = new ArrayList<>();
        List<String> lines = FileManager.searchLines(FileManager.KHOITHIS_FILE, tenKhoi, 1);
        
        for (String line : lines) {
            KhoiThi khoiThi = KhoiThi.fromString(line);
            if (khoiThi != null) {
                result.add(khoiThi);
            }
        }
        
        return result;
    }
    
    /**
     * Lấy danh sách khối thi có chứa môn học cụ thể
     */
    public List<KhoiThi> getKhoiThiByMon(String maMon) {
        List<KhoiThi> result = new ArrayList<>();
        List<KhoiThi> allKhoi = getAllKhoiThi();
        
        for (KhoiThi khoi : allKhoi) {
            if (khoi.getDanhSachMon() != null && khoi.getDanhSachMon().contains(maMon)) {
                result.add(khoi);
            }
        }
        
        return result;
    }
}