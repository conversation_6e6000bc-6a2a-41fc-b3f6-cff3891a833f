package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.model.User;
import com.mycompany.quanlydoituongdacbiet.service.UserService;
import com.mycompany.quanlydoituongdacbiet.view.UserManagementView;
import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Controller cho User Management
 */
public class UserController {
    
    private UserManagementView view;
    private UserService userService;
    private User currentUser;
    
    public UserController(UserManagementView view, User currentUser) {
        this.view = view;
        this.userService = UserService.getInstance();
        this.currentUser = currentUser;
        
        initEventListeners();
        loadAllUsers();
    }
    
    /**
     * Khởi tạo event listeners
     */
    private void initEventListeners() {
        view.addAddListener(new AddUserListener());
        view.addEditListener(new EditUserListener());
        view.addDeleteListener(new DeleteUserListener());
        view.addSaveListener(new SaveUserListener());
        view.addCancelListener(new CancelListener());
        view.addSearchListener(new SearchListener());
        view.addRefreshListener(new RefreshListener());
        view.addLogoutListener(new LogoutListener());
    }
    
    /**
     * Load tất cả users
     */
    private void loadAllUsers() {
        List<User> users = userService.getAllUsers();
        view.loadUsers(users);
        view.updateStatus("Đã tải " + users.size() + " người dùng");
    }
    
    /**
     * Listener cho nút Add
     */
    private class AddUserListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.resetForm();
            view.setEditing(false);
            view.updateStatus("Chế độ thêm người dùng mới");
            
            // Enable form for input
            view.getTxtUsername().setEnabled(true);
            view.getTxtUsername().requestFocus();
        }
    }
    
    /**
     * Listener cho nút Edit
     */
    private class EditUserListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int selectedRow = view.getTableUsers().getSelectedRow();
            if (selectedRow < 0) {
                view.showError("Vui lòng chọn người dùng cần sửa!");
                return;
            }
            
            String username = view.getTableModel().getValueAt(selectedRow, 0).toString();
            User user = userService.findByUsername(username);
            
            if (user != null) {
                view.setEditing(true);
                view.setEditingUsername(username);
                view.getTxtUsername().setEnabled(false); // Không cho sửa username
                view.updateStatus("Chế độ chỉnh sửa người dùng: " + username);
                
                // Clear password fields for security
                view.getTxtPassword().setText("");
                view.getTxtConfirmPassword().setText("");
                view.getTxtFullName().requestFocus();
            }
        }
    }
    
    /**
     * Listener cho nút Delete
     */
    private class DeleteUserListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int selectedRow = view.getTableUsers().getSelectedRow();
            if (selectedRow < 0) {
                view.showError("Vui lòng chọn người dùng cần xóa!");
                return;
            }
            
            String username = view.getTableModel().getValueAt(selectedRow, 0).toString();
            
            // Không cho phép xóa chính mình
            if (username.equals(currentUser.getUsername())) {
                view.showError("Không thể xóa tài khoản đang đăng nhập!");
                return;
            }
            
            int option = JOptionPane.showConfirmDialog(view,
                "Bạn có chắc chắn muốn xóa người dùng '" + username + "'?",
                "Xác nhận xóa",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);
                
            if (option == JOptionPane.YES_OPTION) {
                if (userService.deleteUser(username)) {
                    view.showSuccess("Đã xóa người dùng thành công!");
                    view.resetForm();
                    loadAllUsers();
                } else {
                    view.showError("Không thể xóa người dùng! (Có thể là admin cuối cùng)");
                }
            }
        }
    }
    
    /**
     * Listener cho nút Save
     */
    private class SaveUserListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (!validateForm()) {
                return;
            }
            
            try {
                User user = createUserFromForm();
                String validationError = userService.validateUser(user);
                
                if (validationError != null) {
                    view.showError(validationError);
                    return;
                }
                
                boolean success;
                if (view.isEditing()) {
                    // Update existing user
                    User existingUser = userService.findByUsername(view.getEditingUsername());
                    if (existingUser != null) {
                        // Keep old password if not changed
                        if (view.getTxtPassword().getPassword().length == 0) {
                            user.setPassword(existingUser.getPassword());
                        }
                        success = userService.updateUser(user);
                    } else {
                        success = false;
                    }
                } else {
                    // Add new user
                    if (userService.isUsernameExists(user.getUsername())) {
                        view.showError("Tên đăng nhập đã tồn tại!");
                        return;
                    }
                    success = userService.addUser(user);
                }
                
                if (success) {
                    String message = view.isEditing() ? "Đã cập nhật người dùng thành công!" : "Đã thêm người dùng mới thành công!";
                    view.showSuccess(message);
                    view.resetForm();
                    loadAllUsers();
                } else {
                    view.showError("Có lỗi xảy ra khi lưu người dùng!");
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi: " + ex.getMessage());
            }
        }
    }
    
    /**
     * Listener cho nút Cancel
     */
    private class CancelListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.resetForm();
            view.updateStatus("Đã hủy thao tác");
        }
    }
    
    /**
     * Listener cho nút Search
     */
    private class SearchListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String keyword = view.getTxtSearch().getText().trim();
            if (keyword.isEmpty()) {
                view.showError("Vui lòng nhập từ khóa tìm kiếm!");
                return;
            }
            
            List<User> results = userService.searchUsers(keyword);
            view.loadUsers(results);
            view.updateStatus("Tìm thấy " + results.size() + " kết quả cho: " + keyword);
        }
    }
    
    /**
     * Listener cho nút Refresh
     */
    private class RefreshListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.getTxtSearch().setText("");
            loadAllUsers();
            view.resetForm();
        }
    }
    
    /**
     * Listener cho nút Logout
     */
    private class LogoutListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int option = JOptionPane.showConfirmDialog(view,
                "Bạn có chắc chắn muốn đăng xuất?",
                "Xác nhận đăng xuất",
                JOptionPane.YES_NO_OPTION);
                
            if (option == JOptionPane.YES_OPTION) {
                view.dispose();
                // Có thể mở lại LoginView ở đây
            }
        }
    }
    
    /**
     * Validate form input
     */
    private boolean validateForm() {
        if (view.getTxtUsername().getText().trim().isEmpty()) {
            view.showError("Vui lòng nhập tên đăng nhập!");
            view.getTxtUsername().requestFocus();
            return false;
        }
        
        if (view.getTxtFullName().getText().trim().isEmpty()) {
            view.showError("Vui lòng nhập họ tên!");
            view.getTxtFullName().requestFocus();
            return false;
        }
        
        // Chỉ validate password khi thêm mới hoặc khi có nhập password
        if (!view.isEditing() || view.getTxtPassword().getPassword().length > 0) {
            if (view.getTxtPassword().getPassword().length == 0) {
                view.showError("Vui lòng nhập mật khẩu!");
                view.getTxtPassword().requestFocus();
                return false;
            }
            
            if (view.getTxtPassword().getPassword().length < 4) {
                view.showError("Mật khẩu phải có ít nhất 4 ký tự!");
                view.getTxtPassword().requestFocus();
                return false;
            }
            
            String password = new String(view.getTxtPassword().getPassword());
            String confirmPassword = new String(view.getTxtConfirmPassword().getPassword());
            
            if (!password.equals(confirmPassword)) {
                view.showError("Mật khẩu xác nhận không khớp!");
                view.getTxtConfirmPassword().requestFocus();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Tạo User object từ form
     */
    private User createUserFromForm() {
        String username = view.getTxtUsername().getText().trim();
        String fullName = view.getTxtFullName().getText().trim();
        String email = view.getTxtEmail().getText().trim();
        String password = new String(view.getTxtPassword().getPassword());
        String role = view.getCmbRole().getSelectedItem().toString();
        
        User user = new User(username, password, fullName, role);
        if (!email.isEmpty()) {
            user.setEmail(email);
        }
        
        return user;
    }
}
