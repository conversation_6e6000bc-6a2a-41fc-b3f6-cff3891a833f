# HỆ THỐNG LOGIN - QUẢN LÝ ĐỐI TƯỢNG ĐẶC BIỆT

## Tổng quan
Hệ thống login đã được tích hợp vào ứng dụng quản lý đối tượng đặc biệt với các tính năng:
- Đăng nhập/đăng xuất
- Quản lý người dùng (Admin)
- <PERSON><PERSON> quyền (ADMIN/USER)
- Lưu trữ dữ liệu trong file txt
- Đổi mật khẩu
- Validation đầu vào

## Cấu trúc File

### 1. Model
- `User.java`: Model đại diện cho người dùng
  - username: <PERSON>ê<PERSON> đăng nhập
  - password: Mật khẩu
  - fullName: <PERSON>ọ tên đầy đủ
  - role: <PERSON><PERSON><PERSON><PERSON> (ADMIN/USER)

### 2. DAO (Data Access Object)
- `UserDAO.java`: Quản lý dữ liệu người dùng
  - getAllUsers(): <PERSON><PERSON><PERSON> tất cả users
  - findByUsername(): Tìm user theo username
  - authenticate(): <PERSON><PERSON><PERSON> th<PERSON><PERSON> đăng nhập
  - addUser(): Thêm user mới
  - updateUser(): Cập nhật thông tin user
  - deleteUser(): Xóa user
  - changePassword(): Đổi mật khẩu

### 3. Controller
- `AuthenticationController.java`: Xử lý logic authentication
  - login(): Đăng nhập
  - logout(): Đăng xuất
  - isLoggedIn(): Kiểm tra trạng thái đăng nhập
  - getCurrentUser(): Lấy user hiện tại
  - isCurrentUserAdmin(): Kiểm tra quyền admin
  - registerUser(): Đăng ký user mới (chỉ admin)
  - deleteUser(): Xóa user (chỉ admin)
  - changePassword(): Đổi mật khẩu

### 4. View
- `LoginView.java`: Giao diện đăng nhập và quản lý
  - Màn hình đăng nhập
  - Menu chính sau khi đăng nhập
  - Quản lý người dùng (Admin)
  - Đổi mật khẩu
  - Thông tin tài khoản

### 5. Data
- `data/users.txt`: File lưu trữ thông tin người dùng
  - Format: username|password|fullName|role
  - Encoding: UTF-8

## Tài khoản mặc định

### Admin
- **Username**: admin
- **Password**: admin123
- **Họ tên**: Administrator
- **Quyền**: ADMIN

### User thường
- **Username**: user1
- **Password**: password123
- **Họ tên**: Nguyen Van A
- **Quyền**: USER

## Cách chạy ứng dụng

### 1. Compile
```bash
cd DTDBv1/src/main/java
javac -cp . com/mycompany/quanlydoituongdacbiet/*.java com/mycompany/quanlydoituongdacbiet/*/*.java
```

### 2. Chạy ứng dụng chính
```bash
java -cp . com.mycompany.quanlydoituongdacbiet.Main
```

### 3. Chạy test hệ thống
```bash
javac -cp . com/mycompany/quanlydoituongdacbiet/test/LoginSystemTest.java
java -cp . com.mycompany.quanlydoituongdacbiet.test.LoginSystemTest
```

## Tính năng chính

### 1. Đăng nhập
- Nhập username và password
- Validation đầu vào
- Xác thực với dữ liệu trong file
- Hiển thị thông báo thành công/thất bại

### 2. Quản lý người dùng (Admin only)
- Xem danh sách người dùng
- Thêm người dùng mới
- Xóa người dùng (không thể xóa chính mình)
- Phân quyền ADMIN/USER

### 3. Đổi mật khẩu
- Nhập mật khẩu hiện tại
- Nhập mật khẩu mới
- Xác nhận mật khẩu mới
- Validation độ dài mật khẩu (tối thiểu 3 ký tự)

### 4. Phân quyền
- **ADMIN**: Toàn quyền, có thể quản lý người dùng
- **USER**: Quyền hạn chế, chỉ có thể sử dụng các chức năng cơ bản

## Validation Rules

### Đăng nhập
- Username không được để trống
- Password không được để trống

### Đăng ký user mới
- Username: tối thiểu 3 ký tự, không được trùng
- Password: tối thiểu 3 ký tự
- Họ tên: không được để trống
- Role: ADMIN hoặc USER (mặc định USER)

### Đổi mật khẩu
- Mật khẩu hiện tại phải đúng
- Mật khẩu mới: tối thiểu 3 ký tự
- Xác nhận mật khẩu phải khớp

## Cấu trúc dữ liệu trong file

### Format trong users.txt
```
username|password|fullName|role
```

### Ví dụ
```
admin|admin123|Administrator|ADMIN
user1|password123|Nguyen Van A|USER
testuser|testpass|Test User|USER
```

## Lưu ý kỹ thuật

1. **Encoding**: Tất cả file sử dụng UTF-8
2. **Separator**: Sử dụng ký tự `|` để phân tách các trường
3. **File Management**: Sử dụng FileManager utility để đọc/ghi file
4. **Session**: Lưu trữ thông tin user hiện tại trong memory
5. **Security**: Mật khẩu lưu trữ dạng plain text (có thể cải thiện bằng hash)

## Mở rộng tương lai

1. **Hash password**: Sử dụng BCrypt hoặc SHA-256
2. **Session timeout**: Tự động đăng xuất sau thời gian nhất định
3. **Logging**: Ghi log các hoạt động đăng nhập/đăng xuất
4. **Backup**: Tự động backup file users.txt
5. **Import/Export**: Chức năng import/export danh sách người dùng

## Troubleshooting

### Lỗi thường gặp
1. **File not found**: Đảm bảo thư mục `data` tồn tại
2. **Encoding issues**: Đảm bảo console hỗ trợ UTF-8
3. **Permission denied**: Kiểm tra quyền ghi file trong thư mục data

### Debug
- Chạy LoginSystemTest để kiểm tra các chức năng cơ bản
- Kiểm tra file users.txt có tồn tại và có dữ liệu không
- Đảm bảo format dữ liệu trong file đúng chuẩn
