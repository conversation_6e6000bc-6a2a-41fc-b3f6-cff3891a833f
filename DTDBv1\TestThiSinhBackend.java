import com.mycompany.quanlydoituongdacbiet.service.ThiSinhService;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import java.util.Date;

/**
 * Test backend ThiSinh
 */
public class TestThiSinhBackend {
    
    public static void main(String[] args) {
        System.out.println("=== TEST THIsinh BACKEND ===");
        
        ThiSinhService thiSinhService = ThiSinhService.getInstance();
        
        // Test 1: L<PERSON>y tất cả thí sinh
        System.out.println("\n1. Test lấy tất cả thí sinh:");
        var allThiSinh = thiSinhService.getAllThiSinh();
        System.out.println("✅ Có " + allThiSinh.size() + " thí sinh trong hệ thống");
        
        // Test 2: Thêm thí sinh mới
        System.out.println("\n2. Test thêm thí sinh mới:");
        ThiSinh newThiSinh = new ThiSinh();
        newThiSinh.setSoBaoDanh("TS001");
        newThiSinh.setHoTen("Nguyễn Văn A");
        newThiSinh.setGioiTinh("Nam");
        newThiSinh.setNgaySinh(new Date());
        newThiSinh.setDiaChi("Hà Nội");
        newThiSinh.setSoDienThoai("**********");
        newThiSinh.setEmail("<EMAIL>");
        newThiSinh.setMaKhoi("A");
        
        boolean added = thiSinhService.addThiSinh(newThiSinh);
        if (added) {
            System.out.println("✅ Thêm thí sinh mới thành công!");
        } else {
            System.out.println("❌ Thêm thí sinh mới thất bại!");
        }
        
        // Test 3: Tìm kiếm thí sinh
        System.out.println("\n3. Test tìm kiếm thí sinh:");
        var searchResults = thiSinhService.searchThiSinh("Nguyễn");
        System.out.println("✅ Tìm thấy " + searchResults.size() + " thí sinh với từ khóa 'Nguyễn'");
        
        // Test 4: Validation
        System.out.println("\n4. Test validation:");
        ThiSinh invalidThiSinh = new ThiSinh();
        String validationError = thiSinhService.validateThiSinh(invalidThiSinh);
        if (validationError != null) {
            System.out.println("✅ Validation hoạt động đúng!");
            System.out.println("   Lỗi: " + validationError);
        } else {
            System.out.println("❌ Validation không hoạt động!");
        }
        
        // Test 5: Kiểm tra khối thi
        System.out.println("\n5. Test danh sách khối thi:");
        String[] khoiThis = thiSinhService.getAvailableKhoiThi();
        System.out.println("✅ Có " + khoiThis.length + " khối thi:");
        for (String khoi : khoiThis) {
            System.out.println("   - " + khoi);
        }
        
        System.out.println("\n=== KẾT QUẢ ===");
        System.out.println("Backend ThiSinh hoạt động tốt!");
        System.out.println("Có thể test GUI bằng cách:");
        System.out.println("1. Đăng nhập admin/admin");
        System.out.println("2. Click 'Quản lý Thí sinh'");
        System.out.println("3. Test các nút Thêm, Sửa, Xóa, Tìm kiếm");
    }
}
