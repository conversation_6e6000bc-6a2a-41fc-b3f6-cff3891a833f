package com.mycompany.quanlydoituongdacbiet.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Class chứa các hàm tiện ích cho việc xử lý ngày tháng
 */
public class DateUtils {
    
    public static final String DATE_FORMAT = "dd/MM/yyyy";
    public static final String DATETIME_FORMAT = "dd/MM/yyyy HH:mm:ss";
    
    private static final SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMAT);
    private static final SimpleDateFormat datetimeFormatter = new SimpleDateFormat(DATETIME_FORMAT);
    
    /**
     * Chuyển đổi Date thành String
     */
    public static String dateToString(Date date) {
        if (date == null) return "";
        return dateFormatter.format(date);
    }
    
    /**
     * Chuyển đổi String thành Date
     */
    public static Date stringToDate(String dateStr) {
        try {
            if (dateStr == null || dateStr.trim().isEmpty()) return null;
            return dateFormatter.parse(dateStr.trim());
        } catch (ParseException e) {
            System.err.println("Lỗi parse date: " + dateStr);
            return null;
        }
    }
    
    /**
     * Chuyển đổi Date thành String với format datetime
     */
    public static String datetimeToString(Date date) {
        if (date == null) return "";
        return datetimeFormatter.format(date);
    }
    
    /**
     * Chuyển đổi String thành Date với format datetime
     */
    public static Date stringToDatetime(String datetimeStr) {
        try {
            if (datetimeStr == null || datetimeStr.trim().isEmpty()) return null;
            return datetimeFormatter.parse(datetimeStr.trim());
        } catch (ParseException e) {
            System.err.println("Lỗi parse datetime: " + datetimeStr);
            return null;
        }
    }
    
    /**
     * Lấy ngày hiện tại
     */
    public static Date getCurrentDate() {
        return new Date();
    }
    
    /**
     * Lấy ngày hiện tại dạng String
     */
    public static String getCurrentDateString() {
        return dateToString(getCurrentDate());
    }
    
    /**
     * Kiểm tra date1 có trước date2 không
     */
    public static boolean isBefore(Date date1, Date date2) {
        if (date1 == null || date2 == null) return false;
        return date1.before(date2);
    }
    
    /**
     * Kiểm tra date1 có sau date2 không
     */
    public static boolean isAfter(Date date1, Date date2) {
        if (date1 == null || date2 == null) return false;
        return date1.after(date2);
    }
    
    /**
     * Kiểm tra chuỗi ngày có hợp lệ không
     */
    public static boolean isValidDateString(String dateStr) {
        return stringToDate(dateStr) != null;
    }
}