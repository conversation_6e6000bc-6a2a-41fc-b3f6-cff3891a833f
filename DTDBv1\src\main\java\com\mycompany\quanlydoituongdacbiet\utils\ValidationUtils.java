package com.mycompany.quanlydoituongdacbiet.utils;

import java.util.regex.Pattern;

/**
 * Class chứa các hàm validation dữ liệu
 */
public class ValidationUtils {
    
    // Regex patterns
    private static final Pattern EMAIL_PATTERN = 
        Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    
    private static final Pattern PHONE_PATTERN = 
        Pattern.compile("^[0-9]{10,11}$");
    
    private static final Pattern MA_PATTERN = 
        Pattern.compile("^[A-Z0-9]{2,10}$");
    
    /**
     * Kiểm tra email hợp lệ
     */
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * Kiểm tra số điện thoại hợp lệ
     */
    public static boolean isValidPhone(String phone) {
        return phone != null && PHONE_PATTERN.matcher(phone).matches();
    }
    
    /**
     * <PERSON>ểm tra mã hợp lệ (mã môn, mã khối, số báo danh)
     */
    public static boolean isValidMa(String ma) {
        return ma != null && MA_PATTERN.matcher(ma).matches();
    }
    
    /**
     * Kiểm tra điểm hợp lệ (0-10)
     */
    public static boolean isValidDiem(double diem) {
        return diem >= 0.0 && diem <= 10.0;
    }
    
    /**
     * Kiểm tra chuỗi không rỗng
     */
    public static boolean isNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }
    
    /**
     * Kiểm tra độ dài chuỗi
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (str == null) return false;
        int length = str.trim().length();
        return length >= minLength && length <= maxLength;
    }
    
    /**
     * Kiểm tra tên hợp lệ (chỉ chữ và khoảng trắng)
     */
    public static boolean isValidName(String name) {
        if (name == null || name.trim().isEmpty()) return false;
        return name.matches("^[a-zA-ZàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ\\s]+$");
    }
    
    /**
     * Chuẩn hóa tên (viết hoa chữ cái đầu)
     */
    public static String normalizeName(String name) {
        if (name == null || name.trim().isEmpty()) return "";
        
        String[] words = name.trim().toLowerCase().split("\\s+");
        StringBuilder result = new StringBuilder();
        
        for (String word : words) {
            if (word.length() > 0) {
                result.append(word.substring(0, 1).toUpperCase())
                      .append(word.substring(1))
                      .append(" ");
            }
        }
        
        return result.toString().trim();
    }

    public static boolean isValidId(String soBaoDanh) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }
}