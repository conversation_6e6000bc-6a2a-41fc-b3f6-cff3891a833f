package com.mycompany.quanlydoituongdacbiet.utils;

import java.io.*;
import java.util.*;

/**
 * Class quản lý việc đọc/ghi file dữ liệu
 */
public class FileManager {
    private static final String DATA_DIR = "data";
    
    // Tên các file dữ liệu
    public static final String THISINHS_FILE = DATA_DIR + "/thisinhs.txt";
    public static final String MONTHIS_FILE = DATA_DIR + "/monthis.txt";
    public static final String KHOITHIS_FILE = DATA_DIR + "/khoithis.txt";
    public static final String DIEMTHIS_FILE = DATA_DIR + "/diemthis.txt";
    
    static {
        // Tạo thư mục data nếu chưa tồn tại
        File dataDir = new File(DATA_DIR);
        if (!dataDir.exists()) {
            dataDir.mkdirs();
        }
        
        // Tạo các file nếu chưa tồn tại
        createFileIfNotExists(THISINHS_FILE);
        createFileIfNotExists(MONTHIS_FILE);
        createFileIfNotExists(KHOITHIS_FILE);
        createFileIfNotExists(DIEMTHIS_FILE);
    }
    
    /**
     * Tạo file nếu chưa tồn tại
     */
    private static void createFileIfNotExists(String fileName) {
        File file = new File(fileName);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                System.err.println("Lỗi tạo file: " + fileName);
                e.printStackTrace();
            }
        }
    }
    
    /**
     * Đọc tất cả dòng từ file
     */
    public static List<String> readAllLines(String fileName) {
        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(fileName), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    lines.add(line.trim());
                }
            }
        } catch (IOException e) {
            System.err.println("Lỗi đọc file: " + fileName);
            e.printStackTrace();
        }
        return lines;
    }
    
    /**
     * Ghi tất cả dòng vào file (ghi đè)
     */
    public static void writeAllLines(String fileName, List<String> lines) {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(fileName), "UTF-8"))) {
            for (String line : lines) {
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            System.err.println("Lỗi ghi file: " + fileName);
            e.printStackTrace();
        }
    }
    
    /**
     * Thêm một dòng vào cuối file
     */
    public static void appendLine(String fileName, String line) {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(fileName, true), "UTF-8"))) {
            writer.write(line);
            writer.newLine();
        } catch (IOException e) {
            System.err.println("Lỗi thêm dòng vào file: " + fileName);
            e.printStackTrace();
        }
    }
    
    /**
     * Xóa một dòng khỏi file dựa trên điều kiện
     */
    public static void deleteLine(String fileName, String searchKey, int keyIndex) {
        List<String> lines = readAllLines(fileName);
        List<String> newLines = new ArrayList<>();
        
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length > keyIndex && !parts[keyIndex].equals(searchKey)) {
                newLines.add(line);
            }
        }
        
        writeAllLines(fileName, newLines);
    }
    
    /**
     * Cập nhật một dòng trong file
     */
    public static void updateLine(String fileName, String searchKey, int keyIndex, String newLine) {
        List<String> lines = readAllLines(fileName);
        List<String> newLines = new ArrayList<>();
        
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length > keyIndex && parts[keyIndex].equals(searchKey)) {
                newLines.add(newLine);
            } else {
                newLines.add(line);
            }
        }
        
        writeAllLines(fileName, newLines);
    }
    
    /**
     * Tìm kiếm dòng theo key
     */
    public static List<String> searchLines(String fileName, String searchKey, int keyIndex) {
        List<String> result = new ArrayList<>();
        List<String> lines = readAllLines(fileName);
        
        for (String line : lines) {
            String[] parts = line.split("\\|");
            if (parts.length > keyIndex && parts[keyIndex].contains(searchKey)) {
                result.add(line);
            }
        }
        
        return result;
    }
}