package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.model.User;
import com.mycompany.quanlydoituongdacbiet.service.UserService;
import com.mycompany.quanlydoituongdacbiet.view.RegisterView;
import com.mycompany.quanlydoituongdacbiet.view.LoginManagementView;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.regex.Pattern;

/**
 * Controller cho Register functionality
 */
public class RegisterController {
    
    private RegisterView view;
    private UserService userService;
    private LoginManagementView loginView;
    
    // Email validation pattern
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    
    public RegisterController(RegisterView view, LoginManagementView loginView) {
        this.view = view;
        this.loginView = loginView;
        this.userService = UserService.getInstance();
        
        initEventListeners();
    }
    
    /**
     * Khởi tạo event listeners
     */
    private void initEventListeners() {
        view.addRegisterListener(new RegisterListener());
        view.addCancelListener(new CancelListener());
        view.addBackToLoginListener(new BackToLoginListener());
    }
    
    /**
     * Listener cho nút Register
     */
    private class RegisterListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                // Lấy thông tin từ form
                String username = view.getUsername();
                String password = view.getPassword();
                String confirmPassword = view.getConfirmPassword();
                String fullName = view.getFullName();
                String email = view.getEmail();
                
                // Validation
                if (!validateInput(username, password, confirmPassword, fullName, email)) {
                    return;
                }
                
                // Kiểm tra username đã tồn tại
                if (userService.findByUsername(username) != null) {
                    view.showError("Tên đăng nhập đã tồn tại! Vui lòng chọn tên khác.");
                    return;
                }
                
                // Tạo user mới (mặc định là USER role)
                User newUser = new User(username, password, fullName, email, "USER");
                
                // Lưu user
                if (userService.addUser(newUser)) {
                    view.showSuccess("Đăng ký thành công!\nBạn có thể đăng nhập với tài khoản: " + username);
                    
                    // Delay một chút rồi quay về login
                    Timer timer = new Timer(2000, new ActionListener() {
                        @Override
                        public void actionPerformed(ActionEvent e) {
                            backToLogin();
                        }
                    });
                    timer.setRepeats(false);
                    timer.start();
                    
                } else {
                    view.showError("Có lỗi xảy ra khi đăng ký. Vui lòng thử lại!");
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi hệ thống: " + ex.getMessage());
            }
        }
    }
    
    /**
     * Listener cho nút Cancel
     */
    private class CancelListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.clearForm();
            view.updateStatus("Đã xóa thông tin nhập");
        }
    }
    
    /**
     * Listener cho nút Back to Login
     */
    private class BackToLoginListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            backToLogin();
        }
    }
    
    /**
     * Quay về màn hình login
     */
    private void backToLogin() {
        view.setVisible(false);
        view.dispose();
        loginView.setVisible(true);
        loginView.toFront();
    }
    
    /**
     * Validate input data
     */
    private boolean validateInput(String username, String password, String confirmPassword, 
                                String fullName, String email) {
        
        // Kiểm tra các trường bắt buộc
        if (username.isEmpty()) {
            view.showError("Vui lòng nhập tên đăng nhập!");
            return false;
        }
        
        if (password.isEmpty()) {
            view.showError("Vui lòng nhập mật khẩu!");
            return false;
        }
        
        if (confirmPassword.isEmpty()) {
            view.showError("Vui lòng xác nhận mật khẩu!");
            return false;
        }
        
        if (fullName.isEmpty()) {
            view.showError("Vui lòng nhập họ và tên!");
            return false;
        }
        
        if (email.isEmpty()) {
            view.showError("Vui lòng nhập email!");
            return false;
        }
        
        // Kiểm tra độ dài username
        if (username.length() < 3) {
            view.showError("Tên đăng nhập phải có ít nhất 3 ký tự!");
            return false;
        }
        
        if (username.length() > 20) {
            view.showError("Tên đăng nhập không được quá 20 ký tự!");
            return false;
        }
        
        // Kiểm tra username chỉ chứa chữ cái, số và dấu gạch dưới
        if (!username.matches("^[a-zA-Z0-9_]+$")) {
            view.showError("Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới!");
            return false;
        }
        
        // Kiểm tra độ dài password
        if (password.length() < 6) {
            view.showError("Mật khẩu phải có ít nhất 6 ký tự!");
            return false;
        }
        
        if (password.length() > 50) {
            view.showError("Mật khẩu không được quá 50 ký tự!");
            return false;
        }
        
        // Kiểm tra password và confirm password khớp
        if (!password.equals(confirmPassword)) {
            view.showError("Mật khẩu và xác nhận mật khẩu không khớp!");
            return false;
        }
        
        // Kiểm tra độ dài full name
        if (fullName.length() < 2) {
            view.showError("Họ và tên phải có ít nhất 2 ký tự!");
            return false;
        }
        
        if (fullName.length() > 100) {
            view.showError("Họ và tên không được quá 100 ký tự!");
            return false;
        }
        
        // Kiểm tra format email
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            view.showError("Email không đúng định dạng!");
            return false;
        }
        
        if (email.length() > 100) {
            view.showError("Email không được quá 100 ký tự!");
            return false;
        }
        
        return true;
    }
}
