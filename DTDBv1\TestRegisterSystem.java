import com.mycompany.quanlydoituongdacbiet.view.LoginManagementView;
import com.mycompany.quanlydoituongdacbiet.controller.LoginController;

/**
 * Test class để kiểm tra hệ thống Register mới
 */
public class TestRegisterSystem {
    public static void main(String[] args) {
        try {
            System.out.println("=== KIỂM TRA HỆ THỐNG REGISTER ===");
            
            // Khởi tạo LoginView với nút Register mới
            LoginManagementView loginView = new LoginManagementView();
            LoginController loginController = new LoginController(loginView);
            
            // Hiển thị giao diện
            loginView.setVisible(true);
            
            System.out.println("✓ Đã khởi tạo LoginView với nút Register");
            System.out.println("✓ Hệ thống sẵn sàng để test:");
            System.out.println("  1. Nhấn nút 'Đăng ký' để mở form đăng ký");
            System.out.println("  2. Điền thông tin và đăng ký tài khoản mới");
            System.out.println("  3. Quay lại và đăng nhập với tài khoản vừa tạo");
            System.out.println("  4. Kiểm tra các chức năng quản lý thí sinh");
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi khi khởi tạo hệ thống: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
