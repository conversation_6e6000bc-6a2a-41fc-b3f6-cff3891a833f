<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="com.mycompany.quanlydoituongdacbiet.view.TraCuuDiemView">
  <grid id="27dc6" binding="mainPanel" layout-manager="BorderLayout" hgap="0" vgap="0">
    <constraints>
      <xy x="20" y="20" width="900" height="600"/>
    </constraints>
    <properties>
      <background color="-1"/>
      <preferredSize width="900" height="600"/>
    </properties>
    <border type="none"/>
    <children>
      <component id="a1b2c" class="javax.swing.JLabel" binding="titleLabel">
        <constraints border-constraint="North"/>
        <properties>
          <font name="Arial" size="24" style="1"/>
          <foreground color="-16744192"/>
          <horizontalAlignment value="0"/>
          <text value="TRA CỨU ĐIỂM THI"/>
        </properties>
      </component>
      <grid id="d3e4f" binding="searchPanel" layout-manager="GridBagLayout">
        <constraints border-constraint="North"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="line" title="Tìm kiếm thí sinh" title-color="-16744192"/>
        <children>
          <component id="g5h6i" class="javax.swing.JLabel">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Số báo danh:"/>
            </properties>
          </component>
          <component id="j7k8l" class="javax.swing.JTextField" binding="txtSoBaoDanh">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="200" height="35"/>
              </grid>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="m9n0o" class="javax.swing.JButton" binding="btnSearch">
            <constraints>
              <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <background color="-16744192"/>
              <font name="Arial" size="14" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="120" height="35"/>
              <text value="Tra cứu"/>
            </properties>
          </component>
          <component id="p1q2r" class="javax.swing.JLabel">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Khối thi:"/>
            </properties>
          </component>
          <component id="s3t4u" class="javax.swing.JComboBox" binding="cmbKhoiThi">
            <constraints>
              <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="200" height="35"/>
              </grid>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="v5w6x" class="javax.swing.JButton" binding="btnCalculateTotal">
            <constraints>
              <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <background color="-13408513"/>
              <font name="Arial" size="14" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="120" height="35"/>
              <text value="Tính tổng"/>
            </properties>
          </component>
        </children>
      </grid>
      <grid id="y7z8a" binding="resultPanel" layout-manager="BorderLayout" hgap="0" vgap="0">
        <constraints border-constraint="Center"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="line" title="Kết quả tra cứu" title-color="-16744192"/>
        <children>
          <grid id="b9c0d" binding="infoPanel" layout-manager="GridBagLayout">
            <constraints border-constraint="North"/>
            <properties>
              <background color="-1"/>
            </properties>
            <border type="none"/>
            <children>
              <component id="e1f2g" class="javax.swing.JLabel" binding="lblStudentInfo">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="1.0" weighty="0.0"/>
                </constraints>
                <properties>
                  <font name="Arial" size="14" style="1"/>
                  <foreground color="-16744192"/>
                  <text value="Thông tin thí sinh sẽ hiển thị ở đây"/>
                </properties>
              </component>
              <component id="h3i4j" class="javax.swing.JLabel" binding="lblTotalScore">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="1.0" weighty="0.0"/>
                </constraints>
                <properties>
                  <font name="Arial" size="16" style="1"/>
                  <foreground color="-3407617"/>
                  <text value=""/>
                </properties>
              </component>
            </children>
          </grid>
          <scrollpane id="k5l6m" binding="scrollPane">
            <constraints border-constraint="Center"/>
            <properties/>
            <border type="none"/>
            <children>
              <component id="n7o8p" class="javax.swing.JTable" binding="table">
                <constraints/>
                <properties>
                  <font name="Arial" size="12" style="0"/>
                  <rowHeight value="25"/>
                </properties>
              </component>
            </children>
          </scrollpane>
        </children>
      </grid>
      <grid id="q9r0s" binding="buttonPanel" layout-manager="FlowLayout" hgap="10" vgap="10" flow-align="1">
        <constraints border-constraint="South"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="none"/>
        <children>
          <component id="t1u2v" class="javax.swing.JButton" binding="btnClear">
            <constraints/>
            <properties>
              <background color="-7829368"/>
              <font name="Arial" size="14" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="120" height="40"/>
              <text value="Xóa kết quả"/>
            </properties>
          </component>
          <component id="w3x4y" class="javax.swing.JButton" binding="btnBack">
            <constraints/>
            <properties>
              <background color="-3407617"/>
              <font name="Arial" size="14" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="120" height="40"/>
              <text value="Quay lại"/>
            </properties>
          </component>
        </children>
      </grid>
    </children>
  </grid>
</form>
