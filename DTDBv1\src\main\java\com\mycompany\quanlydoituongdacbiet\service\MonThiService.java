package com.mycompany.quanlydoituongdacbiet.service;

import com.mycompany.quanlydoituongdacbiet.dao.MonThiDAO;
import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import java.util.List;

/**
 * Service layer cho quản lý môn thi
 */
public class MonThiService {
    private static MonThiService instance;
    private MonThiDAO monThiDAO;
    
    private MonThiService() {
        monThiDAO = new MonThiDAO();
    }
    
    public static MonThiService getInstance() {
        if (instance == null) {
            instance = new MonThiService();
        }
        return instance;
    }
    
    /**
     * Lấy tất cả môn thi
     */
    public List<MonThi> getAllMonThi() {
        return monThiDAO.getAllMonThi();
    }
    
    /**
     * Thêm môn thi mới
     */
    public boolean addMonThi(MonThi monThi) {
        // Validation
        if (monThi == null || 
            monThi.getMaMon() == null || monThi.getMaMon().trim().isEmpty() ||
            monThi.getTenMon() == null || monThi.getTenMon().trim().isEmpty()) {
            return false;
        }
        
        // Kiểm tra thời gian thi hợp lệ (> 0)
        if (monThi.getThoiGianThi() <= 0) {
            return false;
        }
        
        return monThiDAO.addMonThi(monThi);
    }
    
    /**
     * Cập nhật thông tin môn thi
     */
    public boolean updateMonThi(MonThi monThi) {
        // Validation
        if (monThi == null || 
            monThi.getMaMon() == null || monThi.getMaMon().trim().isEmpty() ||
            monThi.getTenMon() == null || monThi.getTenMon().trim().isEmpty()) {
            return false;
        }
        
        // Kiểm tra thời gian thi hợp lệ (> 0)
        if (monThi.getThoiGianThi() <= 0) {
            return false;
        }
        
        return monThiDAO.updateMonThi(monThi);
    }
    
    /**
     * Xóa môn thi
     */
    public boolean deleteMonThi(String maMon) {
        if (maMon == null || maMon.trim().isEmpty()) {
            return false;
        }
        
        // TODO: Kiểm tra môn thi có đang được sử dụng trong khối thi hoặc điểm thi không
        // Nếu có thì không cho xóa
        
        return monThiDAO.deleteMonThi(maMon);
    }
    
    /**
     * Tìm môn thi theo mã môn
     */
    public MonThi getMonThiByMaMon(String maMon) {
        if (maMon == null || maMon.trim().isEmpty()) {
            return null;
        }
        return monThiDAO.getMonThiByMaMon(maMon);
    }
    
    /**
     * Tìm kiếm môn thi theo tên
     */
    public List<MonThi> searchMonThiByName(String tenMon) {
        if (tenMon == null || tenMon.trim().isEmpty()) {
            return getAllMonThi();
        }
        return monThiDAO.searchMonThiByName(tenMon);
    }
    
    /**
     * Kiểm tra mã môn có tồn tại không
     */
    public boolean isMonThiExists(String maMon) {
        return getMonThiByMaMon(maMon) != null;
    }
    
    /**
     * Validate dữ liệu môn thi
     */
    public String validateMonThi(MonThi monThi) {
        if (monThi == null) {
            return "Thông tin môn thi không được để trống";
        }
        
        if (monThi.getMaMon() == null || monThi.getMaMon().trim().isEmpty()) {
            return "Mã môn không được để trống";
        }
        
        if (monThi.getTenMon() == null || monThi.getTenMon().trim().isEmpty()) {
            return "Tên môn không được để trống";
        }
        
        if (monThi.getThoiGianThi() <= 0) {
            return "Thời gian thi phải lớn hơn 0";
        }
        
        if (monThi.getThoiGianThi() > 300) { // Tối đa 5 tiếng
            return "Thời gian thi không được vượt quá 300 phút";
        }
        
        return null; // Hợp lệ
    }
}
