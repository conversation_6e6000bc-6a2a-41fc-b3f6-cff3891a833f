package com.mycompany.quanlydoituongdacbiet.service;

import com.mycompany.quanlydoituongdacbiet.model.User;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service class để quản lý User với file-based storage
 */
public class UserService {
    
    private static final String DATA_DIR = "data";
    private static final String USER_FILE = DATA_DIR + "/users.txt";
    private List<User> users;
    private static UserService instance;
    
    private UserService() {
        users = new ArrayList<>();
        createDataDirectory();
        loadUsers();
        
        // Tạo admin mặc định nếu chưa có
        if (users.isEmpty()) {
            createDefaultAdmin();
        }
    }
    
    /**
     * Singleton pattern
     */
    public static UserService getInstance() {
        if (instance == null) {
            instance = new UserService();
        }
        return instance;
    }
    
    /**
     * <PERSON><PERSON><PERSON> thư mục data nếu chưa tồn tại
     */
    private void createDataDirectory() {
        File dataDir = new File(DATA_DIR);
        if (!dataDir.exists()) {
            dataDir.mkdirs();
        }
    }
    
    /**
     * Tạo admin mặc định
     */
    private void createDefaultAdmin() {
        User admin = new User("admin", "admin", "Administrator", "ADMIN");
        admin.setEmail("<EMAIL>");
        users.add(admin);
        saveUsers();
    }
    
    /**
     * Load users từ file
     */
    private void loadUsers() {
        File file = new File(USER_FILE);
        if (!file.exists()) {
            return;
        }
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    User user = User.fromFileString(line);
                    if (user != null) {
                        users.add(user);
                    }
                }
            }
        } catch (IOException e) {
            System.err.println("Lỗi khi đọc file users: " + e.getMessage());
        }
    }
    
    /**
     * Lưu users vào file
     */
    private void saveUsers() {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(USER_FILE), StandardCharsets.UTF_8))) {
            for (User user : users) {
                writer.write(user.toFileString());
                writer.newLine();
            }
        } catch (IOException e) {
            System.err.println("Lỗi khi ghi file users: " + e.getMessage());
        }
    }
    
    /**
     * Đăng nhập
     */
    public User login(String username, String password) {
        if (username == null || password == null) {
            return null;
        }
        
        return users.stream()
                .filter(user -> username.equals(user.getUsername()) && 
                               password.equals(user.getPassword()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Lấy tất cả users
     */
    public List<User> getAllUsers() {
        return new ArrayList<>(users);
    }
    
    /**
     * Tìm user theo username
     */
    public User findByUsername(String username) {
        return users.stream()
                .filter(user -> username.equals(user.getUsername()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Thêm user mới
     */
    public boolean addUser(User user) {
        if (user == null || user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            return false;
        }
        
        // Kiểm tra username đã tồn tại
        if (findByUsername(user.getUsername()) != null) {
            return false;
        }
        
        users.add(user);
        saveUsers();
        return true;
    }
    
    /**
     * Cập nhật user
     */
    public boolean updateUser(User updatedUser) {
        if (updatedUser == null || updatedUser.getUsername() == null) {
            return false;
        }
        
        for (int i = 0; i < users.size(); i++) {
            if (users.get(i).getUsername().equals(updatedUser.getUsername())) {
                users.set(i, updatedUser);
                saveUsers();
                return true;
            }
        }
        return false;
    }
    
    /**
     * Xóa user
     */
    public boolean deleteUser(String username) {
        if (username == null) {
            return false;
        }
        
        // Không cho phép xóa admin cuối cùng
        long adminCount = users.stream()
                .filter(user -> "ADMIN".equals(user.getRole()))
                .count();
        
        User userToDelete = findByUsername(username);
        if (userToDelete != null && "ADMIN".equals(userToDelete.getRole()) && adminCount <= 1) {
            return false; // Không thể xóa admin cuối cùng
        }
        
        boolean removed = users.removeIf(user -> username.equals(user.getUsername()));
        if (removed) {
            saveUsers();
        }
        return removed;
    }
    
    /**
     * Tìm kiếm users
     */
    public List<User> searchUsers(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllUsers();
        }
        
        String lowerKeyword = keyword.toLowerCase().trim();
        return users.stream()
                .filter(user -> 
                    user.getUsername().toLowerCase().contains(lowerKeyword) ||
                    user.getFullName().toLowerCase().contains(lowerKeyword) ||
                    (user.getEmail() != null && user.getEmail().toLowerCase().contains(lowerKeyword)) ||
                    user.getRole().toLowerCase().contains(lowerKeyword)
                )
                .collect(Collectors.toList());
    }
    
    /**
     * Đổi mật khẩu
     */
    public boolean changePassword(String username, String oldPassword, String newPassword) {
        User user = findByUsername(username);
        if (user == null || !oldPassword.equals(user.getPassword())) {
            return false;
        }
        
        user.setPassword(newPassword);
        return updateUser(user);
    }
    
    /**
     * Kiểm tra username có tồn tại không
     */
    public boolean isUsernameExists(String username) {
        return findByUsername(username) != null;
    }
    
    /**
     * Lấy số lượng users
     */
    public int getUserCount() {
        return users.size();
    }
    
    /**
     * Lấy số lượng admins
     */
    public int getAdminCount() {
        return (int) users.stream()
                .filter(user -> "ADMIN".equals(user.getRole()))
                .count();
    }
    
    /**
     * Validate user data
     */
    public String validateUser(User user) {
        if (user == null) {
            return "Dữ liệu user không hợp lệ";
        }
        
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            return "Tên đăng nhập không được để trống";
        }
        
        if (user.getUsername().length() < 3) {
            return "Tên đăng nhập phải có ít nhất 3 ký tự";
        }
        
        if (user.getFullName() == null || user.getFullName().trim().isEmpty()) {
            return "Họ tên không được để trống";
        }
        
        if (user.getPassword() == null || user.getPassword().length() < 4) {
            return "Mật khẩu phải có ít nhất 4 ký tự";
        }
        
        if (user.getRole() == null || (!user.getRole().equals("ADMIN") && !user.getRole().equals("USER"))) {
            return "Vai trò phải là ADMIN hoặc USER";
        }
        
        // Validate email nếu có
        if (user.getEmail() != null && !user.getEmail().trim().isEmpty()) {
            if (!isValidEmail(user.getEmail())) {
                return "Email không hợp lệ";
            }
        }
        
        return null; // Hợp lệ
    }
    
    /**
     * Kiểm tra email hợp lệ
     */
    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
}
