# HƯỚNG DẪN TEST LIÊN KẾT KHỐI THI
## Giữa Quản lý Khối thi và Quản lý Thí sinh

### 🎯 **TÍNH NĂNG MỚI**

Tôi đã **thành công** tạo liên kết giữa 2 module:
- **Quản lý Khối thi** ↔ **Quản lý Thí sinh**
- Khi thêm/sửa/xóa khối thi → ComboBox khối thi trong Quản lý Thí sinh sẽ **tự động cập nhật**

---

## 🔧 **CÁC THAY ĐỔI ĐÃ THỰC HIỆN**

### 1. **ThiSinhService.java** ✅
```java
// Trước: Hardcode danh sách khối thi
public String[] getAvailableKhoiThi() {
    return new String[]{"A", "A1", "B", "C", "D", "D1"};
}

// Sau: <PERSON><PERSON><PERSON> từ <PERSON>hoiThiService (dữ liệu thật)
public String[] getAvailableKhoiThi() {
    List<KhoiThi> khoiThis = khoiThiService.getAllKhoiThi();
    String[] result = new String[khoiThis.size()];
    for (int i = 0; i < khoiThis.size(); i++) {
        KhoiThi khoi = khoiThis.get(i);
        result[i] = khoi.getMaKhoi() + " - " + khoi.getTenKhoi();
    }
    return result;
}
```

### 2. **ThiSinhController.java** ✅
```java
// Thêm static reference để các controller khác có thể gọi
private static ThiSinhController instance;

// Static method để refresh từ bất kỳ đâu
public static void refreshKhoiThiDataStatic() {
    if (instance != null) {
        instance.refreshKhoiThiData();
    }
}
```

### 3. **KhoiThiController.java** ✅
```java
// Sau khi thêm/sửa/xóa khối thi thành công
if (success) {
    loadData();
    view.resetForm();
    
    // ✅ QUAN TRỌNG: Refresh danh sách khối thi trong ThiSinhController
    ThiSinhController.refreshKhoiThiDataStatic();
}
```

---

## 📋 **HƯỚNG DẪN TEST CHI TIẾT**

### 🧪 **TEST CASE 1: THÊM KHỐI THI MỚI**

#### **Bước 1:** Mở Quản lý Thí sinh
1. Đăng nhập với **admin/admin**
2. Bấm **"Quản lý Thí sinh"**
3. **Quan sát** ComboBox "Khối thi" → Ghi nhớ các khối thi hiện có

#### **Bước 2:** Thêm khối thi mới
1. **Không đóng** cửa sổ Quản lý Thí sinh
2. Bấm **"Quản lý Khối thi"** (mở cửa sổ mới)
3. Bấm **"Thêm"**
4. Nhập thông tin khối thi mới:
   - **Mã khối:** `TEST01`
   - **Tên khối:** `Khối Test 01`
   - **Mô tả:** `Khối thi test liên kết`
   - **Chọn môn thi:** Thêm vài môn từ danh sách Available
5. Bấm **"Lưu"**
6. **Kiểm tra:** Thông báo "Thêm khối thi thành công"

#### **Bước 3:** Kiểm tra liên kết
1. **Quay lại** cửa sổ Quản lý Thí sinh
2. **Quan sát** ComboBox "Khối thi"
3. **✅ EXPECTED:** Khối thi `TEST01 - Khối Test 01` đã xuất hiện trong danh sách!

---

### 🧪 **TEST CASE 2: SỬA KHỐI THI**

#### **Bước 1:** Sửa khối thi vừa tạo
1. Trong Quản lý Khối thi, chọn dòng `TEST01`
2. Bấm **"Sửa"**
3. Đổi tên thành: `Khối Test Modified`
4. Bấm **"Lưu"**

#### **Bước 2:** Kiểm tra liên kết
1. **Quay lại** cửa sổ Quản lý Thí sinh
2. **Quan sát** ComboBox "Khối thi"
3. **✅ EXPECTED:** Tên đã thay đổi thành `TEST01 - Khối Test Modified`

---

### 🧪 **TEST CASE 3: XÓA KHỐI THI**

#### **Bước 1:** Xóa khối thi
1. Trong Quản lý Khối thi, chọn dòng `TEST01`
2. Bấm **"Xóa"**
3. Xác nhận xóa

#### **Bước 2:** Kiểm tra liên kết
1. **Quay lại** cửa sổ Quản lý Thí sinh
2. **Quan sát** ComboBox "Khối thi"
3. **✅ EXPECTED:** Khối thi `TEST01` đã biến mất khỏi danh sách!

---

### 🧪 **TEST CASE 4: THÊM THÍ SINH VỚI KHỐI THI MỚI**

#### **Bước 1:** Tạo khối thi mới
1. Trong Quản lý Khối thi, thêm khối: `ABC123 - Khối ABC`

#### **Bước 2:** Thêm thí sinh với khối thi mới
1. Trong Quản lý Thí sinh, bấm **"Thêm"**
2. Nhập thông tin thí sinh:
   - **Số báo danh:** `TS001`
   - **Họ tên:** `Nguyễn Test`
   - **Khối thi:** Chọn `ABC123 - Khối ABC`
   - Điền các thông tin khác
3. Bấm **"Lưu"**

#### **Bước 3:** Kiểm tra
1. **✅ EXPECTED:** Thí sinh được thêm thành công với khối thi mới
2. Trong bảng dữ liệu, cột "Khối thi" hiển thị `ABC123`

---

## 🚨 **TROUBLESHOOTING**

### **Vấn đề 1:** ComboBox không cập nhật
**Nguyên nhân có thể:**
- Cửa sổ Quản lý Thí sinh chưa được mở trước khi thêm khối thi
- Có lỗi trong quá trình lưu khối thi

**Khắc phục:**
1. Đóng và mở lại cửa sổ Quản lý Thí sinh
2. Kiểm tra console có lỗi gì không
3. Restart ứng dụng

### **Vấn đề 2:** Lỗi khi compile
**Khắc phục:**
```bash
# Compile lại các file liên quan
javac -cp . com/mycompany/quanlydoituongdacbiet/service/ThiSinhService.java
javac -cp . com/mycompany/quanlydoituongdacbiet/controller/ThiSinhController.java
javac -cp . com/mycompany/quanlydoituongdacbiet/controller/KhoiThiController.java
```

### **Vấn đề 3:** Dữ liệu không đồng bộ
**Nguyên nhân:** File dữ liệu bị lỗi
**Khắc phục:**
1. Kiểm tra file `data/khoithi.txt`
2. Đảm bảo format đúng: `MaKhoi|TenKhoi|MoTa|DanhSachMaMon`
3. Restart ứng dụng

---

## 🎯 **KẾT QUẢ MONG ĐỢI**

Sau khi test thành công, bạn sẽ có:

### ✅ **Tính năng hoạt động:**
1. **Real-time sync:** Thay đổi khối thi → Cập nhật ngay ComboBox thí sinh
2. **Dữ liệu thật:** Không còn hardcode, lấy từ file dữ liệu
3. **Tích hợp hoàn chỉnh:** 2 module làm việc đồng bộ với nhau

### ✅ **Workflow mới:**
```
Quản lý Khối thi (Thêm/Sửa/Xóa)
         ↓
    Lưu thành công
         ↓
Tự động refresh ComboBox trong Quản lý Thí sinh
         ↓
    Dữ liệu đồng bộ!
```

### ✅ **Lợi ích:**
- **Tiện lợi:** Không cần restart hay refresh thủ công
- **Chính xác:** Dữ liệu luôn đồng bộ giữa các module
- **Chuyên nghiệp:** Hệ thống hoạt động như phần mềm thương mại

---

## 🚀 **NEXT STEPS**

Sau khi test thành công, bạn có thể:

1. **Áp dụng tương tự** cho các module khác:
   - Quản lý Môn thi ↔ Quản lý Khối thi
   - Quản lý Môn thi ↔ Quản lý Điểm thi
   - Quản lý Thí sinh ↔ Quản lý Điểm thi

2. **Mở rộng tính năng:**
   - Auto-refresh khi có thay đổi từ file
   - Notification system giữa các module
   - Real-time data binding

3. **Cải thiện UI/UX:**
   - Loading indicator khi refresh
   - Animation cho việc cập nhật ComboBox
   - Status message khi sync thành công

---

## 💡 **TECHNICAL NOTES**

### **Architecture Pattern:**
```
KhoiThiController → ThiSinhController (Static Reference)
       ↓                    ↓
KhoiThiService ←→ ThiSinhService (Service Integration)
       ↓                    ↓
   KhoiThiDAO          ThiSinhDAO
       ↓                    ↓
  khoithi.txt          thisinh.txt
```

### **Key Components:**
- **Static Reference:** Cho phép cross-controller communication
- **Service Integration:** ThiSinhService sử dụng KhoiThiService
- **Event-driven Refresh:** Tự động cập nhật khi có thay đổi

**Chúc bạn test thành công! 🎉**

---

## 📞 **HỖ TRỢ**

Nếu gặp vấn đề trong quá trình test:
1. Mô tả chi tiết bước đang thực hiện
2. Chụp ảnh màn hình trước và sau
3. Copy thông báo lỗi (nếu có)
4. Cho biết test case nào đang thực hiện

**Tính năng liên kết đã sẵn sàng để sử dụng! 🚀**
