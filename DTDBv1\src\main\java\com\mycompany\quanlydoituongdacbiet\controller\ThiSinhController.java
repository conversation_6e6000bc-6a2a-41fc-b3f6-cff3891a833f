package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import com.mycompany.quanlydoituongdacbiet.service.ThiSinhService;
import com.mycompany.quanlydoituongdacbiet.view.ThiSinhManagementView;
import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Controller cho quản lý thí sinh
 */
public class ThiSinhController {
    private ThiSinhManagementView view;
    private ThiSinhService thiSinhService;
    
    public ThiSinhController(ThiSinhManagementView view) {
        this.view = view;
        this.thiSinhService = ThiSinhService.getInstance();
        initEventListeners();
        loadAllThiSinh();
        loadKhoiThiData();
    }
    
    /**
     * Khởi tạo event listeners
     */
    private void initEventListeners() {
        view.addAddListener(new AddThiSinhListener());
        view.addEditListener(new EditThiSinhListener());
        view.addDeleteListener(new DeleteThiSinhListener());
        view.addSaveListener(new SaveThiSinhListener());
        view.addCancelListener(new CancelListener());
        view.addSearchListener(new SearchListener());
        view.addRefreshListener(new RefreshListener());
    }
    
    /**
     * Load tất cả thí sinh
     */
    private void loadAllThiSinh() {
        List<ThiSinh> thiSinhs = thiSinhService.getAllThiSinh();
        view.loadThiSinhData(thiSinhs);
        view.updateStatus("Đã tải " + thiSinhs.size() + " thí sinh");
    }
    
    /**
     * Load dữ liệu khối thi
     */
    private void loadKhoiThiData() {
        String[] khoiThis = thiSinhService.getAvailableKhoiThi();
        view.loadKhoiThiData(khoiThis);
    }
    
    /**
     * Tạo ThiSinh object từ form
     */
    private ThiSinh createThiSinhFromForm() {
        return view.getThiSinhFromForm();
    }
    
    /**
     * Listener cho nút Thêm
     */
    private class AddThiSinhListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.startAdding();
        }
    }
    
    /**
     * Listener cho nút Sửa
     */
    private class EditThiSinhListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int selectedRow = view.getSelectedRow();
            if (selectedRow == -1) {
                view.showError("Vui lòng chọn thí sinh cần sửa!");
                return;
            }
            
            view.startEditing();
        }
    }
    
    /**
     * Listener cho nút Xóa
     */
    private class DeleteThiSinhListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int selectedRow = view.getSelectedRow();
            if (selectedRow == -1) {
                view.showError("Vui lòng chọn thí sinh cần xóa!");
                return;
            }
            
            String soBaoDanh = view.getSelectedSoBaoDanh();
            if (soBaoDanh == null) {
                view.showError("Không thể lấy thông tin thí sinh!");
                return;
            }
            
            int confirm = JOptionPane.showConfirmDialog(
                view,
                "Bạn có chắc chắn muốn xóa thí sinh: " + soBaoDanh + "?",
                "Xác nhận xóa",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE
            );
            
            if (confirm == JOptionPane.YES_OPTION) {
                boolean success = thiSinhService.deleteThiSinh(soBaoDanh);
                if (success) {
                    view.showSuccess("Xóa thí sinh thành công!");
                    loadAllThiSinh();
                    view.resetForm();
                } else {
                    view.showError("Xóa thí sinh thất bại!");
                }
            }
        }
    }
    
    /**
     * Listener cho nút Lưu
     */
    private class SaveThiSinhListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                ThiSinh thiSinh = createThiSinhFromForm();
                String validationError = thiSinhService.validateThiSinh(thiSinh);
                
                if (validationError != null) {
                    view.showError(validationError);
                    return;
                }
                
                boolean success;
                if (view.isEditing()) {
                    // Update existing thiSinh
                    success = thiSinhService.updateThiSinh(thiSinh);
                } else {
                    // Add new thiSinh
                    if (thiSinhService.isSoBaoDanhExists(thiSinh.getSoBaoDanh())) {
                        view.showError("Số báo danh đã tồn tại!");
                        return;
                    }
                    success = thiSinhService.addThiSinh(thiSinh);
                }
                
                if (success) {
                    String message = view.isEditing() ? "Cập nhật thí sinh thành công!" : "Thêm thí sinh thành công!";
                    view.showSuccess(message);
                    loadAllThiSinh();
                    view.resetForm();
                } else {
                    String message = view.isEditing() ? "Cập nhật thí sinh thất bại!" : "Thêm thí sinh thất bại!";
                    view.showError(message);
                }
                
            } catch (Exception ex) {
                view.showError("Lỗi: " + ex.getMessage());
                ex.printStackTrace();
            }
        }
    }
    
    /**
     * Listener cho nút Hủy
     */
    private class CancelListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.resetForm();
        }
    }
    
    /**
     * Listener cho nút Tìm kiếm
     */
    private class SearchListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String keyword = view.getSearchText();
            List<ThiSinh> results = thiSinhService.searchThiSinh(keyword);
            view.loadThiSinhData(results);
            
            if (keyword == null || keyword.trim().isEmpty()) {
                view.updateStatus("Hiển thị tất cả " + results.size() + " thí sinh");
            } else {
                view.updateStatus("Tìm thấy " + results.size() + " thí sinh với từ khóa: " + keyword);
            }
        }
    }
    
    /**
     * Listener cho nút Làm mới
     */
    private class RefreshListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            loadAllThiSinh();
            view.resetForm();
            view.clearSearchText();
        }
    }
}
