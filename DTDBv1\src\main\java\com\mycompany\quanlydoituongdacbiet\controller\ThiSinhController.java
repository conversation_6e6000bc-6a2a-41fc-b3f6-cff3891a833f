package com.mycompany.quanlydoituongdacbiet.controller;

import com.mycompany.quanlydoituongdacbiet.dao.ThiSinhDAO;
import com.mycompany.quanlydoituongdacbiet.dao.KhoiThiDAO;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import com.mycompany.quanlydoituongdacbiet.model.KhoiThi;
import com.mycompany.quanlydoituongdacbiet.view.ThiSinhManagementView;
import com.mycompany.quanlydoituongdacbiet.utils.ValidationUtils;
import javax.swing.JOptionPane;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Controller cho quản lý thí sinh
 */
public class ThiSinhController {
    private ThiSinhManagementView view;
    private ThiSinhDAO thiSinhDAO;
    private KhoiThiDAO khoiThiDAO;
    
    public ThiSinhController(ThiSinhManagementView view) {
        this.view = view;
        this.thiSinhDAO = new ThiSinhDAO();
        this.khoiThiDAO = new KhoiThiDAO();
        
        initController();
    }
    
    /**
     * Khởi tạo controller
     */
    private void initController() {
        // Load dữ liệu ban đầu
        loadKhoiThiData();
        loadAllThiSinh();
        
        // Thiết lập event handlers
        setupEventHandlers();
    }
    
    /**
     * Thiết lập event handlers
     */
    private void setupEventHandlers() {
        view.setThemActionListener(new ThemActionListener());
        view.setSuaActionListener(new SuaActionListener());
        view.setXoaActionListener(new XoaActionListener());
        view.setLuuActionListener(new LuuActionListener());
        view.setHuyActionListener(new HuyActionListener());
        view.setTimKiemActionListener(new TimKiemActionListener());
        view.setLamMoiActionListener(new LamMoiActionListener());
    }
    
    /**
     * Load danh sách khối thi
     */
    private void loadKhoiThiData() {
        try {
            List<KhoiThi> khoiThis = khoiThiDAO.getAllKhoiThi();
            view.loadKhoiThiData(khoiThis);
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(view, 
                "Lỗi khi load danh sách khối thi: " + e.getMessage(),
                "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * Load tất cả thí sinh
     */
    private void loadAllThiSinh() {
        try {
            List<ThiSinh> thiSinhs = thiSinhDAO.getAllThiSinh();
            view.loadThiSinhData(thiSinhs);
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(view, 
                "Lỗi khi load danh sách thí sinh: " + e.getMessage(),
                "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * Validate dữ liệu thí sinh
     */
    private boolean validateThiSinh(ThiSinh thiSinh) {
        // Kiểm tra các trường bắt buộc
        if (!ValidationUtils.isNotEmpty(thiSinh.getSoBaoDanh())) {
            JOptionPane.showMessageDialog(view, "Số báo danh không được để trống!");
            return false;
        }
        
        if (!ValidationUtils.isNotEmpty(thiSinh.getHoTen())) {
            JOptionPane.showMessageDialog(view, "Họ tên không được để trống!");
            return false;
        }
        
        if (!ValidationUtils.isNotEmpty(thiSinh.getMaKhoi())) {
            JOptionPane.showMessageDialog(view, "Vui lòng chọn khối thi!");
            return false;
        }
        
        // Kiểm tra format
        if (!ValidationUtils.isValidMa(thiSinh.getSoBaoDanh())) {
            JOptionPane.showMessageDialog(view, "Số báo danh không hợp lệ! (Chỉ chứa chữ hoa và số)");
            return false;
        }
        
        if (!ValidationUtils.isValidName(thiSinh.getHoTen())) {
            JOptionPane.showMessageDialog(view, "Họ tên không hợp lệ!");
            return false;
        }
        
        // Kiểm tra email nếu có
        if (ValidationUtils.isNotEmpty(thiSinh.getEmail()) && 
            !ValidationUtils.isValidEmail(thiSinh.getEmail())) {
            JOptionPane.showMessageDialog(view, "Email không hợp lệ!");
            return false;
        }
        
        // Kiểm tra số điện thoại nếu có
        if (ValidationUtils.isNotEmpty(thiSinh.getSoDienThoai()) && 
            !ValidationUtils.isValidPhone(thiSinh.getSoDienThoai())) {
            JOptionPane.showMessageDialog(view, "Số điện thoại không hợp lệ!");
            return false;
        }
        
        return true;
    }
    
    /**
     * Hiển thị view
     */
    public void showView() {
        view.setVisible(true);
    }
    
    // ==================== EVENT LISTENERS ====================
    
    /**
     * Xử lý sự kiện thêm thí sinh
     */
    private class ThemActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.startAdding();
        }
    }
    
    /**
     * Xử lý sự kiện sửa thí sinh
     */
    private class SuaActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.startEditing();
        }
    }
    
    /**
     * Xử lý sự kiện xóa thí sinh
     */
    private class XoaActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                ThiSinh thiSinh = view.getThiSinhFromForm();
                
                int option = JOptionPane.showConfirmDialog(view,
                    "Bạn có chắc chắn muốn xóa thí sinh " + thiSinh.getSoBaoDanh() + 
                    " - " + thiSinh.getHoTen() + "?",
                    "Xác nhận xóa",
                    JOptionPane.YES_NO_OPTION);
                
                if (option == JOptionPane.YES_OPTION) {
                    boolean success = thiSinhDAO.deleteThiSinh(thiSinh.getSoBaoDanh());
                    
                    if (success) {
                        JOptionPane.showMessageDialog(view, "Xóa thí sinh thành công!");
                        loadAllThiSinh();
                        view.resetForm();
                    } else {
                        JOptionPane.showMessageDialog(view, "Không thể xóa thí sinh!",
                            "Lỗi", JOptionPane.ERROR_MESSAGE);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                JOptionPane.showMessageDialog(view, "Lỗi khi xóa thí sinh: " + ex.getMessage(),
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Xử lý sự kiện lưu thí sinh
     */
    private class LuuActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                if (!view.validateForm()) {
                    return;
                }
                
                ThiSinh thiSinh = view.getThiSinhFromForm();
                
                // Chuẩn hóa dữ liệu
                thiSinh.setHoTen(ValidationUtils.normalizeName(thiSinh.getHoTen()));
                thiSinh.setSoBaoDanh(thiSinh.getSoBaoDanh().toUpperCase());
                
                if (!validateThiSinh(thiSinh)) {
                    return;
                }
                
                boolean success;
                String message;
                
                if (view.isEditing()) {
                    // Cập nhật thí sinh
                    success = thiSinhDAO.updateThiSinh(thiSinh);
                    message = success ? "Cập nhật thí sinh thành công!" : "Không thể cập nhật thí sinh!";
                } else {
                    // Thêm thí sinh mới
                    success = thiSinhDAO.addThiSinh(thiSinh);
                    if (!success) {
                        // Kiểm tra lỗi trùng số báo danh
                        ThiSinh existing = thiSinhDAO.getThiSinhBySoBaoDanh(thiSinh.getSoBaoDanh());
                        if (existing != null) {
                            JOptionPane.showMessageDialog(view, 
                                "Số báo danh " + thiSinh.getSoBaoDanh() + " đã tồn tại!",
                                "Lỗi", JOptionPane.ERROR_MESSAGE);
                            return;
                        }
                    }
                    message = success ? "Thêm thí sinh thành công!" : "Không thể thêm thí sinh!";
                }
                
                if (success) {
                    JOptionPane.showMessageDialog(view, message);
                    loadAllThiSinh();
                    view.resetForm();
                } else {
                    JOptionPane.showMessageDialog(view, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
                }
                
            } catch (Exception ex) {
                ex.printStackTrace();
                JOptionPane.showMessageDialog(view, "Lỗi khi lưu thí sinh: " + ex.getMessage(),
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Xử lý sự kiện hủy
     */
    private class HuyActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            view.resetForm();
        }
    }
    
    /**
     * Xử lý sự kiện tìm kiếm
     */
    private class TimKiemActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                String searchText = view.getSearchText();
                
                if (searchText.isEmpty()) {
                    loadAllThiSinh();
                    return;
                }
                
                List<ThiSinh> results;
                
                // Thử tìm theo số báo danh trước
                ThiSinh thiSinhBySoBaoDanh = thiSinhDAO.getThiSinhBySoBaoDanh(searchText.toUpperCase());
                if (thiSinhBySoBaoDanh != null) {
                    results = List.of(thiSinhBySoBaoDanh);
                } else {
                    // Tìm theo tên
                    results = thiSinhDAO.searchThiSinhByName(searchText);
                }
                
                view.loadThiSinhData(results);
                view.updateStatus("Tìm thấy " + results.size() + " kết quả cho: " + searchText);
                
            } catch (Exception ex) {
                ex.printStackTrace();
                JOptionPane.showMessageDialog(view, "Lỗi khi tìm kiếm: " + ex.getMessage(),
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Xử lý sự kiện làm mới
     */
    private class LamMoiActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            loadAllThiSinh();
            view.resetForm();
        }
    }
}