package com.mycompany.quanlydoituongdacbiet.service;

import com.mycompany.quanlydoituongdacbiet.model.DiemThi;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import com.mycompany.quanlydoituongdacbiet.model.KhoiThi;
import com.mycompany.quanlydoituongdacbiet.model.MonThi;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

/**
 * Service cho tra cứu điểm thi
 */
public class TraCuuDiemService {
    private static TraCuuDiemService instance;
    private DiemThiService diemThiService;
    private ThiSinhService thiSinhService;
    private KhoiThiService khoiThiService;
    private MonThiService monThiService;
    
    private TraCuuDiemService() {
        diemThiService = DiemThiService.getInstance();
        thiSinhService = ThiSinhService.getInstance();
        khoiThiService = KhoiThiService.getInstance();
        monThiService = MonThiService.getInstance();
    }
    
    public static TraCuuDiemService getInstance() {
        if (instance == null) {
            instance = new TraCuuDiemService();
        }
        return instance;
    }
    
    /**
     * Tra cứu thông tin thí sinh theo số báo danh
     */
    public ThiSinh getThiSinhInfo(String soBaoDanh) {
        if (soBaoDanh == null || soBaoDanh.trim().isEmpty()) {
            return null;
        }
        return thiSinhService.findBySoBaoDanh(soBaoDanh.trim());
    }
    
    /**
     * Lấy tất cả điểm thi của thí sinh
     */
    public List<DiemThi> getAllDiemThiByThiSinh(String soBaoDanh) {
        if (soBaoDanh == null || soBaoDanh.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<DiemThi> diemThis = diemThiService.getDiemThiByThiSinh(soBaoDanh.trim());
        return diemThis != null ? diemThis : new ArrayList<>();
    }
    
    /**
     * Lấy điểm thi với thông tin môn thi chi tiết
     */
    public List<DiemThiDetail> getDiemThiDetails(String soBaoDanh) {
        List<DiemThi> diemThis = getAllDiemThiByThiSinh(soBaoDanh);
        List<DiemThiDetail> details = new ArrayList<>();
        
        for (DiemThi diemThi : diemThis) {
            MonThi monThi = monThiService.getMonThiByMaMon(diemThi.getMaMon());
            String tenMon = monThi != null ? monThi.getTenMon() : "Không xác định";
            
            DiemThiDetail detail = new DiemThiDetail(
                diemThi.getMaMon(),
                tenMon,
                diemThi.getDiem(),
                diemThi.getNgayThi(),
                diemThi.getXepLoai()
            );
            details.add(detail);
        }
        
        return details;
    }
    
    /**
     * Tính tổng điểm khối thi
     */
    public double tinhTongDiemKhoiThi(String soBaoDanh, String maKhoi) {
        if (soBaoDanh == null || soBaoDanh.trim().isEmpty() || 
            maKhoi == null || maKhoi.trim().isEmpty()) {
            return 0.0;
        }
        
        // Lấy thông tin khối thi
        KhoiThi khoiThi = khoiThiService.getKhoiThiByMaKhoi(maKhoi.trim());
        if (khoiThi == null || khoiThi.getDanhSachMon() == null) {
            return 0.0;
        }
        
        // Lấy danh sách môn trong khối
        List<String> danhSachMon = khoiThi.getDanhSachMon();
        if (danhSachMon.isEmpty()) {
            return 0.0;
        }
        
        // Tính tổng điểm các môn trong khối
        double tongDiem = 0.0;
        int soMonCoDiem = 0;
        
        for (String maMon : danhSachMon) {
            DiemThi diemThi = diemThiService.getDiemThi(soBaoDanh.trim(), maMon);
            if (diemThi != null) {
                tongDiem += diemThi.getDiem();
                soMonCoDiem++;
            }
        }
        
        // Chỉ trả về tổng điểm nếu có đủ điểm tất cả môn trong khối
        if (soMonCoDiem == danhSachMon.size()) {
            return tongDiem;
        } else {
            return -1.0; // Chưa có đủ điểm tất cả môn
        }
    }
    
    /**
     * Lấy thông tin chi tiết điểm khối thi
     */
    public KhoiThiDiemInfo getKhoiThiDiemInfo(String soBaoDanh, String maKhoi) {
        if (soBaoDanh == null || soBaoDanh.trim().isEmpty() || 
            maKhoi == null || maKhoi.trim().isEmpty()) {
            return null;
        }
        
        KhoiThi khoiThi = khoiThiService.getKhoiThiByMaKhoi(maKhoi.trim());
        if (khoiThi == null) {
            return null;
        }
        
        List<String> danhSachMon = khoiThi.getDanhSachMon();
        List<DiemThi> diemKhoi = new ArrayList<>();
        double tongDiem = 0.0;
        int soMonCoDiem = 0;
        
        for (String maMon : danhSachMon) {
            DiemThi diemThi = diemThiService.getDiemThi(soBaoDanh.trim(), maMon);
            if (diemThi != null) {
                diemKhoi.add(diemThi);
                tongDiem += diemThi.getDiem();
                soMonCoDiem++;
            }
        }
        
        boolean dayDuDiem = (soMonCoDiem == danhSachMon.size());
        double diemTrungBinh = dayDuDiem ? tongDiem / danhSachMon.size() : 0.0;
        
        return new KhoiThiDiemInfo(
            khoiThi.getMaKhoi(),
            khoiThi.getTenKhoi(),
            danhSachMon,
            diemKhoi,
            tongDiem,
            diemTrungBinh,
            dayDuDiem
        );
    }
    
    /**
     * Tính thống kê điểm của thí sinh
     */
    public DiemThongKe tinhThongKeDiem(String soBaoDanh) {
        List<DiemThi> diemThis = getAllDiemThiByThiSinh(soBaoDanh);
        
        if (diemThis.isEmpty()) {
            return new DiemThongKe(0, 0.0, 0.0, 0.0);
        }
        
        double tongDiem = 0.0;
        double diemCao = diemThis.get(0).getDiem();
        double diemThap = diemThis.get(0).getDiem();
        
        for (DiemThi diemThi : diemThis) {
            double diem = diemThi.getDiem();
            tongDiem += diem;
            
            if (diem > diemCao) {
                diemCao = diem;
            }
            if (diem < diemThap) {
                diemThap = diem;
            }
        }
        
        double diemTrungBinh = tongDiem / diemThis.size();
        
        return new DiemThongKe(diemThis.size(), diemTrungBinh, diemCao, diemThap);
    }
    
    /**
     * Lấy danh sách khối thi có format để hiển thị
     */
    public String[] getKhoiThiDisplayList() {
        List<KhoiThi> khoiThis = khoiThiService.getAllKhoiThi();
        String[] result = new String[khoiThis.size()];
        
        for (int i = 0; i < khoiThis.size(); i++) {
            KhoiThi khoi = khoiThis.get(i);
            result[i] = khoi.getMaKhoi() + " - " + khoi.getTenKhoi();
        }
        
        return result;
    }
    
    /**
     * Kiểm tra thí sinh có tồn tại không
     */
    public boolean isThiSinhExists(String soBaoDanh) {
        return getThiSinhInfo(soBaoDanh) != null;
    }
    
    /**
     * Lấy danh sách khối thi mà thí sinh có thể đăng ký
     */
    public List<String> getAvailableKhoiThiForThiSinh(String soBaoDanh) {
        List<String> result = new ArrayList<>();
        String[] allKhoi = getKhoiThiDisplayList();
        
        for (String khoi : allKhoi) {
            result.add(khoi);
        }
        
        return result;
    }
    
    // Inner classes cho data transfer
    public static class DiemThiDetail {
        private String maMon;
        private String tenMon;
        private double diem;
        private java.util.Date ngayThi;
        private String xepLoai;
        
        public DiemThiDetail(String maMon, String tenMon, double diem, java.util.Date ngayThi, String xepLoai) {
            this.maMon = maMon;
            this.tenMon = tenMon;
            this.diem = diem;
            this.ngayThi = ngayThi;
            this.xepLoai = xepLoai;
        }
        
        // Getters
        public String getMaMon() { return maMon; }
        public String getTenMon() { return tenMon; }
        public double getDiem() { return diem; }
        public java.util.Date getNgayThi() { return ngayThi; }
        public String getXepLoai() { return xepLoai; }
    }
    
    public static class DiemThongKe {
        private int tongSoMon;
        private double diemTrungBinh;
        private double diemCao;
        private double diemThap;
        
        public DiemThongKe(int tongSoMon, double diemTrungBinh, double diemCao, double diemThap) {
            this.tongSoMon = tongSoMon;
            this.diemTrungBinh = diemTrungBinh;
            this.diemCao = diemCao;
            this.diemThap = diemThap;
        }
        
        // Getters
        public int getTongSoMon() { return tongSoMon; }
        public double getDiemTrungBinh() { return diemTrungBinh; }
        public double getDiemCao() { return diemCao; }
        public double getDiemThap() { return diemThap; }
    }
    
    public static class KhoiThiDiemInfo {
        private String maKhoi;
        private String tenKhoi;
        private List<String> danhSachMon;
        private List<DiemThi> diemKhoi;
        private double tongDiem;
        private double diemTrungBinh;
        private boolean dayDuDiem;
        
        public KhoiThiDiemInfo(String maKhoi, String tenKhoi, List<String> danhSachMon, 
                              List<DiemThi> diemKhoi, double tongDiem, double diemTrungBinh, boolean dayDuDiem) {
            this.maKhoi = maKhoi;
            this.tenKhoi = tenKhoi;
            this.danhSachMon = danhSachMon;
            this.diemKhoi = diemKhoi;
            this.tongDiem = tongDiem;
            this.diemTrungBinh = diemTrungBinh;
            this.dayDuDiem = dayDuDiem;
        }
        
        // Getters
        public String getMaKhoi() { return maKhoi; }
        public String getTenKhoi() { return tenKhoi; }
        public List<String> getDanhSachMon() { return danhSachMon; }
        public List<DiemThi> getDiemKhoi() { return diemKhoi; }
        public double getTongDiem() { return tongDiem; }
        public double getDiemTrungBinh() { return diemTrungBinh; }
        public boolean isDayDuDiem() { return dayDuDiem; }
    }
}
