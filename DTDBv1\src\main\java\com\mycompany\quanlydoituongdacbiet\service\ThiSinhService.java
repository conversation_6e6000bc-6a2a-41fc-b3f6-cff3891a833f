package com.mycompany.quanlydoituongdacbiet.service;

import com.mycompany.quanlydoituongdacbiet.dao.ThiSinhDAO;
import com.mycompany.quanlydoituongdacbiet.model.ThiSinh;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;

/**
 * Service class cho quản lý thí sinh
 */
public class ThiSinhService {
    private static ThiSinhService instance;
    private ThiSinhDAO thiSinhDAO;
    
    private ThiSinhService() {
        thiSinhDAO = new ThiSinhDAO();
    }
    
    public static ThiSinhService getInstance() {
        if (instance == null) {
            instance = new ThiSinhService();
        }
        return instance;
    }
    
    /**
     * Lấy tất cả thí sinh
     */
    public List<ThiSinh> getAllThiSinh() {
        return thiSinhDAO.getAllThiSinh();
    }
    
    /**
     * Thêm thí sinh mới
     */
    public boolean addThiSinh(ThiSinh thiSinh) {
        String validationError = validateThiSinh(thiSinh);
        if (validationError != null) {
            return false;
        }
        return thiSinhDAO.addThiSinh(thiSinh);
    }
    
    /**
     * Cập nhật thí sinh
     */
    public boolean updateThiSinh(ThiSinh thiSinh) {
        String validationError = validateThiSinh(thiSinh);
        if (validationError != null) {
            return false;
        }
        return thiSinhDAO.updateThiSinh(thiSinh);
    }
    
    /**
     * Xóa thí sinh
     */
    public boolean deleteThiSinh(String soBaoDanh) {
        if (soBaoDanh == null || soBaoDanh.trim().isEmpty()) {
            return false;
        }
        return thiSinhDAO.deleteThiSinh(soBaoDanh);
    }
    
    /**
     * Tìm thí sinh theo số báo danh
     */
    public ThiSinh findBySoBaoDanh(String soBaoDanh) {
        return thiSinhDAO.getThiSinhBySoBaoDanh(soBaoDanh);
    }
    
    /**
     * Tìm kiếm thí sinh theo từ khóa
     */
    public List<ThiSinh> searchThiSinh(String keyword) {
        List<ThiSinh> allThiSinh = getAllThiSinh();
        List<ThiSinh> result = new ArrayList<>();
        
        if (keyword == null || keyword.trim().isEmpty()) {
            return allThiSinh;
        }
        
        String searchTerm = keyword.toLowerCase().trim();
        
        for (ThiSinh ts : allThiSinh) {
            if (ts.getSoBaoDanh().toLowerCase().contains(searchTerm) ||
                ts.getHoTen().toLowerCase().contains(searchTerm) ||
                ts.getDiaChi().toLowerCase().contains(searchTerm) ||
                ts.getSoDienThoai().contains(searchTerm) ||
                (ts.getEmail() != null && ts.getEmail().toLowerCase().contains(searchTerm))) {
                result.add(ts);
            }
        }
        
        return result;
    }
    
    /**
     * Kiểm tra số báo danh đã tồn tại
     */
    public boolean isSoBaoDanhExists(String soBaoDanh) {
        return findBySoBaoDanh(soBaoDanh) != null;
    }
    
    /**
     * Validate thông tin thí sinh
     */
    public String validateThiSinh(ThiSinh thiSinh) {
        if (thiSinh == null) {
            return "Dữ liệu thí sinh không hợp lệ";
        }
        
        if (thiSinh.getSoBaoDanh() == null || thiSinh.getSoBaoDanh().trim().isEmpty()) {
            return "Số báo danh không được để trống";
        }
        
        if (thiSinh.getHoTen() == null || thiSinh.getHoTen().trim().isEmpty()) {
            return "Họ tên không được để trống";
        }
        
        if (thiSinh.getNgaySinh() == null) {
            return "Ngày sinh không được để trống";
        }
        
        // Kiểm tra ngày sinh hợp lệ (không quá 100 tuổi, không trong tương lai)
        Date now = new Date();
        Date minDate = new Date(now.getTime() - (100L * 365 * 24 * 60 * 60 * 1000)); // 100 năm trước
        
        if (thiSinh.getNgaySinh().after(now)) {
            return "Ngày sinh không thể trong tương lai";
        }
        
        if (thiSinh.getNgaySinh().before(minDate)) {
            return "Ngày sinh không hợp lệ";
        }
        
        if (thiSinh.getGioiTinh() == null || thiSinh.getGioiTinh().trim().isEmpty()) {
            return "Giới tính không được để trống";
        }
        
        if (thiSinh.getDiaChi() == null || thiSinh.getDiaChi().trim().isEmpty()) {
            return "Địa chỉ không được để trống";
        }
        
        // Validate số điện thoại
        if (thiSinh.getSoDienThoai() != null && !thiSinh.getSoDienThoai().trim().isEmpty()) {
            String sdt = thiSinh.getSoDienThoai().trim();
            if (!sdt.matches("^[0-9+\\-\\s()]{10,15}$")) {
                return "Số điện thoại không hợp lệ";
            }
        }
        
        // Validate email
        if (thiSinh.getEmail() != null && !thiSinh.getEmail().trim().isEmpty()) {
            String email = thiSinh.getEmail().trim();
            if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
                return "Email không hợp lệ";
            }
        }
        
        return null; // Hợp lệ
    }
    
    /**
     * Lấy danh sách khối thi
     */
    public String[] getAvailableKhoiThi() {
        // Có thể lấy từ database hoặc hardcode
        return new String[]{"A", "A1", "B", "C", "D", "D1"};
    }
}
