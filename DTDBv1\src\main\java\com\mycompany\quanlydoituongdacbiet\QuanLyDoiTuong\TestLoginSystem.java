package com.mycompany.quanlydoituongdacbiet.QuanLyDoiTuong;

import com.mycompany.quanlydoituongdacbiet.controller.AuthenticationController;
import com.mycompany.quanlydoituongdacbiet.dao.UserDAO;
import com.mycompany.quanlydoituongdacbiet.model.User;
import com.mycompany.quanlydoituongdacbiet.view.LoginView;

import java.util.List;
import java.util.Scanner;

/**
 * Test class cho hệ thống login
 * Chạy file này để test các chức năng đăng nhập
 */
public class TestLoginSystem {
    
    private static Scanner scanner = new Scanner(System.in);
    
    public static void main(String[] args) {
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                    TEST HỆ THỐNG LOGIN                      ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
        
        while (true) {
            showTestMenu();
            int choice = getChoice();
            
            switch (choice) {
                case 1:
                    testBasicLogin();
                    break;
                case 2:
                    testUserManagement();
                    break;
                case 3:
                    testValidation();
                    break;
                case 4:
                    testFileOperations();
                    break;
                case 5:
                    runFullLoginApp();
                    break;
                case 6:
                    testAllFunctions();
                    break;
                case 0:
                    System.out.println("Tạm biệt!");
                    return;
                default:
                    System.out.println("❌ Lựa chọn không hợp lệ!");
            }
            
            pauseScreen();
        }
    }
    
    /**
     * Hiển thị menu test
     */
    private static void showTestMenu() {
        clearScreen();
        System.out.println("┌──────────────────────────────────────────────────────────────┐");
        System.out.println("│                      MENU TEST LOGIN                        │");
        System.out.println("├──────────────────────────────────────────────────────────────┤");
        System.out.println("│  1. Test đăng nhập cơ bản                                   │");
        System.out.println("│  2. Test quản lý người dùng                                 │");
        System.out.println("│  3. Test validation                                          │");
        System.out.println("│  4. Test file operations                                     │");
        System.out.println("│  5. Chạy ứng dụng login đầy đủ                              │");
        System.out.println("│  6. Test tất cả chức năng                                   │");
        System.out.println("│  0. Thoát                                                    │");
        System.out.println("└──────────────────────────────────────────────────────────────┘");
        System.out.print("Nhập lựa chọn của bạn: ");
    }
    
    /**
     * Test đăng nhập cơ bản
     */
    private static void testBasicLogin() {
        clearScreen();
        System.out.println("=== TEST ĐĂNG NHẬP CƠ BẢN ===\n");
        
        AuthenticationController authController = new AuthenticationController();
        
        // Test 1: Đăng nhập thành công với admin
        System.out.println("1. Test đăng nhập admin:");
        boolean loginResult = authController.login("admin", "admin123");
        System.out.println("   Kết quả: " + (loginResult ? "✅ Thành công" : "❌ Thất bại"));
        
        if (loginResult) {
            User currentUser = authController.getCurrentUser();
            System.out.println("   User: " + currentUser.getFullName());
            System.out.println("   Role: " + currentUser.getRole());
            System.out.println("   Is Admin: " + authController.isCurrentUserAdmin());
        }
        
        // Test 2: Đăng nhập thất bại
        authController.logout();
        System.out.println("\n2. Test đăng nhập sai password:");
        boolean failLogin = authController.login("admin", "wrongpass");
        System.out.println("   Kết quả: " + (failLogin ? "❌ Lỗi: Cho phép đăng nhập sai" : "✅ Từ chối đúng"));
        
        // Test 3: Đăng nhập với user thường
        System.out.println("\n3. Test đăng nhập user thường:");
        boolean userLogin = authController.login("user1", "password123");
        System.out.println("   Kết quả: " + (userLogin ? "✅ Thành công" : "❌ Thất bại"));
        
        if (userLogin) {
            User currentUser = authController.getCurrentUser();
            System.out.println("   User: " + currentUser.getFullName());
            System.out.println("   Role: " + currentUser.getRole());
            System.out.println("   Is Admin: " + authController.isCurrentUserAdmin());
        }
        
        // Test 4: Logout
        System.out.println("\n4. Test logout:");
        authController.logout();
        System.out.println("   Logged in: " + authController.isLoggedIn());
        System.out.println("   Kết quả: " + (!authController.isLoggedIn() ? "✅ Logout thành công" : "❌ Logout thất bại"));
    }
    
    /**
     * Test quản lý người dùng
     */
    private static void testUserManagement() {
        clearScreen();
        System.out.println("=== TEST QUẢN LÝ NGƯỜI DÙNG ===\n");
        
        AuthenticationController authController = new AuthenticationController();
        
        // Đăng nhập admin để test
        authController.login("admin", "admin123");
        System.out.println("Đã đăng nhập admin để test quản lý user\n");
        
        // Test 1: Lấy danh sách users
        System.out.println("1. Danh sách users hiện tại:");
        List<User> users = authController.getAllUsers();
        for (User user : users) {
            System.out.println("   - " + user.getUsername() + " | " + user.getFullName() + " | " + user.getRole());
        }
        
        // Test 2: Thêm user mới
        System.out.println("\n2. Test thêm user mới:");
        boolean addResult = authController.registerUser("testuser2", "testpass", "Test User 2", "USER");
        System.out.println("   Kết quả: " + (addResult ? "✅ Thành công" : "❌ Thất bại"));
        
        // Test 3: Thêm user trùng username
        System.out.println("\n3. Test thêm user trùng username:");
        boolean duplicateResult = authController.registerUser("admin", "newpass", "New Admin", "ADMIN");
        System.out.println("   Kết quả: " + (!duplicateResult ? "✅ Từ chối đúng" : "❌ Lỗi: Cho phép trùng username"));
        
        // Test 4: Đổi mật khẩu
        System.out.println("\n4. Test đổi mật khẩu:");
        boolean changePassResult = authController.changePassword("admin123", "newpass123");
        System.out.println("   Kết quả: " + (changePassResult ? "✅ Thành công" : "❌ Thất bại"));
        
        // Test đăng nhập với mật khẩu mới
        authController.logout();
        boolean newPassLogin = authController.login("admin", "newpass123");
        System.out.println("   Login với pass mới: " + (newPassLogin ? "✅ Thành công" : "❌ Thất bại"));
        
        // Đổi lại mật khẩu cũ
        if (newPassLogin) {
            authController.changePassword("newpass123", "admin123");
            System.out.println("   Đã đổi lại mật khẩu cũ");
        }
    }
    
    /**
     * Test validation
     */
    private static void testValidation() {
        clearScreen();
        System.out.println("=== TEST VALIDATION ===\n");
        
        AuthenticationController authController = new AuthenticationController();
        
        // Test validation login
        System.out.println("1. Test validation login:");
        
        String[] testCases = {
            "", "password",           // Username trống
            "admin", "",              // Password trống
            "admin", "admin123"       // Hợp lệ
        };
        
        for (int i = 0; i < testCases.length; i += 2) {
            String username = testCases[i];
            String password = testCases[i + 1];
            String error = authController.validateLoginInput(username, password);
            
            System.out.println("   Username: '" + username + "', Password: '" + password + "'");
            System.out.println("   Kết quả: " + (error != null ? "❌ " + error : "✅ Hợp lệ"));
        }
        
        // Test validation registration
        System.out.println("\n2. Test validation registration:");
        
        String[][] regTestCases = {
            {"", "pass", "Name"},           // Username trống
            {"ab", "pass", "Name"},         // Username quá ngắn
            {"user", "", "Name"},           // Password trống
            {"user", "ab", "Name"},         // Password quá ngắn
            {"user", "pass", ""},           // Tên trống
            {"admin", "pass", "Name"},      // Username đã tồn tại
            {"newuser", "newpass", "New User"}  // Hợp lệ
        };
        
        for (String[] testCase : regTestCases) {
            String error = authController.validateRegistrationInput(testCase[0], testCase[1], testCase[2]);
            System.out.println("   User: '" + testCase[0] + "', Pass: '" + testCase[1] + "', Name: '" + testCase[2] + "'");
            System.out.println("   Kết quả: " + (error != null ? "❌ " + error : "✅ Hợp lệ"));
        }
    }
    
    /**
     * Test file operations
     */
    private static void testFileOperations() {
        clearScreen();
        System.out.println("=== TEST FILE OPERATIONS ===\n");
        
        UserDAO userDAO = new UserDAO();
        
        // Test 1: Kiểm tra file tồn tại
        System.out.println("1. Kiểm tra file users.txt:");
        try {
            java.io.File usersFile = new java.io.File("data/users.txt");
            System.out.println("   File tồn tại: " + (usersFile.exists() ? "✅ Có" : "❌ Không"));
            System.out.println("   Đường dẫn: " + usersFile.getAbsolutePath());
            System.out.println("   Kích thước: " + usersFile.length() + " bytes");
        } catch (Exception e) {
            System.out.println("   ❌ Lỗi: " + e.getMessage());
        }
        
        // Test 2: Đọc dữ liệu
        System.out.println("\n2. Đọc dữ liệu từ file:");
        List<User> users = userDAO.getAllUsers();
        System.out.println("   Số lượng users: " + users.size());
        for (User user : users) {
            System.out.println("   - " + user.toFileString());
        }
        
        // Test 3: Thêm và xóa user
        System.out.println("\n3. Test thêm/xóa user:");
        User testUser = new User("tempuser", "temppass", "Temp User", "USER");
        
        boolean addResult = userDAO.addUser(testUser);
        System.out.println("   Thêm user: " + (addResult ? "✅ Thành công" : "❌ Thất bại"));
        
        User foundUser = userDAO.findByUsername("tempuser");
        System.out.println("   Tìm user: " + (foundUser != null ? "✅ Tìm thấy" : "❌ Không tìm thấy"));
        
        boolean deleteResult = userDAO.deleteUser("tempuser");
        System.out.println("   Xóa user: " + (deleteResult ? "✅ Thành công" : "❌ Thất bại"));
        
        User deletedUser = userDAO.findByUsername("tempuser");
        System.out.println("   Kiểm tra sau xóa: " + (deletedUser == null ? "✅ Đã xóa" : "❌ Chưa xóa"));
    }
    
    /**
     * Chạy ứng dụng login đầy đủ
     */
    private static void runFullLoginApp() {
        clearScreen();
        System.out.println("=== CHẠY ỨNG DỤNG LOGIN ĐẦY ĐỦ ===\n");
        System.out.println("Đang khởi chạy ứng dụng login...\n");
        
        try {
            LoginView loginView = new LoginView();
            loginView.showLoginScreen();
        } catch (Exception e) {
            System.out.println("❌ Lỗi khi chạy ứng dụng: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Test tất cả chức năng
     */
    private static void testAllFunctions() {
        clearScreen();
        System.out.println("=== TEST TẤT CẢ CHỨC NĂNG ===\n");
        
        System.out.println("Đang chạy tất cả test cases...\n");
        
        testBasicLogin();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        testUserManagement();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        testValidation();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        testFileOperations();
        
        System.out.println("\n✅ Đã hoàn thành tất cả test cases!");
    }
    
    /**
     * Lấy lựa chọn từ người dùng
     */
    private static int getChoice() {
        try {
            String input = scanner.nextLine();
            return Integer.parseInt(input);
        } catch (NumberFormatException e) {
            return -1;
        }
    }
    
    /**
     * Xóa màn hình
     */
    private static void clearScreen() {
        System.out.print("\033[2J\033[H");
        System.out.flush();
    }
    
    /**
     * Tạm dừng màn hình
     */
    private static void pauseScreen() {
        System.out.print("\nNhấn Enter để tiếp tục...");
        scanner.nextLine();
    }
}
