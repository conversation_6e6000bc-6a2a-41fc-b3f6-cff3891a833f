<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="com.mycompany.quanlydoituongdacbiet.view.DiemThiManagementView">
  <grid id="27dc6" binding="mainPanel" layout-manager="BorderLayout" hgap="0" vgap="0">
    <constraints>
      <xy x="20" y="20" width="1000" height="700"/>
    </constraints>
    <properties>
      <background color="-1"/>
      <preferredSize width="1000" height="700"/>
    </properties>
    <border type="none"/>
    <children>
      <component id="a1b2c" class="javax.swing.JLabel" binding="titleLabel">
        <constraints border-constraint="North"/>
        <properties>
          <font name="Arial" size="24" style="1"/>
          <foreground color="-16744192"/>
          <horizontalAlignment value="0"/>
          <text value="QUẢN LÝ ĐIỂM THI"/>
        </properties>
      </component>
      <grid id="d3e4f" binding="formPanel" layout-manager="GridBagLayout">
        <constraints border-constraint="North"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="line" title="Thông tin điểm thi" title-color="-16744192"/>
        <children>
          <component id="g5h6i" class="javax.swing.JLabel">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Thí sinh:"/>
            </properties>
          </component>
          <component id="j7k8l" class="javax.swing.JComboBox" binding="cmbThiSinh">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="200" height="30"/>
              </grid>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="m9n0o" class="javax.swing.JLabel">
            <constraints>
              <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Môn thi:"/>
            </properties>
          </component>
          <component id="p1q2r" class="javax.swing.JComboBox" binding="cmbMonThi">
            <constraints>
              <grid row="0" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="200" height="30"/>
              </grid>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="s3t4u" class="javax.swing.JLabel">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Điểm:"/>
            </properties>
          </component>
          <component id="v5w6x" class="javax.swing.JTextField" binding="txtDiem">
            <constraints>
              <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="200" height="30"/>
              </grid>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
          <component id="y7z8a" class="javax.swing.JLabel">
            <constraints>
              <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
              <text value="Ngày thi:"/>
            </properties>
          </component>
          <component id="b9c0d" class="javax.swing.JTextField" binding="txtNgayThi">
            <constraints>
              <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                <preferred-size width="200" height="30"/>
              </grid>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <font name="Arial" size="14" style="0"/>
            </properties>
          </component>
        </children>
      </grid>
      <grid id="e1f2g" binding="statisticsPanel" layout-manager="FlowLayout" hgap="10" vgap="5" flow-align="0">
        <constraints border-constraint="North"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="line" title="Thống kê" title-color="-16744192"/>
        <children>
          <component id="h3i4j" class="javax.swing.JLabel" binding="lblTotalStudents">
            <constraints/>
            <properties>
              <font name="Arial" size="12" style="1"/>
              <foreground color="-16744192"/>
              <text value="Tổng số thí sinh: 0"/>
            </properties>
          </component>
          <component id="k5l6m" class="javax.swing.JLabel" binding="lblTotalScores">
            <constraints/>
            <properties>
              <font name="Arial" size="12" style="1"/>
              <foreground color="-16744192"/>
              <text value="Tổng số điểm: 0"/>
            </properties>
          </component>
          <component id="n7o8p" class="javax.swing.JLabel" binding="lblAverageScore">
            <constraints/>
            <properties>
              <font name="Arial" size="12" style="1"/>
              <foreground color="-16744192"/>
              <text value="Điểm trung bình: 0.0"/>
            </properties>
          </component>
        </children>
      </grid>
      <grid id="q9r0s" binding="tablePanel" layout-manager="BorderLayout" hgap="0" vgap="0">
        <constraints border-constraint="Center"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="line" title="Danh sách điểm thi" title-color="-16744192"/>
        <children>
          <grid id="t1u2v" binding="searchPanel" layout-manager="FlowLayout" hgap="5" vgap="5" flow-align="0">
            <constraints border-constraint="North"/>
            <properties>
              <background color="-1"/>
            </properties>
            <border type="none"/>
            <children>
              <component id="w3x4y" class="javax.swing.JLabel">
                <constraints/>
                <properties>
                  <font name="Arial" size="14" style="0"/>
                  <text value="Tìm kiếm:"/>
                </properties>
              </component>
              <component id="z5a6b" class="javax.swing.JTextField" binding="txtSearch">
                <constraints/>
                <properties>
                  <font name="Arial" size="14" style="0"/>
                  <preferredSize width="200" height="30"/>
                </properties>
              </component>
              <component id="c7d8e" class="javax.swing.JButton" binding="btnSearch">
                <constraints/>
                <properties>
                  <background color="-12566464"/>
                  <font name="Arial" size="12" style="1"/>
                  <foreground color="-1"/>
                  <preferredSize width="100" height="30"/>
                  <text value="Tìm kiếm"/>
                </properties>
              </component>
              <component id="f9g0h" class="javax.swing.JButton" binding="btnRefresh">
                <constraints/>
                <properties>
                  <background color="-7829368"/>
                  <font name="Arial" size="12" style="1"/>
                  <foreground color="-1"/>
                  <preferredSize width="100" height="30"/>
                  <text value="Làm mới"/>
                </properties>
              </component>
            </children>
          </grid>
          <scrollpane id="i1j2k" binding="scrollPane">
            <constraints border-constraint="Center"/>
            <properties/>
            <border type="none"/>
            <children>
              <component id="l3m4n" class="javax.swing.JTable" binding="table">
                <constraints/>
                <properties>
                  <font name="Arial" size="12" style="0"/>
                  <rowHeight value="25"/>
                </properties>
              </component>
            </children>
          </scrollpane>
        </children>
      </grid>
      <grid id="o5p6q" binding="buttonPanel" layout-manager="FlowLayout" hgap="10" vgap="10" flow-align="1">
        <constraints border-constraint="South"/>
        <properties>
          <background color="-1"/>
        </properties>
        <border type="none"/>
        <children>
          <component id="r7s8t" class="javax.swing.JButton" binding="btnAdd">
            <constraints/>
            <properties>
              <background color="-13408513"/>
              <font name="Arial" size="12" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="100" height="35"/>
              <text value="Thêm mới"/>
            </properties>
          </component>
          <component id="u9v0w" class="javax.swing.JButton" binding="btnEdit">
            <constraints/>
            <properties>
              <background color="-256"/>
              <font name="Arial" size="12" style="1"/>
              <foreground color="-16777216"/>
              <preferredSize width="100" height="35"/>
              <text value="Sửa"/>
            </properties>
          </component>
          <component id="x1y2z" class="javax.swing.JButton" binding="btnDelete">
            <constraints/>
            <properties>
              <background color="-3407617"/>
              <font name="Arial" size="12" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="100" height="35"/>
              <text value="Xóa"/>
            </properties>
          </component>
          <component id="a3b4c" class="javax.swing.JButton" binding="btnSave">
            <constraints/>
            <properties>
              <background color="-16744192"/>
              <font name="Arial" size="12" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="100" height="35"/>
              <text value="Lưu"/>
            </properties>
          </component>
          <component id="d5e6f" class="javax.swing.JButton" binding="btnCancel">
            <constraints/>
            <properties>
              <background color="-7829368"/>
              <font name="Arial" size="12" style="1"/>
              <foreground color="-1"/>
              <preferredSize width="100" height="35"/>
              <text value="Hủy"/>
            </properties>
          </component>
        </children>
      </grid>
    </children>
  </grid>
</form>
